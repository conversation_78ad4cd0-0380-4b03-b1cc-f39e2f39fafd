{"version": 3, "file": "forum.js", "sources": ["../node_modules/.pnpm/swiper@11.2.10/node_modules/swiper/shared/ssr-window.esm.mjs", "../node_modules/.pnpm/swiper@11.2.10/node_modules/swiper/shared/utils.mjs", "../node_modules/.pnpm/swiper@11.2.10/node_modules/swiper/shared/swiper-core.mjs", "../node_modules/.pnpm/swiper@11.2.10/node_modules/swiper/modules/autoplay.mjs", "../src/forum/utils/dom-utils.ts", "../src/common/config/constants.ts", "../src/forum/utils/mobile-detection.ts", "../src/common/config/defaults.ts", "../src/forum/components/tag-tiles-manager.ts", "../src/forum/utils/error-handler.ts", "../src/forum/utils/config-manager.ts", "../src/forum/index.ts"], "sourcesContent": ["/**\n * SSR Window 5.0.1\n * Better handling for window object in SSR environment\n * https://github.com/nolimits4web/ssr-window\n *\n * Copyright 2025, <PERSON>\n *\n * Licensed under MIT\n *\n * Released on: June 27, 2025\n */\n/* eslint-disable no-param-reassign */\nfunction isObject(obj) {\n  return obj !== null && typeof obj === 'object' && 'constructor' in obj && obj.constructor === Object;\n}\nfunction extend(target, src) {\n  if (target === void 0) {\n    target = {};\n  }\n  if (src === void 0) {\n    src = {};\n  }\n  const noExtend = ['__proto__', 'constructor', 'prototype'];\n  Object.keys(src).filter(key => noExtend.indexOf(key) < 0).forEach(key => {\n    if (typeof target[key] === 'undefined') target[key] = src[key];else if (isObject(src[key]) && isObject(target[key]) && Object.keys(src[key]).length > 0) {\n      extend(target[key], src[key]);\n    }\n  });\n}\nconst ssrDocument = {\n  body: {},\n  addEventListener() {},\n  removeEventListener() {},\n  activeElement: {\n    blur() {},\n    nodeName: ''\n  },\n  querySelector() {\n    return null;\n  },\n  querySelectorAll() {\n    return [];\n  },\n  getElementById() {\n    return null;\n  },\n  createEvent() {\n    return {\n      initEvent() {}\n    };\n  },\n  createElement() {\n    return {\n      children: [],\n      childNodes: [],\n      style: {},\n      setAttribute() {},\n      getElementsByTagName() {\n        return [];\n      }\n    };\n  },\n  createElementNS() {\n    return {};\n  },\n  importNode() {\n    return null;\n  },\n  location: {\n    hash: '',\n    host: '',\n    hostname: '',\n    href: '',\n    origin: '',\n    pathname: '',\n    protocol: '',\n    search: ''\n  }\n};\nfunction getDocument() {\n  const doc = typeof document !== 'undefined' ? document : {};\n  extend(doc, ssrDocument);\n  return doc;\n}\nconst ssrWindow = {\n  document: ssrDocument,\n  navigator: {\n    userAgent: ''\n  },\n  location: {\n    hash: '',\n    host: '',\n    hostname: '',\n    href: '',\n    origin: '',\n    pathname: '',\n    protocol: '',\n    search: ''\n  },\n  history: {\n    replaceState() {},\n    pushState() {},\n    go() {},\n    back() {}\n  },\n  CustomEvent: function CustomEvent() {\n    return this;\n  },\n  addEventListener() {},\n  removeEventListener() {},\n  getComputedStyle() {\n    return {\n      getPropertyValue() {\n        return '';\n      }\n    };\n  },\n  Image() {},\n  Date() {},\n  screen: {},\n  setTimeout() {},\n  clearTimeout() {},\n  matchMedia() {\n    return {};\n  },\n  requestAnimationFrame(callback) {\n    if (typeof setTimeout === 'undefined') {\n      callback();\n      return null;\n    }\n    return setTimeout(callback, 0);\n  },\n  cancelAnimationFrame(id) {\n    if (typeof setTimeout === 'undefined') {\n      return;\n    }\n    clearTimeout(id);\n  }\n};\nfunction getWindow() {\n  const win = typeof window !== 'undefined' ? window : {};\n  extend(win, ssrWindow);\n  return win;\n}\n\nexport { getWindow as a, getDocument as g };\n", "import { a as getWindow, g as getDocument } from './ssr-window.esm.mjs';\n\nfunction classesToTokens(classes) {\n  if (classes === void 0) {\n    classes = '';\n  }\n  return classes.trim().split(' ').filter(c => !!c.trim());\n}\n\nfunction deleteProps(obj) {\n  const object = obj;\n  Object.keys(object).forEach(key => {\n    try {\n      object[key] = null;\n    } catch (e) {\n      // no getter for object\n    }\n    try {\n      delete object[key];\n    } catch (e) {\n      // something got wrong\n    }\n  });\n}\nfunction nextTick(callback, delay) {\n  if (delay === void 0) {\n    delay = 0;\n  }\n  return setTimeout(callback, delay);\n}\nfunction now() {\n  return Date.now();\n}\nfunction getComputedStyle(el) {\n  const window = getWindow();\n  let style;\n  if (window.getComputedStyle) {\n    style = window.getComputedStyle(el, null);\n  }\n  if (!style && el.currentStyle) {\n    style = el.currentStyle;\n  }\n  if (!style) {\n    style = el.style;\n  }\n  return style;\n}\nfunction getTranslate(el, axis) {\n  if (axis === void 0) {\n    axis = 'x';\n  }\n  const window = getWindow();\n  let matrix;\n  let curTransform;\n  let transformMatrix;\n  const curStyle = getComputedStyle(el);\n  if (window.WebKitCSSMatrix) {\n    curTransform = curStyle.transform || curStyle.webkitTransform;\n    if (curTransform.split(',').length > 6) {\n      curTransform = curTransform.split(', ').map(a => a.replace(',', '.')).join(', ');\n    }\n    // Some old versions of Webkit choke when 'none' is passed; pass\n    // empty string instead in this case\n    transformMatrix = new window.WebKitCSSMatrix(curTransform === 'none' ? '' : curTransform);\n  } else {\n    transformMatrix = curStyle.MozTransform || curStyle.OTransform || curStyle.MsTransform || curStyle.msTransform || curStyle.transform || curStyle.getPropertyValue('transform').replace('translate(', 'matrix(1, 0, 0, 1,');\n    matrix = transformMatrix.toString().split(',');\n  }\n  if (axis === 'x') {\n    // Latest Chrome and webkits Fix\n    if (window.WebKitCSSMatrix) curTransform = transformMatrix.m41;\n    // Crazy IE10 Matrix\n    else if (matrix.length === 16) curTransform = parseFloat(matrix[12]);\n    // Normal Browsers\n    else curTransform = parseFloat(matrix[4]);\n  }\n  if (axis === 'y') {\n    // Latest Chrome and webkits Fix\n    if (window.WebKitCSSMatrix) curTransform = transformMatrix.m42;\n    // Crazy IE10 Matrix\n    else if (matrix.length === 16) curTransform = parseFloat(matrix[13]);\n    // Normal Browsers\n    else curTransform = parseFloat(matrix[5]);\n  }\n  return curTransform || 0;\n}\nfunction isObject(o) {\n  return typeof o === 'object' && o !== null && o.constructor && Object.prototype.toString.call(o).slice(8, -1) === 'Object';\n}\nfunction isNode(node) {\n  // eslint-disable-next-line\n  if (typeof window !== 'undefined' && typeof window.HTMLElement !== 'undefined') {\n    return node instanceof HTMLElement;\n  }\n  return node && (node.nodeType === 1 || node.nodeType === 11);\n}\nfunction extend() {\n  const to = Object(arguments.length <= 0 ? undefined : arguments[0]);\n  const noExtend = ['__proto__', 'constructor', 'prototype'];\n  for (let i = 1; i < arguments.length; i += 1) {\n    const nextSource = i < 0 || arguments.length <= i ? undefined : arguments[i];\n    if (nextSource !== undefined && nextSource !== null && !isNode(nextSource)) {\n      const keysArray = Object.keys(Object(nextSource)).filter(key => noExtend.indexOf(key) < 0);\n      for (let nextIndex = 0, len = keysArray.length; nextIndex < len; nextIndex += 1) {\n        const nextKey = keysArray[nextIndex];\n        const desc = Object.getOwnPropertyDescriptor(nextSource, nextKey);\n        if (desc !== undefined && desc.enumerable) {\n          if (isObject(to[nextKey]) && isObject(nextSource[nextKey])) {\n            if (nextSource[nextKey].__swiper__) {\n              to[nextKey] = nextSource[nextKey];\n            } else {\n              extend(to[nextKey], nextSource[nextKey]);\n            }\n          } else if (!isObject(to[nextKey]) && isObject(nextSource[nextKey])) {\n            to[nextKey] = {};\n            if (nextSource[nextKey].__swiper__) {\n              to[nextKey] = nextSource[nextKey];\n            } else {\n              extend(to[nextKey], nextSource[nextKey]);\n            }\n          } else {\n            to[nextKey] = nextSource[nextKey];\n          }\n        }\n      }\n    }\n  }\n  return to;\n}\nfunction setCSSProperty(el, varName, varValue) {\n  el.style.setProperty(varName, varValue);\n}\nfunction animateCSSModeScroll(_ref) {\n  let {\n    swiper,\n    targetPosition,\n    side\n  } = _ref;\n  const window = getWindow();\n  const startPosition = -swiper.translate;\n  let startTime = null;\n  let time;\n  const duration = swiper.params.speed;\n  swiper.wrapperEl.style.scrollSnapType = 'none';\n  window.cancelAnimationFrame(swiper.cssModeFrameID);\n  const dir = targetPosition > startPosition ? 'next' : 'prev';\n  const isOutOfBound = (current, target) => {\n    return dir === 'next' && current >= target || dir === 'prev' && current <= target;\n  };\n  const animate = () => {\n    time = new Date().getTime();\n    if (startTime === null) {\n      startTime = time;\n    }\n    const progress = Math.max(Math.min((time - startTime) / duration, 1), 0);\n    const easeProgress = 0.5 - Math.cos(progress * Math.PI) / 2;\n    let currentPosition = startPosition + easeProgress * (targetPosition - startPosition);\n    if (isOutOfBound(currentPosition, targetPosition)) {\n      currentPosition = targetPosition;\n    }\n    swiper.wrapperEl.scrollTo({\n      [side]: currentPosition\n    });\n    if (isOutOfBound(currentPosition, targetPosition)) {\n      swiper.wrapperEl.style.overflow = 'hidden';\n      swiper.wrapperEl.style.scrollSnapType = '';\n      setTimeout(() => {\n        swiper.wrapperEl.style.overflow = '';\n        swiper.wrapperEl.scrollTo({\n          [side]: currentPosition\n        });\n      });\n      window.cancelAnimationFrame(swiper.cssModeFrameID);\n      return;\n    }\n    swiper.cssModeFrameID = window.requestAnimationFrame(animate);\n  };\n  animate();\n}\nfunction getSlideTransformEl(slideEl) {\n  return slideEl.querySelector('.swiper-slide-transform') || slideEl.shadowRoot && slideEl.shadowRoot.querySelector('.swiper-slide-transform') || slideEl;\n}\nfunction elementChildren(element, selector) {\n  if (selector === void 0) {\n    selector = '';\n  }\n  const window = getWindow();\n  const children = [...element.children];\n  if (window.HTMLSlotElement && element instanceof HTMLSlotElement) {\n    children.push(...element.assignedElements());\n  }\n  if (!selector) {\n    return children;\n  }\n  return children.filter(el => el.matches(selector));\n}\nfunction elementIsChildOfSlot(el, slot) {\n  // Breadth-first search through all parent's children and assigned elements\n  const elementsQueue = [slot];\n  while (elementsQueue.length > 0) {\n    const elementToCheck = elementsQueue.shift();\n    if (el === elementToCheck) {\n      return true;\n    }\n    elementsQueue.push(...elementToCheck.children, ...(elementToCheck.shadowRoot ? elementToCheck.shadowRoot.children : []), ...(elementToCheck.assignedElements ? elementToCheck.assignedElements() : []));\n  }\n}\nfunction elementIsChildOf(el, parent) {\n  const window = getWindow();\n  let isChild = parent.contains(el);\n  if (!isChild && window.HTMLSlotElement && parent instanceof HTMLSlotElement) {\n    const children = [...parent.assignedElements()];\n    isChild = children.includes(el);\n    if (!isChild) {\n      isChild = elementIsChildOfSlot(el, parent);\n    }\n  }\n  return isChild;\n}\nfunction showWarning(text) {\n  try {\n    console.warn(text);\n    return;\n  } catch (err) {\n    // err\n  }\n}\nfunction createElement(tag, classes) {\n  if (classes === void 0) {\n    classes = [];\n  }\n  const el = document.createElement(tag);\n  el.classList.add(...(Array.isArray(classes) ? classes : classesToTokens(classes)));\n  return el;\n}\nfunction elementOffset(el) {\n  const window = getWindow();\n  const document = getDocument();\n  const box = el.getBoundingClientRect();\n  const body = document.body;\n  const clientTop = el.clientTop || body.clientTop || 0;\n  const clientLeft = el.clientLeft || body.clientLeft || 0;\n  const scrollTop = el === window ? window.scrollY : el.scrollTop;\n  const scrollLeft = el === window ? window.scrollX : el.scrollLeft;\n  return {\n    top: box.top + scrollTop - clientTop,\n    left: box.left + scrollLeft - clientLeft\n  };\n}\nfunction elementPrevAll(el, selector) {\n  const prevEls = [];\n  while (el.previousElementSibling) {\n    const prev = el.previousElementSibling; // eslint-disable-line\n    if (selector) {\n      if (prev.matches(selector)) prevEls.push(prev);\n    } else prevEls.push(prev);\n    el = prev;\n  }\n  return prevEls;\n}\nfunction elementNextAll(el, selector) {\n  const nextEls = [];\n  while (el.nextElementSibling) {\n    const next = el.nextElementSibling; // eslint-disable-line\n    if (selector) {\n      if (next.matches(selector)) nextEls.push(next);\n    } else nextEls.push(next);\n    el = next;\n  }\n  return nextEls;\n}\nfunction elementStyle(el, prop) {\n  const window = getWindow();\n  return window.getComputedStyle(el, null).getPropertyValue(prop);\n}\nfunction elementIndex(el) {\n  let child = el;\n  let i;\n  if (child) {\n    i = 0;\n    // eslint-disable-next-line\n    while ((child = child.previousSibling) !== null) {\n      if (child.nodeType === 1) i += 1;\n    }\n    return i;\n  }\n  return undefined;\n}\nfunction elementParents(el, selector) {\n  const parents = []; // eslint-disable-line\n  let parent = el.parentElement; // eslint-disable-line\n  while (parent) {\n    if (selector) {\n      if (parent.matches(selector)) parents.push(parent);\n    } else {\n      parents.push(parent);\n    }\n    parent = parent.parentElement;\n  }\n  return parents;\n}\nfunction elementTransitionEnd(el, callback) {\n  function fireCallBack(e) {\n    if (e.target !== el) return;\n    callback.call(el, e);\n    el.removeEventListener('transitionend', fireCallBack);\n  }\n  if (callback) {\n    el.addEventListener('transitionend', fireCallBack);\n  }\n}\nfunction elementOuterSize(el, size, includeMargins) {\n  const window = getWindow();\n  if (includeMargins) {\n    return el[size === 'width' ? 'offsetWidth' : 'offsetHeight'] + parseFloat(window.getComputedStyle(el, null).getPropertyValue(size === 'width' ? 'margin-right' : 'margin-top')) + parseFloat(window.getComputedStyle(el, null).getPropertyValue(size === 'width' ? 'margin-left' : 'margin-bottom'));\n  }\n  return el.offsetWidth;\n}\nfunction makeElementsArray(el) {\n  return (Array.isArray(el) ? el : [el]).filter(e => !!e);\n}\nfunction getRotateFix(swiper) {\n  return v => {\n    if (Math.abs(v) > 0 && swiper.browser && swiper.browser.need3dFix && Math.abs(v) % 90 === 0) {\n      return v + 0.001;\n    }\n    return v;\n  };\n}\nfunction setInnerHTML(el, html) {\n  if (html === void 0) {\n    html = '';\n  }\n  if (typeof trustedTypes !== 'undefined') {\n    el.innerHTML = trustedTypes.createPolicy('html', {\n      createHTML: s => s\n    }).createHTML(html);\n  } else {\n    el.innerHTML = html;\n  }\n}\n\nexport { setCSSProperty as a, elementParents as b, createElement as c, elementOffset as d, elementChildren as e, now as f, getSlideTransformEl as g, elementOuterSize as h, elementIndex as i, classesToTokens as j, getTranslate as k, elementTransitionEnd as l, makeElementsArray as m, nextTick as n, isObject as o, getRotateFix as p, elementStyle as q, elementNextAll as r, setInnerHTML as s, elementPrevAll as t, animateCSSModeScroll as u, showWarning as v, elementIsChildOf as w, extend as x, deleteProps as y };\n", "import { a as getWindow, g as getDocument } from './ssr-window.esm.mjs';\nimport { b as elementParents, q as elementStyle, e as elementChildren, a as setCSSProperty, h as elementOuterSize, r as elementNextAll, t as elementPrevAll, k as getTranslate, u as animateCSSModeScroll, n as nextTick, v as showWarning, c as createElement, w as elementIsChildOf, f as now, x as extend, i as elementIndex, y as deleteProps } from './utils.mjs';\n\nlet support;\nfunction calcSupport() {\n  const window = getWindow();\n  const document = getDocument();\n  return {\n    smoothScroll: document.documentElement && document.documentElement.style && 'scrollBehavior' in document.documentElement.style,\n    touch: !!('ontouchstart' in window || window.DocumentTouch && document instanceof window.DocumentTouch)\n  };\n}\nfunction getSupport() {\n  if (!support) {\n    support = calcSupport();\n  }\n  return support;\n}\n\nlet deviceCached;\nfunction calcDevice(_temp) {\n  let {\n    userAgent\n  } = _temp === void 0 ? {} : _temp;\n  const support = getSupport();\n  const window = getWindow();\n  const platform = window.navigator.platform;\n  const ua = userAgent || window.navigator.userAgent;\n  const device = {\n    ios: false,\n    android: false\n  };\n  const screenWidth = window.screen.width;\n  const screenHeight = window.screen.height;\n  const android = ua.match(/(Android);?[\\s\\/]+([\\d.]+)?/); // eslint-disable-line\n  let ipad = ua.match(/(iPad).*OS\\s([\\d_]+)/);\n  const ipod = ua.match(/(iPod)(.*OS\\s([\\d_]+))?/);\n  const iphone = !ipad && ua.match(/(iPhone\\sOS|iOS)\\s([\\d_]+)/);\n  const windows = platform === 'Win32';\n  let macos = platform === 'MacIntel';\n\n  // iPadOs 13 fix\n  const iPadScreens = ['1024x1366', '1366x1024', '834x1194', '1194x834', '834x1112', '1112x834', '768x1024', '1024x768', '820x1180', '1180x820', '810x1080', '1080x810'];\n  if (!ipad && macos && support.touch && iPadScreens.indexOf(`${screenWidth}x${screenHeight}`) >= 0) {\n    ipad = ua.match(/(Version)\\/([\\d.]+)/);\n    if (!ipad) ipad = [0, 1, '13_0_0'];\n    macos = false;\n  }\n\n  // Android\n  if (android && !windows) {\n    device.os = 'android';\n    device.android = true;\n  }\n  if (ipad || iphone || ipod) {\n    device.os = 'ios';\n    device.ios = true;\n  }\n\n  // Export object\n  return device;\n}\nfunction getDevice(overrides) {\n  if (overrides === void 0) {\n    overrides = {};\n  }\n  if (!deviceCached) {\n    deviceCached = calcDevice(overrides);\n  }\n  return deviceCached;\n}\n\nlet browser;\nfunction calcBrowser() {\n  const window = getWindow();\n  const device = getDevice();\n  let needPerspectiveFix = false;\n  function isSafari() {\n    const ua = window.navigator.userAgent.toLowerCase();\n    return ua.indexOf('safari') >= 0 && ua.indexOf('chrome') < 0 && ua.indexOf('android') < 0;\n  }\n  if (isSafari()) {\n    const ua = String(window.navigator.userAgent);\n    if (ua.includes('Version/')) {\n      const [major, minor] = ua.split('Version/')[1].split(' ')[0].split('.').map(num => Number(num));\n      needPerspectiveFix = major < 16 || major === 16 && minor < 2;\n    }\n  }\n  const isWebView = /(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(window.navigator.userAgent);\n  const isSafariBrowser = isSafari();\n  const need3dFix = isSafariBrowser || isWebView && device.ios;\n  return {\n    isSafari: needPerspectiveFix || isSafariBrowser,\n    needPerspectiveFix,\n    need3dFix,\n    isWebView\n  };\n}\nfunction getBrowser() {\n  if (!browser) {\n    browser = calcBrowser();\n  }\n  return browser;\n}\n\nfunction Resize(_ref) {\n  let {\n    swiper,\n    on,\n    emit\n  } = _ref;\n  const window = getWindow();\n  let observer = null;\n  let animationFrame = null;\n  const resizeHandler = () => {\n    if (!swiper || swiper.destroyed || !swiper.initialized) return;\n    emit('beforeResize');\n    emit('resize');\n  };\n  const createObserver = () => {\n    if (!swiper || swiper.destroyed || !swiper.initialized) return;\n    observer = new ResizeObserver(entries => {\n      animationFrame = window.requestAnimationFrame(() => {\n        const {\n          width,\n          height\n        } = swiper;\n        let newWidth = width;\n        let newHeight = height;\n        entries.forEach(_ref2 => {\n          let {\n            contentBoxSize,\n            contentRect,\n            target\n          } = _ref2;\n          if (target && target !== swiper.el) return;\n          newWidth = contentRect ? contentRect.width : (contentBoxSize[0] || contentBoxSize).inlineSize;\n          newHeight = contentRect ? contentRect.height : (contentBoxSize[0] || contentBoxSize).blockSize;\n        });\n        if (newWidth !== width || newHeight !== height) {\n          resizeHandler();\n        }\n      });\n    });\n    observer.observe(swiper.el);\n  };\n  const removeObserver = () => {\n    if (animationFrame) {\n      window.cancelAnimationFrame(animationFrame);\n    }\n    if (observer && observer.unobserve && swiper.el) {\n      observer.unobserve(swiper.el);\n      observer = null;\n    }\n  };\n  const orientationChangeHandler = () => {\n    if (!swiper || swiper.destroyed || !swiper.initialized) return;\n    emit('orientationchange');\n  };\n  on('init', () => {\n    if (swiper.params.resizeObserver && typeof window.ResizeObserver !== 'undefined') {\n      createObserver();\n      return;\n    }\n    window.addEventListener('resize', resizeHandler);\n    window.addEventListener('orientationchange', orientationChangeHandler);\n  });\n  on('destroy', () => {\n    removeObserver();\n    window.removeEventListener('resize', resizeHandler);\n    window.removeEventListener('orientationchange', orientationChangeHandler);\n  });\n}\n\nfunction Observer(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on,\n    emit\n  } = _ref;\n  const observers = [];\n  const window = getWindow();\n  const attach = function (target, options) {\n    if (options === void 0) {\n      options = {};\n    }\n    const ObserverFunc = window.MutationObserver || window.WebkitMutationObserver;\n    const observer = new ObserverFunc(mutations => {\n      // The observerUpdate event should only be triggered\n      // once despite the number of mutations.  Additional\n      // triggers are redundant and are very costly\n      if (swiper.__preventObserver__) return;\n      if (mutations.length === 1) {\n        emit('observerUpdate', mutations[0]);\n        return;\n      }\n      const observerUpdate = function observerUpdate() {\n        emit('observerUpdate', mutations[0]);\n      };\n      if (window.requestAnimationFrame) {\n        window.requestAnimationFrame(observerUpdate);\n      } else {\n        window.setTimeout(observerUpdate, 0);\n      }\n    });\n    observer.observe(target, {\n      attributes: typeof options.attributes === 'undefined' ? true : options.attributes,\n      childList: swiper.isElement || (typeof options.childList === 'undefined' ? true : options).childList,\n      characterData: typeof options.characterData === 'undefined' ? true : options.characterData\n    });\n    observers.push(observer);\n  };\n  const init = () => {\n    if (!swiper.params.observer) return;\n    if (swiper.params.observeParents) {\n      const containerParents = elementParents(swiper.hostEl);\n      for (let i = 0; i < containerParents.length; i += 1) {\n        attach(containerParents[i]);\n      }\n    }\n    // Observe container\n    attach(swiper.hostEl, {\n      childList: swiper.params.observeSlideChildren\n    });\n\n    // Observe wrapper\n    attach(swiper.wrapperEl, {\n      attributes: false\n    });\n  };\n  const destroy = () => {\n    observers.forEach(observer => {\n      observer.disconnect();\n    });\n    observers.splice(0, observers.length);\n  };\n  extendParams({\n    observer: false,\n    observeParents: false,\n    observeSlideChildren: false\n  });\n  on('init', init);\n  on('destroy', destroy);\n}\n\n/* eslint-disable no-underscore-dangle */\n\nvar eventsEmitter = {\n  on(events, handler, priority) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (typeof handler !== 'function') return self;\n    const method = priority ? 'unshift' : 'push';\n    events.split(' ').forEach(event => {\n      if (!self.eventsListeners[event]) self.eventsListeners[event] = [];\n      self.eventsListeners[event][method](handler);\n    });\n    return self;\n  },\n  once(events, handler, priority) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (typeof handler !== 'function') return self;\n    function onceHandler() {\n      self.off(events, onceHandler);\n      if (onceHandler.__emitterProxy) {\n        delete onceHandler.__emitterProxy;\n      }\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n      handler.apply(self, args);\n    }\n    onceHandler.__emitterProxy = handler;\n    return self.on(events, onceHandler, priority);\n  },\n  onAny(handler, priority) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (typeof handler !== 'function') return self;\n    const method = priority ? 'unshift' : 'push';\n    if (self.eventsAnyListeners.indexOf(handler) < 0) {\n      self.eventsAnyListeners[method](handler);\n    }\n    return self;\n  },\n  offAny(handler) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (!self.eventsAnyListeners) return self;\n    const index = self.eventsAnyListeners.indexOf(handler);\n    if (index >= 0) {\n      self.eventsAnyListeners.splice(index, 1);\n    }\n    return self;\n  },\n  off(events, handler) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (!self.eventsListeners) return self;\n    events.split(' ').forEach(event => {\n      if (typeof handler === 'undefined') {\n        self.eventsListeners[event] = [];\n      } else if (self.eventsListeners[event]) {\n        self.eventsListeners[event].forEach((eventHandler, index) => {\n          if (eventHandler === handler || eventHandler.__emitterProxy && eventHandler.__emitterProxy === handler) {\n            self.eventsListeners[event].splice(index, 1);\n          }\n        });\n      }\n    });\n    return self;\n  },\n  emit() {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (!self.eventsListeners) return self;\n    let events;\n    let data;\n    let context;\n    for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n      args[_key2] = arguments[_key2];\n    }\n    if (typeof args[0] === 'string' || Array.isArray(args[0])) {\n      events = args[0];\n      data = args.slice(1, args.length);\n      context = self;\n    } else {\n      events = args[0].events;\n      data = args[0].data;\n      context = args[0].context || self;\n    }\n    data.unshift(context);\n    const eventsArray = Array.isArray(events) ? events : events.split(' ');\n    eventsArray.forEach(event => {\n      if (self.eventsAnyListeners && self.eventsAnyListeners.length) {\n        self.eventsAnyListeners.forEach(eventHandler => {\n          eventHandler.apply(context, [event, ...data]);\n        });\n      }\n      if (self.eventsListeners && self.eventsListeners[event]) {\n        self.eventsListeners[event].forEach(eventHandler => {\n          eventHandler.apply(context, data);\n        });\n      }\n    });\n    return self;\n  }\n};\n\nfunction updateSize() {\n  const swiper = this;\n  let width;\n  let height;\n  const el = swiper.el;\n  if (typeof swiper.params.width !== 'undefined' && swiper.params.width !== null) {\n    width = swiper.params.width;\n  } else {\n    width = el.clientWidth;\n  }\n  if (typeof swiper.params.height !== 'undefined' && swiper.params.height !== null) {\n    height = swiper.params.height;\n  } else {\n    height = el.clientHeight;\n  }\n  if (width === 0 && swiper.isHorizontal() || height === 0 && swiper.isVertical()) {\n    return;\n  }\n\n  // Subtract paddings\n  width = width - parseInt(elementStyle(el, 'padding-left') || 0, 10) - parseInt(elementStyle(el, 'padding-right') || 0, 10);\n  height = height - parseInt(elementStyle(el, 'padding-top') || 0, 10) - parseInt(elementStyle(el, 'padding-bottom') || 0, 10);\n  if (Number.isNaN(width)) width = 0;\n  if (Number.isNaN(height)) height = 0;\n  Object.assign(swiper, {\n    width,\n    height,\n    size: swiper.isHorizontal() ? width : height\n  });\n}\n\nfunction updateSlides() {\n  const swiper = this;\n  function getDirectionPropertyValue(node, label) {\n    return parseFloat(node.getPropertyValue(swiper.getDirectionLabel(label)) || 0);\n  }\n  const params = swiper.params;\n  const {\n    wrapperEl,\n    slidesEl,\n    size: swiperSize,\n    rtlTranslate: rtl,\n    wrongRTL\n  } = swiper;\n  const isVirtual = swiper.virtual && params.virtual.enabled;\n  const previousSlidesLength = isVirtual ? swiper.virtual.slides.length : swiper.slides.length;\n  const slides = elementChildren(slidesEl, `.${swiper.params.slideClass}, swiper-slide`);\n  const slidesLength = isVirtual ? swiper.virtual.slides.length : slides.length;\n  let snapGrid = [];\n  const slidesGrid = [];\n  const slidesSizesGrid = [];\n  let offsetBefore = params.slidesOffsetBefore;\n  if (typeof offsetBefore === 'function') {\n    offsetBefore = params.slidesOffsetBefore.call(swiper);\n  }\n  let offsetAfter = params.slidesOffsetAfter;\n  if (typeof offsetAfter === 'function') {\n    offsetAfter = params.slidesOffsetAfter.call(swiper);\n  }\n  const previousSnapGridLength = swiper.snapGrid.length;\n  const previousSlidesGridLength = swiper.slidesGrid.length;\n  let spaceBetween = params.spaceBetween;\n  let slidePosition = -offsetBefore;\n  let prevSlideSize = 0;\n  let index = 0;\n  if (typeof swiperSize === 'undefined') {\n    return;\n  }\n  if (typeof spaceBetween === 'string' && spaceBetween.indexOf('%') >= 0) {\n    spaceBetween = parseFloat(spaceBetween.replace('%', '')) / 100 * swiperSize;\n  } else if (typeof spaceBetween === 'string') {\n    spaceBetween = parseFloat(spaceBetween);\n  }\n  swiper.virtualSize = -spaceBetween;\n\n  // reset margins\n  slides.forEach(slideEl => {\n    if (rtl) {\n      slideEl.style.marginLeft = '';\n    } else {\n      slideEl.style.marginRight = '';\n    }\n    slideEl.style.marginBottom = '';\n    slideEl.style.marginTop = '';\n  });\n\n  // reset cssMode offsets\n  if (params.centeredSlides && params.cssMode) {\n    setCSSProperty(wrapperEl, '--swiper-centered-offset-before', '');\n    setCSSProperty(wrapperEl, '--swiper-centered-offset-after', '');\n  }\n  const gridEnabled = params.grid && params.grid.rows > 1 && swiper.grid;\n  if (gridEnabled) {\n    swiper.grid.initSlides(slides);\n  } else if (swiper.grid) {\n    swiper.grid.unsetSlides();\n  }\n\n  // Calc slides\n  let slideSize;\n  const shouldResetSlideSize = params.slidesPerView === 'auto' && params.breakpoints && Object.keys(params.breakpoints).filter(key => {\n    return typeof params.breakpoints[key].slidesPerView !== 'undefined';\n  }).length > 0;\n  for (let i = 0; i < slidesLength; i += 1) {\n    slideSize = 0;\n    let slide;\n    if (slides[i]) slide = slides[i];\n    if (gridEnabled) {\n      swiper.grid.updateSlide(i, slide, slides);\n    }\n    if (slides[i] && elementStyle(slide, 'display') === 'none') continue; // eslint-disable-line\n\n    if (params.slidesPerView === 'auto') {\n      if (shouldResetSlideSize) {\n        slides[i].style[swiper.getDirectionLabel('width')] = ``;\n      }\n      const slideStyles = getComputedStyle(slide);\n      const currentTransform = slide.style.transform;\n      const currentWebKitTransform = slide.style.webkitTransform;\n      if (currentTransform) {\n        slide.style.transform = 'none';\n      }\n      if (currentWebKitTransform) {\n        slide.style.webkitTransform = 'none';\n      }\n      if (params.roundLengths) {\n        slideSize = swiper.isHorizontal() ? elementOuterSize(slide, 'width', true) : elementOuterSize(slide, 'height', true);\n      } else {\n        // eslint-disable-next-line\n        const width = getDirectionPropertyValue(slideStyles, 'width');\n        const paddingLeft = getDirectionPropertyValue(slideStyles, 'padding-left');\n        const paddingRight = getDirectionPropertyValue(slideStyles, 'padding-right');\n        const marginLeft = getDirectionPropertyValue(slideStyles, 'margin-left');\n        const marginRight = getDirectionPropertyValue(slideStyles, 'margin-right');\n        const boxSizing = slideStyles.getPropertyValue('box-sizing');\n        if (boxSizing && boxSizing === 'border-box') {\n          slideSize = width + marginLeft + marginRight;\n        } else {\n          const {\n            clientWidth,\n            offsetWidth\n          } = slide;\n          slideSize = width + paddingLeft + paddingRight + marginLeft + marginRight + (offsetWidth - clientWidth);\n        }\n      }\n      if (currentTransform) {\n        slide.style.transform = currentTransform;\n      }\n      if (currentWebKitTransform) {\n        slide.style.webkitTransform = currentWebKitTransform;\n      }\n      if (params.roundLengths) slideSize = Math.floor(slideSize);\n    } else {\n      slideSize = (swiperSize - (params.slidesPerView - 1) * spaceBetween) / params.slidesPerView;\n      if (params.roundLengths) slideSize = Math.floor(slideSize);\n      if (slides[i]) {\n        slides[i].style[swiper.getDirectionLabel('width')] = `${slideSize}px`;\n      }\n    }\n    if (slides[i]) {\n      slides[i].swiperSlideSize = slideSize;\n    }\n    slidesSizesGrid.push(slideSize);\n    if (params.centeredSlides) {\n      slidePosition = slidePosition + slideSize / 2 + prevSlideSize / 2 + spaceBetween;\n      if (prevSlideSize === 0 && i !== 0) slidePosition = slidePosition - swiperSize / 2 - spaceBetween;\n      if (i === 0) slidePosition = slidePosition - swiperSize / 2 - spaceBetween;\n      if (Math.abs(slidePosition) < 1 / 1000) slidePosition = 0;\n      if (params.roundLengths) slidePosition = Math.floor(slidePosition);\n      if (index % params.slidesPerGroup === 0) snapGrid.push(slidePosition);\n      slidesGrid.push(slidePosition);\n    } else {\n      if (params.roundLengths) slidePosition = Math.floor(slidePosition);\n      if ((index - Math.min(swiper.params.slidesPerGroupSkip, index)) % swiper.params.slidesPerGroup === 0) snapGrid.push(slidePosition);\n      slidesGrid.push(slidePosition);\n      slidePosition = slidePosition + slideSize + spaceBetween;\n    }\n    swiper.virtualSize += slideSize + spaceBetween;\n    prevSlideSize = slideSize;\n    index += 1;\n  }\n  swiper.virtualSize = Math.max(swiper.virtualSize, swiperSize) + offsetAfter;\n  if (rtl && wrongRTL && (params.effect === 'slide' || params.effect === 'coverflow')) {\n    wrapperEl.style.width = `${swiper.virtualSize + spaceBetween}px`;\n  }\n  if (params.setWrapperSize) {\n    wrapperEl.style[swiper.getDirectionLabel('width')] = `${swiper.virtualSize + spaceBetween}px`;\n  }\n  if (gridEnabled) {\n    swiper.grid.updateWrapperSize(slideSize, snapGrid);\n  }\n\n  // Remove last grid elements depending on width\n  if (!params.centeredSlides) {\n    const newSlidesGrid = [];\n    for (let i = 0; i < snapGrid.length; i += 1) {\n      let slidesGridItem = snapGrid[i];\n      if (params.roundLengths) slidesGridItem = Math.floor(slidesGridItem);\n      if (snapGrid[i] <= swiper.virtualSize - swiperSize) {\n        newSlidesGrid.push(slidesGridItem);\n      }\n    }\n    snapGrid = newSlidesGrid;\n    if (Math.floor(swiper.virtualSize - swiperSize) - Math.floor(snapGrid[snapGrid.length - 1]) > 1) {\n      snapGrid.push(swiper.virtualSize - swiperSize);\n    }\n  }\n  if (isVirtual && params.loop) {\n    const size = slidesSizesGrid[0] + spaceBetween;\n    if (params.slidesPerGroup > 1) {\n      const groups = Math.ceil((swiper.virtual.slidesBefore + swiper.virtual.slidesAfter) / params.slidesPerGroup);\n      const groupSize = size * params.slidesPerGroup;\n      for (let i = 0; i < groups; i += 1) {\n        snapGrid.push(snapGrid[snapGrid.length - 1] + groupSize);\n      }\n    }\n    for (let i = 0; i < swiper.virtual.slidesBefore + swiper.virtual.slidesAfter; i += 1) {\n      if (params.slidesPerGroup === 1) {\n        snapGrid.push(snapGrid[snapGrid.length - 1] + size);\n      }\n      slidesGrid.push(slidesGrid[slidesGrid.length - 1] + size);\n      swiper.virtualSize += size;\n    }\n  }\n  if (snapGrid.length === 0) snapGrid = [0];\n  if (spaceBetween !== 0) {\n    const key = swiper.isHorizontal() && rtl ? 'marginLeft' : swiper.getDirectionLabel('marginRight');\n    slides.filter((_, slideIndex) => {\n      if (!params.cssMode || params.loop) return true;\n      if (slideIndex === slides.length - 1) {\n        return false;\n      }\n      return true;\n    }).forEach(slideEl => {\n      slideEl.style[key] = `${spaceBetween}px`;\n    });\n  }\n  if (params.centeredSlides && params.centeredSlidesBounds) {\n    let allSlidesSize = 0;\n    slidesSizesGrid.forEach(slideSizeValue => {\n      allSlidesSize += slideSizeValue + (spaceBetween || 0);\n    });\n    allSlidesSize -= spaceBetween;\n    const maxSnap = allSlidesSize > swiperSize ? allSlidesSize - swiperSize : 0;\n    snapGrid = snapGrid.map(snap => {\n      if (snap <= 0) return -offsetBefore;\n      if (snap > maxSnap) return maxSnap + offsetAfter;\n      return snap;\n    });\n  }\n  if (params.centerInsufficientSlides) {\n    let allSlidesSize = 0;\n    slidesSizesGrid.forEach(slideSizeValue => {\n      allSlidesSize += slideSizeValue + (spaceBetween || 0);\n    });\n    allSlidesSize -= spaceBetween;\n    const offsetSize = (params.slidesOffsetBefore || 0) + (params.slidesOffsetAfter || 0);\n    if (allSlidesSize + offsetSize < swiperSize) {\n      const allSlidesOffset = (swiperSize - allSlidesSize - offsetSize) / 2;\n      snapGrid.forEach((snap, snapIndex) => {\n        snapGrid[snapIndex] = snap - allSlidesOffset;\n      });\n      slidesGrid.forEach((snap, snapIndex) => {\n        slidesGrid[snapIndex] = snap + allSlidesOffset;\n      });\n    }\n  }\n  Object.assign(swiper, {\n    slides,\n    snapGrid,\n    slidesGrid,\n    slidesSizesGrid\n  });\n  if (params.centeredSlides && params.cssMode && !params.centeredSlidesBounds) {\n    setCSSProperty(wrapperEl, '--swiper-centered-offset-before', `${-snapGrid[0]}px`);\n    setCSSProperty(wrapperEl, '--swiper-centered-offset-after', `${swiper.size / 2 - slidesSizesGrid[slidesSizesGrid.length - 1] / 2}px`);\n    const addToSnapGrid = -swiper.snapGrid[0];\n    const addToSlidesGrid = -swiper.slidesGrid[0];\n    swiper.snapGrid = swiper.snapGrid.map(v => v + addToSnapGrid);\n    swiper.slidesGrid = swiper.slidesGrid.map(v => v + addToSlidesGrid);\n  }\n  if (slidesLength !== previousSlidesLength) {\n    swiper.emit('slidesLengthChange');\n  }\n  if (snapGrid.length !== previousSnapGridLength) {\n    if (swiper.params.watchOverflow) swiper.checkOverflow();\n    swiper.emit('snapGridLengthChange');\n  }\n  if (slidesGrid.length !== previousSlidesGridLength) {\n    swiper.emit('slidesGridLengthChange');\n  }\n  if (params.watchSlidesProgress) {\n    swiper.updateSlidesOffset();\n  }\n  swiper.emit('slidesUpdated');\n  if (!isVirtual && !params.cssMode && (params.effect === 'slide' || params.effect === 'fade')) {\n    const backFaceHiddenClass = `${params.containerModifierClass}backface-hidden`;\n    const hasClassBackfaceClassAdded = swiper.el.classList.contains(backFaceHiddenClass);\n    if (slidesLength <= params.maxBackfaceHiddenSlides) {\n      if (!hasClassBackfaceClassAdded) swiper.el.classList.add(backFaceHiddenClass);\n    } else if (hasClassBackfaceClassAdded) {\n      swiper.el.classList.remove(backFaceHiddenClass);\n    }\n  }\n}\n\nfunction updateAutoHeight(speed) {\n  const swiper = this;\n  const activeSlides = [];\n  const isVirtual = swiper.virtual && swiper.params.virtual.enabled;\n  let newHeight = 0;\n  let i;\n  if (typeof speed === 'number') {\n    swiper.setTransition(speed);\n  } else if (speed === true) {\n    swiper.setTransition(swiper.params.speed);\n  }\n  const getSlideByIndex = index => {\n    if (isVirtual) {\n      return swiper.slides[swiper.getSlideIndexByData(index)];\n    }\n    return swiper.slides[index];\n  };\n  // Find slides currently in view\n  if (swiper.params.slidesPerView !== 'auto' && swiper.params.slidesPerView > 1) {\n    if (swiper.params.centeredSlides) {\n      (swiper.visibleSlides || []).forEach(slide => {\n        activeSlides.push(slide);\n      });\n    } else {\n      for (i = 0; i < Math.ceil(swiper.params.slidesPerView); i += 1) {\n        const index = swiper.activeIndex + i;\n        if (index > swiper.slides.length && !isVirtual) break;\n        activeSlides.push(getSlideByIndex(index));\n      }\n    }\n  } else {\n    activeSlides.push(getSlideByIndex(swiper.activeIndex));\n  }\n\n  // Find new height from highest slide in view\n  for (i = 0; i < activeSlides.length; i += 1) {\n    if (typeof activeSlides[i] !== 'undefined') {\n      const height = activeSlides[i].offsetHeight;\n      newHeight = height > newHeight ? height : newHeight;\n    }\n  }\n\n  // Update Height\n  if (newHeight || newHeight === 0) swiper.wrapperEl.style.height = `${newHeight}px`;\n}\n\nfunction updateSlidesOffset() {\n  const swiper = this;\n  const slides = swiper.slides;\n  // eslint-disable-next-line\n  const minusOffset = swiper.isElement ? swiper.isHorizontal() ? swiper.wrapperEl.offsetLeft : swiper.wrapperEl.offsetTop : 0;\n  for (let i = 0; i < slides.length; i += 1) {\n    slides[i].swiperSlideOffset = (swiper.isHorizontal() ? slides[i].offsetLeft : slides[i].offsetTop) - minusOffset - swiper.cssOverflowAdjustment();\n  }\n}\n\nconst toggleSlideClasses$1 = (slideEl, condition, className) => {\n  if (condition && !slideEl.classList.contains(className)) {\n    slideEl.classList.add(className);\n  } else if (!condition && slideEl.classList.contains(className)) {\n    slideEl.classList.remove(className);\n  }\n};\nfunction updateSlidesProgress(translate) {\n  if (translate === void 0) {\n    translate = this && this.translate || 0;\n  }\n  const swiper = this;\n  const params = swiper.params;\n  const {\n    slides,\n    rtlTranslate: rtl,\n    snapGrid\n  } = swiper;\n  if (slides.length === 0) return;\n  if (typeof slides[0].swiperSlideOffset === 'undefined') swiper.updateSlidesOffset();\n  let offsetCenter = -translate;\n  if (rtl) offsetCenter = translate;\n  swiper.visibleSlidesIndexes = [];\n  swiper.visibleSlides = [];\n  let spaceBetween = params.spaceBetween;\n  if (typeof spaceBetween === 'string' && spaceBetween.indexOf('%') >= 0) {\n    spaceBetween = parseFloat(spaceBetween.replace('%', '')) / 100 * swiper.size;\n  } else if (typeof spaceBetween === 'string') {\n    spaceBetween = parseFloat(spaceBetween);\n  }\n  for (let i = 0; i < slides.length; i += 1) {\n    const slide = slides[i];\n    let slideOffset = slide.swiperSlideOffset;\n    if (params.cssMode && params.centeredSlides) {\n      slideOffset -= slides[0].swiperSlideOffset;\n    }\n    const slideProgress = (offsetCenter + (params.centeredSlides ? swiper.minTranslate() : 0) - slideOffset) / (slide.swiperSlideSize + spaceBetween);\n    const originalSlideProgress = (offsetCenter - snapGrid[0] + (params.centeredSlides ? swiper.minTranslate() : 0) - slideOffset) / (slide.swiperSlideSize + spaceBetween);\n    const slideBefore = -(offsetCenter - slideOffset);\n    const slideAfter = slideBefore + swiper.slidesSizesGrid[i];\n    const isFullyVisible = slideBefore >= 0 && slideBefore <= swiper.size - swiper.slidesSizesGrid[i];\n    const isVisible = slideBefore >= 0 && slideBefore < swiper.size - 1 || slideAfter > 1 && slideAfter <= swiper.size || slideBefore <= 0 && slideAfter >= swiper.size;\n    if (isVisible) {\n      swiper.visibleSlides.push(slide);\n      swiper.visibleSlidesIndexes.push(i);\n    }\n    toggleSlideClasses$1(slide, isVisible, params.slideVisibleClass);\n    toggleSlideClasses$1(slide, isFullyVisible, params.slideFullyVisibleClass);\n    slide.progress = rtl ? -slideProgress : slideProgress;\n    slide.originalProgress = rtl ? -originalSlideProgress : originalSlideProgress;\n  }\n}\n\nfunction updateProgress(translate) {\n  const swiper = this;\n  if (typeof translate === 'undefined') {\n    const multiplier = swiper.rtlTranslate ? -1 : 1;\n    // eslint-disable-next-line\n    translate = swiper && swiper.translate && swiper.translate * multiplier || 0;\n  }\n  const params = swiper.params;\n  const translatesDiff = swiper.maxTranslate() - swiper.minTranslate();\n  let {\n    progress,\n    isBeginning,\n    isEnd,\n    progressLoop\n  } = swiper;\n  const wasBeginning = isBeginning;\n  const wasEnd = isEnd;\n  if (translatesDiff === 0) {\n    progress = 0;\n    isBeginning = true;\n    isEnd = true;\n  } else {\n    progress = (translate - swiper.minTranslate()) / translatesDiff;\n    const isBeginningRounded = Math.abs(translate - swiper.minTranslate()) < 1;\n    const isEndRounded = Math.abs(translate - swiper.maxTranslate()) < 1;\n    isBeginning = isBeginningRounded || progress <= 0;\n    isEnd = isEndRounded || progress >= 1;\n    if (isBeginningRounded) progress = 0;\n    if (isEndRounded) progress = 1;\n  }\n  if (params.loop) {\n    const firstSlideIndex = swiper.getSlideIndexByData(0);\n    const lastSlideIndex = swiper.getSlideIndexByData(swiper.slides.length - 1);\n    const firstSlideTranslate = swiper.slidesGrid[firstSlideIndex];\n    const lastSlideTranslate = swiper.slidesGrid[lastSlideIndex];\n    const translateMax = swiper.slidesGrid[swiper.slidesGrid.length - 1];\n    const translateAbs = Math.abs(translate);\n    if (translateAbs >= firstSlideTranslate) {\n      progressLoop = (translateAbs - firstSlideTranslate) / translateMax;\n    } else {\n      progressLoop = (translateAbs + translateMax - lastSlideTranslate) / translateMax;\n    }\n    if (progressLoop > 1) progressLoop -= 1;\n  }\n  Object.assign(swiper, {\n    progress,\n    progressLoop,\n    isBeginning,\n    isEnd\n  });\n  if (params.watchSlidesProgress || params.centeredSlides && params.autoHeight) swiper.updateSlidesProgress(translate);\n  if (isBeginning && !wasBeginning) {\n    swiper.emit('reachBeginning toEdge');\n  }\n  if (isEnd && !wasEnd) {\n    swiper.emit('reachEnd toEdge');\n  }\n  if (wasBeginning && !isBeginning || wasEnd && !isEnd) {\n    swiper.emit('fromEdge');\n  }\n  swiper.emit('progress', progress);\n}\n\nconst toggleSlideClasses = (slideEl, condition, className) => {\n  if (condition && !slideEl.classList.contains(className)) {\n    slideEl.classList.add(className);\n  } else if (!condition && slideEl.classList.contains(className)) {\n    slideEl.classList.remove(className);\n  }\n};\nfunction updateSlidesClasses() {\n  const swiper = this;\n  const {\n    slides,\n    params,\n    slidesEl,\n    activeIndex\n  } = swiper;\n  const isVirtual = swiper.virtual && params.virtual.enabled;\n  const gridEnabled = swiper.grid && params.grid && params.grid.rows > 1;\n  const getFilteredSlide = selector => {\n    return elementChildren(slidesEl, `.${params.slideClass}${selector}, swiper-slide${selector}`)[0];\n  };\n  let activeSlide;\n  let prevSlide;\n  let nextSlide;\n  if (isVirtual) {\n    if (params.loop) {\n      let slideIndex = activeIndex - swiper.virtual.slidesBefore;\n      if (slideIndex < 0) slideIndex = swiper.virtual.slides.length + slideIndex;\n      if (slideIndex >= swiper.virtual.slides.length) slideIndex -= swiper.virtual.slides.length;\n      activeSlide = getFilteredSlide(`[data-swiper-slide-index=\"${slideIndex}\"]`);\n    } else {\n      activeSlide = getFilteredSlide(`[data-swiper-slide-index=\"${activeIndex}\"]`);\n    }\n  } else {\n    if (gridEnabled) {\n      activeSlide = slides.find(slideEl => slideEl.column === activeIndex);\n      nextSlide = slides.find(slideEl => slideEl.column === activeIndex + 1);\n      prevSlide = slides.find(slideEl => slideEl.column === activeIndex - 1);\n    } else {\n      activeSlide = slides[activeIndex];\n    }\n  }\n  if (activeSlide) {\n    if (!gridEnabled) {\n      // Next Slide\n      nextSlide = elementNextAll(activeSlide, `.${params.slideClass}, swiper-slide`)[0];\n      if (params.loop && !nextSlide) {\n        nextSlide = slides[0];\n      }\n\n      // Prev Slide\n      prevSlide = elementPrevAll(activeSlide, `.${params.slideClass}, swiper-slide`)[0];\n      if (params.loop && !prevSlide === 0) {\n        prevSlide = slides[slides.length - 1];\n      }\n    }\n  }\n  slides.forEach(slideEl => {\n    toggleSlideClasses(slideEl, slideEl === activeSlide, params.slideActiveClass);\n    toggleSlideClasses(slideEl, slideEl === nextSlide, params.slideNextClass);\n    toggleSlideClasses(slideEl, slideEl === prevSlide, params.slidePrevClass);\n  });\n  swiper.emitSlidesClasses();\n}\n\nconst processLazyPreloader = (swiper, imageEl) => {\n  if (!swiper || swiper.destroyed || !swiper.params) return;\n  const slideSelector = () => swiper.isElement ? `swiper-slide` : `.${swiper.params.slideClass}`;\n  const slideEl = imageEl.closest(slideSelector());\n  if (slideEl) {\n    let lazyEl = slideEl.querySelector(`.${swiper.params.lazyPreloaderClass}`);\n    if (!lazyEl && swiper.isElement) {\n      if (slideEl.shadowRoot) {\n        lazyEl = slideEl.shadowRoot.querySelector(`.${swiper.params.lazyPreloaderClass}`);\n      } else {\n        // init later\n        requestAnimationFrame(() => {\n          if (slideEl.shadowRoot) {\n            lazyEl = slideEl.shadowRoot.querySelector(`.${swiper.params.lazyPreloaderClass}`);\n            if (lazyEl) lazyEl.remove();\n          }\n        });\n      }\n    }\n    if (lazyEl) lazyEl.remove();\n  }\n};\nconst unlazy = (swiper, index) => {\n  if (!swiper.slides[index]) return;\n  const imageEl = swiper.slides[index].querySelector('[loading=\"lazy\"]');\n  if (imageEl) imageEl.removeAttribute('loading');\n};\nconst preload = swiper => {\n  if (!swiper || swiper.destroyed || !swiper.params) return;\n  let amount = swiper.params.lazyPreloadPrevNext;\n  const len = swiper.slides.length;\n  if (!len || !amount || amount < 0) return;\n  amount = Math.min(amount, len);\n  const slidesPerView = swiper.params.slidesPerView === 'auto' ? swiper.slidesPerViewDynamic() : Math.ceil(swiper.params.slidesPerView);\n  const activeIndex = swiper.activeIndex;\n  if (swiper.params.grid && swiper.params.grid.rows > 1) {\n    const activeColumn = activeIndex;\n    const preloadColumns = [activeColumn - amount];\n    preloadColumns.push(...Array.from({\n      length: amount\n    }).map((_, i) => {\n      return activeColumn + slidesPerView + i;\n    }));\n    swiper.slides.forEach((slideEl, i) => {\n      if (preloadColumns.includes(slideEl.column)) unlazy(swiper, i);\n    });\n    return;\n  }\n  const slideIndexLastInView = activeIndex + slidesPerView - 1;\n  if (swiper.params.rewind || swiper.params.loop) {\n    for (let i = activeIndex - amount; i <= slideIndexLastInView + amount; i += 1) {\n      const realIndex = (i % len + len) % len;\n      if (realIndex < activeIndex || realIndex > slideIndexLastInView) unlazy(swiper, realIndex);\n    }\n  } else {\n    for (let i = Math.max(activeIndex - amount, 0); i <= Math.min(slideIndexLastInView + amount, len - 1); i += 1) {\n      if (i !== activeIndex && (i > slideIndexLastInView || i < activeIndex)) {\n        unlazy(swiper, i);\n      }\n    }\n  }\n};\n\nfunction getActiveIndexByTranslate(swiper) {\n  const {\n    slidesGrid,\n    params\n  } = swiper;\n  const translate = swiper.rtlTranslate ? swiper.translate : -swiper.translate;\n  let activeIndex;\n  for (let i = 0; i < slidesGrid.length; i += 1) {\n    if (typeof slidesGrid[i + 1] !== 'undefined') {\n      if (translate >= slidesGrid[i] && translate < slidesGrid[i + 1] - (slidesGrid[i + 1] - slidesGrid[i]) / 2) {\n        activeIndex = i;\n      } else if (translate >= slidesGrid[i] && translate < slidesGrid[i + 1]) {\n        activeIndex = i + 1;\n      }\n    } else if (translate >= slidesGrid[i]) {\n      activeIndex = i;\n    }\n  }\n  // Normalize slideIndex\n  if (params.normalizeSlideIndex) {\n    if (activeIndex < 0 || typeof activeIndex === 'undefined') activeIndex = 0;\n  }\n  return activeIndex;\n}\nfunction updateActiveIndex(newActiveIndex) {\n  const swiper = this;\n  const translate = swiper.rtlTranslate ? swiper.translate : -swiper.translate;\n  const {\n    snapGrid,\n    params,\n    activeIndex: previousIndex,\n    realIndex: previousRealIndex,\n    snapIndex: previousSnapIndex\n  } = swiper;\n  let activeIndex = newActiveIndex;\n  let snapIndex;\n  const getVirtualRealIndex = aIndex => {\n    let realIndex = aIndex - swiper.virtual.slidesBefore;\n    if (realIndex < 0) {\n      realIndex = swiper.virtual.slides.length + realIndex;\n    }\n    if (realIndex >= swiper.virtual.slides.length) {\n      realIndex -= swiper.virtual.slides.length;\n    }\n    return realIndex;\n  };\n  if (typeof activeIndex === 'undefined') {\n    activeIndex = getActiveIndexByTranslate(swiper);\n  }\n  if (snapGrid.indexOf(translate) >= 0) {\n    snapIndex = snapGrid.indexOf(translate);\n  } else {\n    const skip = Math.min(params.slidesPerGroupSkip, activeIndex);\n    snapIndex = skip + Math.floor((activeIndex - skip) / params.slidesPerGroup);\n  }\n  if (snapIndex >= snapGrid.length) snapIndex = snapGrid.length - 1;\n  if (activeIndex === previousIndex && !swiper.params.loop) {\n    if (snapIndex !== previousSnapIndex) {\n      swiper.snapIndex = snapIndex;\n      swiper.emit('snapIndexChange');\n    }\n    return;\n  }\n  if (activeIndex === previousIndex && swiper.params.loop && swiper.virtual && swiper.params.virtual.enabled) {\n    swiper.realIndex = getVirtualRealIndex(activeIndex);\n    return;\n  }\n  const gridEnabled = swiper.grid && params.grid && params.grid.rows > 1;\n\n  // Get real index\n  let realIndex;\n  if (swiper.virtual && params.virtual.enabled && params.loop) {\n    realIndex = getVirtualRealIndex(activeIndex);\n  } else if (gridEnabled) {\n    const firstSlideInColumn = swiper.slides.find(slideEl => slideEl.column === activeIndex);\n    let activeSlideIndex = parseInt(firstSlideInColumn.getAttribute('data-swiper-slide-index'), 10);\n    if (Number.isNaN(activeSlideIndex)) {\n      activeSlideIndex = Math.max(swiper.slides.indexOf(firstSlideInColumn), 0);\n    }\n    realIndex = Math.floor(activeSlideIndex / params.grid.rows);\n  } else if (swiper.slides[activeIndex]) {\n    const slideIndex = swiper.slides[activeIndex].getAttribute('data-swiper-slide-index');\n    if (slideIndex) {\n      realIndex = parseInt(slideIndex, 10);\n    } else {\n      realIndex = activeIndex;\n    }\n  } else {\n    realIndex = activeIndex;\n  }\n  Object.assign(swiper, {\n    previousSnapIndex,\n    snapIndex,\n    previousRealIndex,\n    realIndex,\n    previousIndex,\n    activeIndex\n  });\n  if (swiper.initialized) {\n    preload(swiper);\n  }\n  swiper.emit('activeIndexChange');\n  swiper.emit('snapIndexChange');\n  if (swiper.initialized || swiper.params.runCallbacksOnInit) {\n    if (previousRealIndex !== realIndex) {\n      swiper.emit('realIndexChange');\n    }\n    swiper.emit('slideChange');\n  }\n}\n\nfunction updateClickedSlide(el, path) {\n  const swiper = this;\n  const params = swiper.params;\n  let slide = el.closest(`.${params.slideClass}, swiper-slide`);\n  if (!slide && swiper.isElement && path && path.length > 1 && path.includes(el)) {\n    [...path.slice(path.indexOf(el) + 1, path.length)].forEach(pathEl => {\n      if (!slide && pathEl.matches && pathEl.matches(`.${params.slideClass}, swiper-slide`)) {\n        slide = pathEl;\n      }\n    });\n  }\n  let slideFound = false;\n  let slideIndex;\n  if (slide) {\n    for (let i = 0; i < swiper.slides.length; i += 1) {\n      if (swiper.slides[i] === slide) {\n        slideFound = true;\n        slideIndex = i;\n        break;\n      }\n    }\n  }\n  if (slide && slideFound) {\n    swiper.clickedSlide = slide;\n    if (swiper.virtual && swiper.params.virtual.enabled) {\n      swiper.clickedIndex = parseInt(slide.getAttribute('data-swiper-slide-index'), 10);\n    } else {\n      swiper.clickedIndex = slideIndex;\n    }\n  } else {\n    swiper.clickedSlide = undefined;\n    swiper.clickedIndex = undefined;\n    return;\n  }\n  if (params.slideToClickedSlide && swiper.clickedIndex !== undefined && swiper.clickedIndex !== swiper.activeIndex) {\n    swiper.slideToClickedSlide();\n  }\n}\n\nvar update = {\n  updateSize,\n  updateSlides,\n  updateAutoHeight,\n  updateSlidesOffset,\n  updateSlidesProgress,\n  updateProgress,\n  updateSlidesClasses,\n  updateActiveIndex,\n  updateClickedSlide\n};\n\nfunction getSwiperTranslate(axis) {\n  if (axis === void 0) {\n    axis = this.isHorizontal() ? 'x' : 'y';\n  }\n  const swiper = this;\n  const {\n    params,\n    rtlTranslate: rtl,\n    translate,\n    wrapperEl\n  } = swiper;\n  if (params.virtualTranslate) {\n    return rtl ? -translate : translate;\n  }\n  if (params.cssMode) {\n    return translate;\n  }\n  let currentTranslate = getTranslate(wrapperEl, axis);\n  currentTranslate += swiper.cssOverflowAdjustment();\n  if (rtl) currentTranslate = -currentTranslate;\n  return currentTranslate || 0;\n}\n\nfunction setTranslate(translate, byController) {\n  const swiper = this;\n  const {\n    rtlTranslate: rtl,\n    params,\n    wrapperEl,\n    progress\n  } = swiper;\n  let x = 0;\n  let y = 0;\n  const z = 0;\n  if (swiper.isHorizontal()) {\n    x = rtl ? -translate : translate;\n  } else {\n    y = translate;\n  }\n  if (params.roundLengths) {\n    x = Math.floor(x);\n    y = Math.floor(y);\n  }\n  swiper.previousTranslate = swiper.translate;\n  swiper.translate = swiper.isHorizontal() ? x : y;\n  if (params.cssMode) {\n    wrapperEl[swiper.isHorizontal() ? 'scrollLeft' : 'scrollTop'] = swiper.isHorizontal() ? -x : -y;\n  } else if (!params.virtualTranslate) {\n    if (swiper.isHorizontal()) {\n      x -= swiper.cssOverflowAdjustment();\n    } else {\n      y -= swiper.cssOverflowAdjustment();\n    }\n    wrapperEl.style.transform = `translate3d(${x}px, ${y}px, ${z}px)`;\n  }\n\n  // Check if we need to update progress\n  let newProgress;\n  const translatesDiff = swiper.maxTranslate() - swiper.minTranslate();\n  if (translatesDiff === 0) {\n    newProgress = 0;\n  } else {\n    newProgress = (translate - swiper.minTranslate()) / translatesDiff;\n  }\n  if (newProgress !== progress) {\n    swiper.updateProgress(translate);\n  }\n  swiper.emit('setTranslate', swiper.translate, byController);\n}\n\nfunction minTranslate() {\n  return -this.snapGrid[0];\n}\n\nfunction maxTranslate() {\n  return -this.snapGrid[this.snapGrid.length - 1];\n}\n\nfunction translateTo(translate, speed, runCallbacks, translateBounds, internal) {\n  if (translate === void 0) {\n    translate = 0;\n  }\n  if (speed === void 0) {\n    speed = this.params.speed;\n  }\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  if (translateBounds === void 0) {\n    translateBounds = true;\n  }\n  const swiper = this;\n  const {\n    params,\n    wrapperEl\n  } = swiper;\n  if (swiper.animating && params.preventInteractionOnTransition) {\n    return false;\n  }\n  const minTranslate = swiper.minTranslate();\n  const maxTranslate = swiper.maxTranslate();\n  let newTranslate;\n  if (translateBounds && translate > minTranslate) newTranslate = minTranslate;else if (translateBounds && translate < maxTranslate) newTranslate = maxTranslate;else newTranslate = translate;\n\n  // Update progress\n  swiper.updateProgress(newTranslate);\n  if (params.cssMode) {\n    const isH = swiper.isHorizontal();\n    if (speed === 0) {\n      wrapperEl[isH ? 'scrollLeft' : 'scrollTop'] = -newTranslate;\n    } else {\n      if (!swiper.support.smoothScroll) {\n        animateCSSModeScroll({\n          swiper,\n          targetPosition: -newTranslate,\n          side: isH ? 'left' : 'top'\n        });\n        return true;\n      }\n      wrapperEl.scrollTo({\n        [isH ? 'left' : 'top']: -newTranslate,\n        behavior: 'smooth'\n      });\n    }\n    return true;\n  }\n  if (speed === 0) {\n    swiper.setTransition(0);\n    swiper.setTranslate(newTranslate);\n    if (runCallbacks) {\n      swiper.emit('beforeTransitionStart', speed, internal);\n      swiper.emit('transitionEnd');\n    }\n  } else {\n    swiper.setTransition(speed);\n    swiper.setTranslate(newTranslate);\n    if (runCallbacks) {\n      swiper.emit('beforeTransitionStart', speed, internal);\n      swiper.emit('transitionStart');\n    }\n    if (!swiper.animating) {\n      swiper.animating = true;\n      if (!swiper.onTranslateToWrapperTransitionEnd) {\n        swiper.onTranslateToWrapperTransitionEnd = function transitionEnd(e) {\n          if (!swiper || swiper.destroyed) return;\n          if (e.target !== this) return;\n          swiper.wrapperEl.removeEventListener('transitionend', swiper.onTranslateToWrapperTransitionEnd);\n          swiper.onTranslateToWrapperTransitionEnd = null;\n          delete swiper.onTranslateToWrapperTransitionEnd;\n          swiper.animating = false;\n          if (runCallbacks) {\n            swiper.emit('transitionEnd');\n          }\n        };\n      }\n      swiper.wrapperEl.addEventListener('transitionend', swiper.onTranslateToWrapperTransitionEnd);\n    }\n  }\n  return true;\n}\n\nvar translate = {\n  getTranslate: getSwiperTranslate,\n  setTranslate,\n  minTranslate,\n  maxTranslate,\n  translateTo\n};\n\nfunction setTransition(duration, byController) {\n  const swiper = this;\n  if (!swiper.params.cssMode) {\n    swiper.wrapperEl.style.transitionDuration = `${duration}ms`;\n    swiper.wrapperEl.style.transitionDelay = duration === 0 ? `0ms` : '';\n  }\n  swiper.emit('setTransition', duration, byController);\n}\n\nfunction transitionEmit(_ref) {\n  let {\n    swiper,\n    runCallbacks,\n    direction,\n    step\n  } = _ref;\n  const {\n    activeIndex,\n    previousIndex\n  } = swiper;\n  let dir = direction;\n  if (!dir) {\n    if (activeIndex > previousIndex) dir = 'next';else if (activeIndex < previousIndex) dir = 'prev';else dir = 'reset';\n  }\n  swiper.emit(`transition${step}`);\n  if (runCallbacks && dir === 'reset') {\n    swiper.emit(`slideResetTransition${step}`);\n  } else if (runCallbacks && activeIndex !== previousIndex) {\n    swiper.emit(`slideChangeTransition${step}`);\n    if (dir === 'next') {\n      swiper.emit(`slideNextTransition${step}`);\n    } else {\n      swiper.emit(`slidePrevTransition${step}`);\n    }\n  }\n}\n\nfunction transitionStart(runCallbacks, direction) {\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  const swiper = this;\n  const {\n    params\n  } = swiper;\n  if (params.cssMode) return;\n  if (params.autoHeight) {\n    swiper.updateAutoHeight();\n  }\n  transitionEmit({\n    swiper,\n    runCallbacks,\n    direction,\n    step: 'Start'\n  });\n}\n\nfunction transitionEnd(runCallbacks, direction) {\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  const swiper = this;\n  const {\n    params\n  } = swiper;\n  swiper.animating = false;\n  if (params.cssMode) return;\n  swiper.setTransition(0);\n  transitionEmit({\n    swiper,\n    runCallbacks,\n    direction,\n    step: 'End'\n  });\n}\n\nvar transition = {\n  setTransition,\n  transitionStart,\n  transitionEnd\n};\n\nfunction slideTo(index, speed, runCallbacks, internal, initial) {\n  if (index === void 0) {\n    index = 0;\n  }\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  if (typeof index === 'string') {\n    index = parseInt(index, 10);\n  }\n  const swiper = this;\n  let slideIndex = index;\n  if (slideIndex < 0) slideIndex = 0;\n  const {\n    params,\n    snapGrid,\n    slidesGrid,\n    previousIndex,\n    activeIndex,\n    rtlTranslate: rtl,\n    wrapperEl,\n    enabled\n  } = swiper;\n  if (!enabled && !internal && !initial || swiper.destroyed || swiper.animating && params.preventInteractionOnTransition) {\n    return false;\n  }\n  if (typeof speed === 'undefined') {\n    speed = swiper.params.speed;\n  }\n  const skip = Math.min(swiper.params.slidesPerGroupSkip, slideIndex);\n  let snapIndex = skip + Math.floor((slideIndex - skip) / swiper.params.slidesPerGroup);\n  if (snapIndex >= snapGrid.length) snapIndex = snapGrid.length - 1;\n  const translate = -snapGrid[snapIndex];\n  // Normalize slideIndex\n  if (params.normalizeSlideIndex) {\n    for (let i = 0; i < slidesGrid.length; i += 1) {\n      const normalizedTranslate = -Math.floor(translate * 100);\n      const normalizedGrid = Math.floor(slidesGrid[i] * 100);\n      const normalizedGridNext = Math.floor(slidesGrid[i + 1] * 100);\n      if (typeof slidesGrid[i + 1] !== 'undefined') {\n        if (normalizedTranslate >= normalizedGrid && normalizedTranslate < normalizedGridNext - (normalizedGridNext - normalizedGrid) / 2) {\n          slideIndex = i;\n        } else if (normalizedTranslate >= normalizedGrid && normalizedTranslate < normalizedGridNext) {\n          slideIndex = i + 1;\n        }\n      } else if (normalizedTranslate >= normalizedGrid) {\n        slideIndex = i;\n      }\n    }\n  }\n  // Directions locks\n  if (swiper.initialized && slideIndex !== activeIndex) {\n    if (!swiper.allowSlideNext && (rtl ? translate > swiper.translate && translate > swiper.minTranslate() : translate < swiper.translate && translate < swiper.minTranslate())) {\n      return false;\n    }\n    if (!swiper.allowSlidePrev && translate > swiper.translate && translate > swiper.maxTranslate()) {\n      if ((activeIndex || 0) !== slideIndex) {\n        return false;\n      }\n    }\n  }\n  if (slideIndex !== (previousIndex || 0) && runCallbacks) {\n    swiper.emit('beforeSlideChangeStart');\n  }\n\n  // Update progress\n  swiper.updateProgress(translate);\n  let direction;\n  if (slideIndex > activeIndex) direction = 'next';else if (slideIndex < activeIndex) direction = 'prev';else direction = 'reset';\n\n  // initial virtual\n  const isVirtual = swiper.virtual && swiper.params.virtual.enabled;\n  const isInitialVirtual = isVirtual && initial;\n  // Update Index\n  if (!isInitialVirtual && (rtl && -translate === swiper.translate || !rtl && translate === swiper.translate)) {\n    swiper.updateActiveIndex(slideIndex);\n    // Update Height\n    if (params.autoHeight) {\n      swiper.updateAutoHeight();\n    }\n    swiper.updateSlidesClasses();\n    if (params.effect !== 'slide') {\n      swiper.setTranslate(translate);\n    }\n    if (direction !== 'reset') {\n      swiper.transitionStart(runCallbacks, direction);\n      swiper.transitionEnd(runCallbacks, direction);\n    }\n    return false;\n  }\n  if (params.cssMode) {\n    const isH = swiper.isHorizontal();\n    const t = rtl ? translate : -translate;\n    if (speed === 0) {\n      if (isVirtual) {\n        swiper.wrapperEl.style.scrollSnapType = 'none';\n        swiper._immediateVirtual = true;\n      }\n      if (isVirtual && !swiper._cssModeVirtualInitialSet && swiper.params.initialSlide > 0) {\n        swiper._cssModeVirtualInitialSet = true;\n        requestAnimationFrame(() => {\n          wrapperEl[isH ? 'scrollLeft' : 'scrollTop'] = t;\n        });\n      } else {\n        wrapperEl[isH ? 'scrollLeft' : 'scrollTop'] = t;\n      }\n      if (isVirtual) {\n        requestAnimationFrame(() => {\n          swiper.wrapperEl.style.scrollSnapType = '';\n          swiper._immediateVirtual = false;\n        });\n      }\n    } else {\n      if (!swiper.support.smoothScroll) {\n        animateCSSModeScroll({\n          swiper,\n          targetPosition: t,\n          side: isH ? 'left' : 'top'\n        });\n        return true;\n      }\n      wrapperEl.scrollTo({\n        [isH ? 'left' : 'top']: t,\n        behavior: 'smooth'\n      });\n    }\n    return true;\n  }\n  const browser = getBrowser();\n  const isSafari = browser.isSafari;\n  if (isVirtual && !initial && isSafari && swiper.isElement) {\n    swiper.virtual.update(false, false, slideIndex);\n  }\n  swiper.setTransition(speed);\n  swiper.setTranslate(translate);\n  swiper.updateActiveIndex(slideIndex);\n  swiper.updateSlidesClasses();\n  swiper.emit('beforeTransitionStart', speed, internal);\n  swiper.transitionStart(runCallbacks, direction);\n  if (speed === 0) {\n    swiper.transitionEnd(runCallbacks, direction);\n  } else if (!swiper.animating) {\n    swiper.animating = true;\n    if (!swiper.onSlideToWrapperTransitionEnd) {\n      swiper.onSlideToWrapperTransitionEnd = function transitionEnd(e) {\n        if (!swiper || swiper.destroyed) return;\n        if (e.target !== this) return;\n        swiper.wrapperEl.removeEventListener('transitionend', swiper.onSlideToWrapperTransitionEnd);\n        swiper.onSlideToWrapperTransitionEnd = null;\n        delete swiper.onSlideToWrapperTransitionEnd;\n        swiper.transitionEnd(runCallbacks, direction);\n      };\n    }\n    swiper.wrapperEl.addEventListener('transitionend', swiper.onSlideToWrapperTransitionEnd);\n  }\n  return true;\n}\n\nfunction slideToLoop(index, speed, runCallbacks, internal) {\n  if (index === void 0) {\n    index = 0;\n  }\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  if (typeof index === 'string') {\n    const indexAsNumber = parseInt(index, 10);\n    index = indexAsNumber;\n  }\n  const swiper = this;\n  if (swiper.destroyed) return;\n  if (typeof speed === 'undefined') {\n    speed = swiper.params.speed;\n  }\n  const gridEnabled = swiper.grid && swiper.params.grid && swiper.params.grid.rows > 1;\n  let newIndex = index;\n  if (swiper.params.loop) {\n    if (swiper.virtual && swiper.params.virtual.enabled) {\n      // eslint-disable-next-line\n      newIndex = newIndex + swiper.virtual.slidesBefore;\n    } else {\n      let targetSlideIndex;\n      if (gridEnabled) {\n        const slideIndex = newIndex * swiper.params.grid.rows;\n        targetSlideIndex = swiper.slides.find(slideEl => slideEl.getAttribute('data-swiper-slide-index') * 1 === slideIndex).column;\n      } else {\n        targetSlideIndex = swiper.getSlideIndexByData(newIndex);\n      }\n      const cols = gridEnabled ? Math.ceil(swiper.slides.length / swiper.params.grid.rows) : swiper.slides.length;\n      const {\n        centeredSlides\n      } = swiper.params;\n      let slidesPerView = swiper.params.slidesPerView;\n      if (slidesPerView === 'auto') {\n        slidesPerView = swiper.slidesPerViewDynamic();\n      } else {\n        slidesPerView = Math.ceil(parseFloat(swiper.params.slidesPerView, 10));\n        if (centeredSlides && slidesPerView % 2 === 0) {\n          slidesPerView = slidesPerView + 1;\n        }\n      }\n      let needLoopFix = cols - targetSlideIndex < slidesPerView;\n      if (centeredSlides) {\n        needLoopFix = needLoopFix || targetSlideIndex < Math.ceil(slidesPerView / 2);\n      }\n      if (internal && centeredSlides && swiper.params.slidesPerView !== 'auto' && !gridEnabled) {\n        needLoopFix = false;\n      }\n      if (needLoopFix) {\n        const direction = centeredSlides ? targetSlideIndex < swiper.activeIndex ? 'prev' : 'next' : targetSlideIndex - swiper.activeIndex - 1 < swiper.params.slidesPerView ? 'next' : 'prev';\n        swiper.loopFix({\n          direction,\n          slideTo: true,\n          activeSlideIndex: direction === 'next' ? targetSlideIndex + 1 : targetSlideIndex - cols + 1,\n          slideRealIndex: direction === 'next' ? swiper.realIndex : undefined\n        });\n      }\n      if (gridEnabled) {\n        const slideIndex = newIndex * swiper.params.grid.rows;\n        newIndex = swiper.slides.find(slideEl => slideEl.getAttribute('data-swiper-slide-index') * 1 === slideIndex).column;\n      } else {\n        newIndex = swiper.getSlideIndexByData(newIndex);\n      }\n    }\n  }\n  requestAnimationFrame(() => {\n    swiper.slideTo(newIndex, speed, runCallbacks, internal);\n  });\n  return swiper;\n}\n\n/* eslint no-unused-vars: \"off\" */\nfunction slideNext(speed, runCallbacks, internal) {\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  const swiper = this;\n  const {\n    enabled,\n    params,\n    animating\n  } = swiper;\n  if (!enabled || swiper.destroyed) return swiper;\n  if (typeof speed === 'undefined') {\n    speed = swiper.params.speed;\n  }\n  let perGroup = params.slidesPerGroup;\n  if (params.slidesPerView === 'auto' && params.slidesPerGroup === 1 && params.slidesPerGroupAuto) {\n    perGroup = Math.max(swiper.slidesPerViewDynamic('current', true), 1);\n  }\n  const increment = swiper.activeIndex < params.slidesPerGroupSkip ? 1 : perGroup;\n  const isVirtual = swiper.virtual && params.virtual.enabled;\n  if (params.loop) {\n    if (animating && !isVirtual && params.loopPreventsSliding) return false;\n    swiper.loopFix({\n      direction: 'next'\n    });\n    // eslint-disable-next-line\n    swiper._clientLeft = swiper.wrapperEl.clientLeft;\n    if (swiper.activeIndex === swiper.slides.length - 1 && params.cssMode) {\n      requestAnimationFrame(() => {\n        swiper.slideTo(swiper.activeIndex + increment, speed, runCallbacks, internal);\n      });\n      return true;\n    }\n  }\n  if (params.rewind && swiper.isEnd) {\n    return swiper.slideTo(0, speed, runCallbacks, internal);\n  }\n  return swiper.slideTo(swiper.activeIndex + increment, speed, runCallbacks, internal);\n}\n\n/* eslint no-unused-vars: \"off\" */\nfunction slidePrev(speed, runCallbacks, internal) {\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  const swiper = this;\n  const {\n    params,\n    snapGrid,\n    slidesGrid,\n    rtlTranslate,\n    enabled,\n    animating\n  } = swiper;\n  if (!enabled || swiper.destroyed) return swiper;\n  if (typeof speed === 'undefined') {\n    speed = swiper.params.speed;\n  }\n  const isVirtual = swiper.virtual && params.virtual.enabled;\n  if (params.loop) {\n    if (animating && !isVirtual && params.loopPreventsSliding) return false;\n    swiper.loopFix({\n      direction: 'prev'\n    });\n    // eslint-disable-next-line\n    swiper._clientLeft = swiper.wrapperEl.clientLeft;\n  }\n  const translate = rtlTranslate ? swiper.translate : -swiper.translate;\n  function normalize(val) {\n    if (val < 0) return -Math.floor(Math.abs(val));\n    return Math.floor(val);\n  }\n  const normalizedTranslate = normalize(translate);\n  const normalizedSnapGrid = snapGrid.map(val => normalize(val));\n  const isFreeMode = params.freeMode && params.freeMode.enabled;\n  let prevSnap = snapGrid[normalizedSnapGrid.indexOf(normalizedTranslate) - 1];\n  if (typeof prevSnap === 'undefined' && (params.cssMode || isFreeMode)) {\n    let prevSnapIndex;\n    snapGrid.forEach((snap, snapIndex) => {\n      if (normalizedTranslate >= snap) {\n        // prevSnap = snap;\n        prevSnapIndex = snapIndex;\n      }\n    });\n    if (typeof prevSnapIndex !== 'undefined') {\n      prevSnap = isFreeMode ? snapGrid[prevSnapIndex] : snapGrid[prevSnapIndex > 0 ? prevSnapIndex - 1 : prevSnapIndex];\n    }\n  }\n  let prevIndex = 0;\n  if (typeof prevSnap !== 'undefined') {\n    prevIndex = slidesGrid.indexOf(prevSnap);\n    if (prevIndex < 0) prevIndex = swiper.activeIndex - 1;\n    if (params.slidesPerView === 'auto' && params.slidesPerGroup === 1 && params.slidesPerGroupAuto) {\n      prevIndex = prevIndex - swiper.slidesPerViewDynamic('previous', true) + 1;\n      prevIndex = Math.max(prevIndex, 0);\n    }\n  }\n  if (params.rewind && swiper.isBeginning) {\n    const lastIndex = swiper.params.virtual && swiper.params.virtual.enabled && swiper.virtual ? swiper.virtual.slides.length - 1 : swiper.slides.length - 1;\n    return swiper.slideTo(lastIndex, speed, runCallbacks, internal);\n  } else if (params.loop && swiper.activeIndex === 0 && params.cssMode) {\n    requestAnimationFrame(() => {\n      swiper.slideTo(prevIndex, speed, runCallbacks, internal);\n    });\n    return true;\n  }\n  return swiper.slideTo(prevIndex, speed, runCallbacks, internal);\n}\n\n/* eslint no-unused-vars: \"off\" */\nfunction slideReset(speed, runCallbacks, internal) {\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  const swiper = this;\n  if (swiper.destroyed) return;\n  if (typeof speed === 'undefined') {\n    speed = swiper.params.speed;\n  }\n  return swiper.slideTo(swiper.activeIndex, speed, runCallbacks, internal);\n}\n\n/* eslint no-unused-vars: \"off\" */\nfunction slideToClosest(speed, runCallbacks, internal, threshold) {\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  if (threshold === void 0) {\n    threshold = 0.5;\n  }\n  const swiper = this;\n  if (swiper.destroyed) return;\n  if (typeof speed === 'undefined') {\n    speed = swiper.params.speed;\n  }\n  let index = swiper.activeIndex;\n  const skip = Math.min(swiper.params.slidesPerGroupSkip, index);\n  const snapIndex = skip + Math.floor((index - skip) / swiper.params.slidesPerGroup);\n  const translate = swiper.rtlTranslate ? swiper.translate : -swiper.translate;\n  if (translate >= swiper.snapGrid[snapIndex]) {\n    // The current translate is on or after the current snap index, so the choice\n    // is between the current index and the one after it.\n    const currentSnap = swiper.snapGrid[snapIndex];\n    const nextSnap = swiper.snapGrid[snapIndex + 1];\n    if (translate - currentSnap > (nextSnap - currentSnap) * threshold) {\n      index += swiper.params.slidesPerGroup;\n    }\n  } else {\n    // The current translate is before the current snap index, so the choice\n    // is between the current index and the one before it.\n    const prevSnap = swiper.snapGrid[snapIndex - 1];\n    const currentSnap = swiper.snapGrid[snapIndex];\n    if (translate - prevSnap <= (currentSnap - prevSnap) * threshold) {\n      index -= swiper.params.slidesPerGroup;\n    }\n  }\n  index = Math.max(index, 0);\n  index = Math.min(index, swiper.slidesGrid.length - 1);\n  return swiper.slideTo(index, speed, runCallbacks, internal);\n}\n\nfunction slideToClickedSlide() {\n  const swiper = this;\n  if (swiper.destroyed) return;\n  const {\n    params,\n    slidesEl\n  } = swiper;\n  const slidesPerView = params.slidesPerView === 'auto' ? swiper.slidesPerViewDynamic() : params.slidesPerView;\n  let slideToIndex = swiper.getSlideIndexWhenGrid(swiper.clickedIndex);\n  let realIndex;\n  const slideSelector = swiper.isElement ? `swiper-slide` : `.${params.slideClass}`;\n  const isGrid = swiper.grid && swiper.params.grid && swiper.params.grid.rows > 1;\n  if (params.loop) {\n    if (swiper.animating) return;\n    realIndex = parseInt(swiper.clickedSlide.getAttribute('data-swiper-slide-index'), 10);\n    if (params.centeredSlides) {\n      swiper.slideToLoop(realIndex);\n    } else if (slideToIndex > (isGrid ? (swiper.slides.length - slidesPerView) / 2 - (swiper.params.grid.rows - 1) : swiper.slides.length - slidesPerView)) {\n      swiper.loopFix();\n      slideToIndex = swiper.getSlideIndex(elementChildren(slidesEl, `${slideSelector}[data-swiper-slide-index=\"${realIndex}\"]`)[0]);\n      nextTick(() => {\n        swiper.slideTo(slideToIndex);\n      });\n    } else {\n      swiper.slideTo(slideToIndex);\n    }\n  } else {\n    swiper.slideTo(slideToIndex);\n  }\n}\n\nvar slide = {\n  slideTo,\n  slideToLoop,\n  slideNext,\n  slidePrev,\n  slideReset,\n  slideToClosest,\n  slideToClickedSlide\n};\n\nfunction loopCreate(slideRealIndex, initial) {\n  const swiper = this;\n  const {\n    params,\n    slidesEl\n  } = swiper;\n  if (!params.loop || swiper.virtual && swiper.params.virtual.enabled) return;\n  const initSlides = () => {\n    const slides = elementChildren(slidesEl, `.${params.slideClass}, swiper-slide`);\n    slides.forEach((el, index) => {\n      el.setAttribute('data-swiper-slide-index', index);\n    });\n  };\n  const clearBlankSlides = () => {\n    const slides = elementChildren(slidesEl, `.${params.slideBlankClass}`);\n    slides.forEach(el => {\n      el.remove();\n    });\n    if (slides.length > 0) {\n      swiper.recalcSlides();\n      swiper.updateSlides();\n    }\n  };\n  const gridEnabled = swiper.grid && params.grid && params.grid.rows > 1;\n  if (params.loopAddBlankSlides && (params.slidesPerGroup > 1 || gridEnabled)) {\n    clearBlankSlides();\n  }\n  const slidesPerGroup = params.slidesPerGroup * (gridEnabled ? params.grid.rows : 1);\n  const shouldFillGroup = swiper.slides.length % slidesPerGroup !== 0;\n  const shouldFillGrid = gridEnabled && swiper.slides.length % params.grid.rows !== 0;\n  const addBlankSlides = amountOfSlides => {\n    for (let i = 0; i < amountOfSlides; i += 1) {\n      const slideEl = swiper.isElement ? createElement('swiper-slide', [params.slideBlankClass]) : createElement('div', [params.slideClass, params.slideBlankClass]);\n      swiper.slidesEl.append(slideEl);\n    }\n  };\n  if (shouldFillGroup) {\n    if (params.loopAddBlankSlides) {\n      const slidesToAdd = slidesPerGroup - swiper.slides.length % slidesPerGroup;\n      addBlankSlides(slidesToAdd);\n      swiper.recalcSlides();\n      swiper.updateSlides();\n    } else {\n      showWarning('Swiper Loop Warning: The number of slides is not even to slidesPerGroup, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)');\n    }\n    initSlides();\n  } else if (shouldFillGrid) {\n    if (params.loopAddBlankSlides) {\n      const slidesToAdd = params.grid.rows - swiper.slides.length % params.grid.rows;\n      addBlankSlides(slidesToAdd);\n      swiper.recalcSlides();\n      swiper.updateSlides();\n    } else {\n      showWarning('Swiper Loop Warning: The number of slides is not even to grid.rows, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)');\n    }\n    initSlides();\n  } else {\n    initSlides();\n  }\n  swiper.loopFix({\n    slideRealIndex,\n    direction: params.centeredSlides ? undefined : 'next',\n    initial\n  });\n}\n\nfunction loopFix(_temp) {\n  let {\n    slideRealIndex,\n    slideTo = true,\n    direction,\n    setTranslate,\n    activeSlideIndex,\n    initial,\n    byController,\n    byMousewheel\n  } = _temp === void 0 ? {} : _temp;\n  const swiper = this;\n  if (!swiper.params.loop) return;\n  swiper.emit('beforeLoopFix');\n  const {\n    slides,\n    allowSlidePrev,\n    allowSlideNext,\n    slidesEl,\n    params\n  } = swiper;\n  const {\n    centeredSlides,\n    initialSlide\n  } = params;\n  swiper.allowSlidePrev = true;\n  swiper.allowSlideNext = true;\n  if (swiper.virtual && params.virtual.enabled) {\n    if (slideTo) {\n      if (!params.centeredSlides && swiper.snapIndex === 0) {\n        swiper.slideTo(swiper.virtual.slides.length, 0, false, true);\n      } else if (params.centeredSlides && swiper.snapIndex < params.slidesPerView) {\n        swiper.slideTo(swiper.virtual.slides.length + swiper.snapIndex, 0, false, true);\n      } else if (swiper.snapIndex === swiper.snapGrid.length - 1) {\n        swiper.slideTo(swiper.virtual.slidesBefore, 0, false, true);\n      }\n    }\n    swiper.allowSlidePrev = allowSlidePrev;\n    swiper.allowSlideNext = allowSlideNext;\n    swiper.emit('loopFix');\n    return;\n  }\n  let slidesPerView = params.slidesPerView;\n  if (slidesPerView === 'auto') {\n    slidesPerView = swiper.slidesPerViewDynamic();\n  } else {\n    slidesPerView = Math.ceil(parseFloat(params.slidesPerView, 10));\n    if (centeredSlides && slidesPerView % 2 === 0) {\n      slidesPerView = slidesPerView + 1;\n    }\n  }\n  const slidesPerGroup = params.slidesPerGroupAuto ? slidesPerView : params.slidesPerGroup;\n  let loopedSlides = centeredSlides ? Math.max(slidesPerGroup, Math.ceil(slidesPerView / 2)) : slidesPerGroup;\n  if (loopedSlides % slidesPerGroup !== 0) {\n    loopedSlides += slidesPerGroup - loopedSlides % slidesPerGroup;\n  }\n  loopedSlides += params.loopAdditionalSlides;\n  swiper.loopedSlides = loopedSlides;\n  const gridEnabled = swiper.grid && params.grid && params.grid.rows > 1;\n  if (slides.length < slidesPerView + loopedSlides || swiper.params.effect === 'cards' && slides.length < slidesPerView + loopedSlides * 2) {\n    showWarning('Swiper Loop Warning: The number of slides is not enough for loop mode, it will be disabled or not function properly. You need to add more slides (or make duplicates) or lower the values of slidesPerView and slidesPerGroup parameters');\n  } else if (gridEnabled && params.grid.fill === 'row') {\n    showWarning('Swiper Loop Warning: Loop mode is not compatible with grid.fill = `row`');\n  }\n  const prependSlidesIndexes = [];\n  const appendSlidesIndexes = [];\n  const cols = gridEnabled ? Math.ceil(slides.length / params.grid.rows) : slides.length;\n  const isInitialOverflow = initial && cols - initialSlide < slidesPerView && !centeredSlides;\n  let activeIndex = isInitialOverflow ? initialSlide : swiper.activeIndex;\n  if (typeof activeSlideIndex === 'undefined') {\n    activeSlideIndex = swiper.getSlideIndex(slides.find(el => el.classList.contains(params.slideActiveClass)));\n  } else {\n    activeIndex = activeSlideIndex;\n  }\n  const isNext = direction === 'next' || !direction;\n  const isPrev = direction === 'prev' || !direction;\n  let slidesPrepended = 0;\n  let slidesAppended = 0;\n  const activeColIndex = gridEnabled ? slides[activeSlideIndex].column : activeSlideIndex;\n  const activeColIndexWithShift = activeColIndex + (centeredSlides && typeof setTranslate === 'undefined' ? -slidesPerView / 2 + 0.5 : 0);\n  // prepend last slides before start\n  if (activeColIndexWithShift < loopedSlides) {\n    slidesPrepended = Math.max(loopedSlides - activeColIndexWithShift, slidesPerGroup);\n    for (let i = 0; i < loopedSlides - activeColIndexWithShift; i += 1) {\n      const index = i - Math.floor(i / cols) * cols;\n      if (gridEnabled) {\n        const colIndexToPrepend = cols - index - 1;\n        for (let i = slides.length - 1; i >= 0; i -= 1) {\n          if (slides[i].column === colIndexToPrepend) prependSlidesIndexes.push(i);\n        }\n        // slides.forEach((slide, slideIndex) => {\n        //   if (slide.column === colIndexToPrepend) prependSlidesIndexes.push(slideIndex);\n        // });\n      } else {\n        prependSlidesIndexes.push(cols - index - 1);\n      }\n    }\n  } else if (activeColIndexWithShift + slidesPerView > cols - loopedSlides) {\n    slidesAppended = Math.max(activeColIndexWithShift - (cols - loopedSlides * 2), slidesPerGroup);\n    if (isInitialOverflow) {\n      slidesAppended = Math.max(slidesAppended, slidesPerView - cols + initialSlide + 1);\n    }\n    for (let i = 0; i < slidesAppended; i += 1) {\n      const index = i - Math.floor(i / cols) * cols;\n      if (gridEnabled) {\n        slides.forEach((slide, slideIndex) => {\n          if (slide.column === index) appendSlidesIndexes.push(slideIndex);\n        });\n      } else {\n        appendSlidesIndexes.push(index);\n      }\n    }\n  }\n  swiper.__preventObserver__ = true;\n  requestAnimationFrame(() => {\n    swiper.__preventObserver__ = false;\n  });\n  if (swiper.params.effect === 'cards' && slides.length < slidesPerView + loopedSlides * 2) {\n    if (appendSlidesIndexes.includes(activeSlideIndex)) {\n      appendSlidesIndexes.splice(appendSlidesIndexes.indexOf(activeSlideIndex), 1);\n    }\n    if (prependSlidesIndexes.includes(activeSlideIndex)) {\n      prependSlidesIndexes.splice(prependSlidesIndexes.indexOf(activeSlideIndex), 1);\n    }\n  }\n  if (isPrev) {\n    prependSlidesIndexes.forEach(index => {\n      slides[index].swiperLoopMoveDOM = true;\n      slidesEl.prepend(slides[index]);\n      slides[index].swiperLoopMoveDOM = false;\n    });\n  }\n  if (isNext) {\n    appendSlidesIndexes.forEach(index => {\n      slides[index].swiperLoopMoveDOM = true;\n      slidesEl.append(slides[index]);\n      slides[index].swiperLoopMoveDOM = false;\n    });\n  }\n  swiper.recalcSlides();\n  if (params.slidesPerView === 'auto') {\n    swiper.updateSlides();\n  } else if (gridEnabled && (prependSlidesIndexes.length > 0 && isPrev || appendSlidesIndexes.length > 0 && isNext)) {\n    swiper.slides.forEach((slide, slideIndex) => {\n      swiper.grid.updateSlide(slideIndex, slide, swiper.slides);\n    });\n  }\n  if (params.watchSlidesProgress) {\n    swiper.updateSlidesOffset();\n  }\n  if (slideTo) {\n    if (prependSlidesIndexes.length > 0 && isPrev) {\n      if (typeof slideRealIndex === 'undefined') {\n        const currentSlideTranslate = swiper.slidesGrid[activeIndex];\n        const newSlideTranslate = swiper.slidesGrid[activeIndex + slidesPrepended];\n        const diff = newSlideTranslate - currentSlideTranslate;\n        if (byMousewheel) {\n          swiper.setTranslate(swiper.translate - diff);\n        } else {\n          swiper.slideTo(activeIndex + Math.ceil(slidesPrepended), 0, false, true);\n          if (setTranslate) {\n            swiper.touchEventsData.startTranslate = swiper.touchEventsData.startTranslate - diff;\n            swiper.touchEventsData.currentTranslate = swiper.touchEventsData.currentTranslate - diff;\n          }\n        }\n      } else {\n        if (setTranslate) {\n          const shift = gridEnabled ? prependSlidesIndexes.length / params.grid.rows : prependSlidesIndexes.length;\n          swiper.slideTo(swiper.activeIndex + shift, 0, false, true);\n          swiper.touchEventsData.currentTranslate = swiper.translate;\n        }\n      }\n    } else if (appendSlidesIndexes.length > 0 && isNext) {\n      if (typeof slideRealIndex === 'undefined') {\n        const currentSlideTranslate = swiper.slidesGrid[activeIndex];\n        const newSlideTranslate = swiper.slidesGrid[activeIndex - slidesAppended];\n        const diff = newSlideTranslate - currentSlideTranslate;\n        if (byMousewheel) {\n          swiper.setTranslate(swiper.translate - diff);\n        } else {\n          swiper.slideTo(activeIndex - slidesAppended, 0, false, true);\n          if (setTranslate) {\n            swiper.touchEventsData.startTranslate = swiper.touchEventsData.startTranslate - diff;\n            swiper.touchEventsData.currentTranslate = swiper.touchEventsData.currentTranslate - diff;\n          }\n        }\n      } else {\n        const shift = gridEnabled ? appendSlidesIndexes.length / params.grid.rows : appendSlidesIndexes.length;\n        swiper.slideTo(swiper.activeIndex - shift, 0, false, true);\n      }\n    }\n  }\n  swiper.allowSlidePrev = allowSlidePrev;\n  swiper.allowSlideNext = allowSlideNext;\n  if (swiper.controller && swiper.controller.control && !byController) {\n    const loopParams = {\n      slideRealIndex,\n      direction,\n      setTranslate,\n      activeSlideIndex,\n      byController: true\n    };\n    if (Array.isArray(swiper.controller.control)) {\n      swiper.controller.control.forEach(c => {\n        if (!c.destroyed && c.params.loop) c.loopFix({\n          ...loopParams,\n          slideTo: c.params.slidesPerView === params.slidesPerView ? slideTo : false\n        });\n      });\n    } else if (swiper.controller.control instanceof swiper.constructor && swiper.controller.control.params.loop) {\n      swiper.controller.control.loopFix({\n        ...loopParams,\n        slideTo: swiper.controller.control.params.slidesPerView === params.slidesPerView ? slideTo : false\n      });\n    }\n  }\n  swiper.emit('loopFix');\n}\n\nfunction loopDestroy() {\n  const swiper = this;\n  const {\n    params,\n    slidesEl\n  } = swiper;\n  if (!params.loop || !slidesEl || swiper.virtual && swiper.params.virtual.enabled) return;\n  swiper.recalcSlides();\n  const newSlidesOrder = [];\n  swiper.slides.forEach(slideEl => {\n    const index = typeof slideEl.swiperSlideIndex === 'undefined' ? slideEl.getAttribute('data-swiper-slide-index') * 1 : slideEl.swiperSlideIndex;\n    newSlidesOrder[index] = slideEl;\n  });\n  swiper.slides.forEach(slideEl => {\n    slideEl.removeAttribute('data-swiper-slide-index');\n  });\n  newSlidesOrder.forEach(slideEl => {\n    slidesEl.append(slideEl);\n  });\n  swiper.recalcSlides();\n  swiper.slideTo(swiper.realIndex, 0);\n}\n\nvar loop = {\n  loopCreate,\n  loopFix,\n  loopDestroy\n};\n\nfunction setGrabCursor(moving) {\n  const swiper = this;\n  if (!swiper.params.simulateTouch || swiper.params.watchOverflow && swiper.isLocked || swiper.params.cssMode) return;\n  const el = swiper.params.touchEventsTarget === 'container' ? swiper.el : swiper.wrapperEl;\n  if (swiper.isElement) {\n    swiper.__preventObserver__ = true;\n  }\n  el.style.cursor = 'move';\n  el.style.cursor = moving ? 'grabbing' : 'grab';\n  if (swiper.isElement) {\n    requestAnimationFrame(() => {\n      swiper.__preventObserver__ = false;\n    });\n  }\n}\n\nfunction unsetGrabCursor() {\n  const swiper = this;\n  if (swiper.params.watchOverflow && swiper.isLocked || swiper.params.cssMode) {\n    return;\n  }\n  if (swiper.isElement) {\n    swiper.__preventObserver__ = true;\n  }\n  swiper[swiper.params.touchEventsTarget === 'container' ? 'el' : 'wrapperEl'].style.cursor = '';\n  if (swiper.isElement) {\n    requestAnimationFrame(() => {\n      swiper.__preventObserver__ = false;\n    });\n  }\n}\n\nvar grabCursor = {\n  setGrabCursor,\n  unsetGrabCursor\n};\n\n// Modified from https://stackoverflow.com/questions/54520554/custom-element-getrootnode-closest-function-crossing-multiple-parent-shadowd\nfunction closestElement(selector, base) {\n  if (base === void 0) {\n    base = this;\n  }\n  function __closestFrom(el) {\n    if (!el || el === getDocument() || el === getWindow()) return null;\n    if (el.assignedSlot) el = el.assignedSlot;\n    const found = el.closest(selector);\n    if (!found && !el.getRootNode) {\n      return null;\n    }\n    return found || __closestFrom(el.getRootNode().host);\n  }\n  return __closestFrom(base);\n}\nfunction preventEdgeSwipe(swiper, event, startX) {\n  const window = getWindow();\n  const {\n    params\n  } = swiper;\n  const edgeSwipeDetection = params.edgeSwipeDetection;\n  const edgeSwipeThreshold = params.edgeSwipeThreshold;\n  if (edgeSwipeDetection && (startX <= edgeSwipeThreshold || startX >= window.innerWidth - edgeSwipeThreshold)) {\n    if (edgeSwipeDetection === 'prevent') {\n      event.preventDefault();\n      return true;\n    }\n    return false;\n  }\n  return true;\n}\nfunction onTouchStart(event) {\n  const swiper = this;\n  const document = getDocument();\n  let e = event;\n  if (e.originalEvent) e = e.originalEvent;\n  const data = swiper.touchEventsData;\n  if (e.type === 'pointerdown') {\n    if (data.pointerId !== null && data.pointerId !== e.pointerId) {\n      return;\n    }\n    data.pointerId = e.pointerId;\n  } else if (e.type === 'touchstart' && e.targetTouches.length === 1) {\n    data.touchId = e.targetTouches[0].identifier;\n  }\n  if (e.type === 'touchstart') {\n    // don't proceed touch event\n    preventEdgeSwipe(swiper, e, e.targetTouches[0].pageX);\n    return;\n  }\n  const {\n    params,\n    touches,\n    enabled\n  } = swiper;\n  if (!enabled) return;\n  if (!params.simulateTouch && e.pointerType === 'mouse') return;\n  if (swiper.animating && params.preventInteractionOnTransition) {\n    return;\n  }\n  if (!swiper.animating && params.cssMode && params.loop) {\n    swiper.loopFix();\n  }\n  let targetEl = e.target;\n  if (params.touchEventsTarget === 'wrapper') {\n    if (!elementIsChildOf(targetEl, swiper.wrapperEl)) return;\n  }\n  if ('which' in e && e.which === 3) return;\n  if ('button' in e && e.button > 0) return;\n  if (data.isTouched && data.isMoved) return;\n\n  // change target el for shadow root component\n  const swipingClassHasValue = !!params.noSwipingClass && params.noSwipingClass !== '';\n  // eslint-disable-next-line\n  const eventPath = e.composedPath ? e.composedPath() : e.path;\n  if (swipingClassHasValue && e.target && e.target.shadowRoot && eventPath) {\n    targetEl = eventPath[0];\n  }\n  const noSwipingSelector = params.noSwipingSelector ? params.noSwipingSelector : `.${params.noSwipingClass}`;\n  const isTargetShadow = !!(e.target && e.target.shadowRoot);\n\n  // use closestElement for shadow root element to get the actual closest for nested shadow root element\n  if (params.noSwiping && (isTargetShadow ? closestElement(noSwipingSelector, targetEl) : targetEl.closest(noSwipingSelector))) {\n    swiper.allowClick = true;\n    return;\n  }\n  if (params.swipeHandler) {\n    if (!targetEl.closest(params.swipeHandler)) return;\n  }\n  touches.currentX = e.pageX;\n  touches.currentY = e.pageY;\n  const startX = touches.currentX;\n  const startY = touches.currentY;\n\n  // Do NOT start if iOS edge swipe is detected. Otherwise iOS app cannot swipe-to-go-back anymore\n\n  if (!preventEdgeSwipe(swiper, e, startX)) {\n    return;\n  }\n  Object.assign(data, {\n    isTouched: true,\n    isMoved: false,\n    allowTouchCallbacks: true,\n    isScrolling: undefined,\n    startMoving: undefined\n  });\n  touches.startX = startX;\n  touches.startY = startY;\n  data.touchStartTime = now();\n  swiper.allowClick = true;\n  swiper.updateSize();\n  swiper.swipeDirection = undefined;\n  if (params.threshold > 0) data.allowThresholdMove = false;\n  let preventDefault = true;\n  if (targetEl.matches(data.focusableElements)) {\n    preventDefault = false;\n    if (targetEl.nodeName === 'SELECT') {\n      data.isTouched = false;\n    }\n  }\n  if (document.activeElement && document.activeElement.matches(data.focusableElements) && document.activeElement !== targetEl && (e.pointerType === 'mouse' || e.pointerType !== 'mouse' && !targetEl.matches(data.focusableElements))) {\n    document.activeElement.blur();\n  }\n  const shouldPreventDefault = preventDefault && swiper.allowTouchMove && params.touchStartPreventDefault;\n  if ((params.touchStartForcePreventDefault || shouldPreventDefault) && !targetEl.isContentEditable) {\n    e.preventDefault();\n  }\n  if (params.freeMode && params.freeMode.enabled && swiper.freeMode && swiper.animating && !params.cssMode) {\n    swiper.freeMode.onTouchStart();\n  }\n  swiper.emit('touchStart', e);\n}\n\nfunction onTouchMove(event) {\n  const document = getDocument();\n  const swiper = this;\n  const data = swiper.touchEventsData;\n  const {\n    params,\n    touches,\n    rtlTranslate: rtl,\n    enabled\n  } = swiper;\n  if (!enabled) return;\n  if (!params.simulateTouch && event.pointerType === 'mouse') return;\n  let e = event;\n  if (e.originalEvent) e = e.originalEvent;\n  if (e.type === 'pointermove') {\n    if (data.touchId !== null) return; // return from pointer if we use touch\n    const id = e.pointerId;\n    if (id !== data.pointerId) return;\n  }\n  let targetTouch;\n  if (e.type === 'touchmove') {\n    targetTouch = [...e.changedTouches].find(t => t.identifier === data.touchId);\n    if (!targetTouch || targetTouch.identifier !== data.touchId) return;\n  } else {\n    targetTouch = e;\n  }\n  if (!data.isTouched) {\n    if (data.startMoving && data.isScrolling) {\n      swiper.emit('touchMoveOpposite', e);\n    }\n    return;\n  }\n  const pageX = targetTouch.pageX;\n  const pageY = targetTouch.pageY;\n  if (e.preventedByNestedSwiper) {\n    touches.startX = pageX;\n    touches.startY = pageY;\n    return;\n  }\n  if (!swiper.allowTouchMove) {\n    if (!e.target.matches(data.focusableElements)) {\n      swiper.allowClick = false;\n    }\n    if (data.isTouched) {\n      Object.assign(touches, {\n        startX: pageX,\n        startY: pageY,\n        currentX: pageX,\n        currentY: pageY\n      });\n      data.touchStartTime = now();\n    }\n    return;\n  }\n  if (params.touchReleaseOnEdges && !params.loop) {\n    if (swiper.isVertical()) {\n      // Vertical\n      if (pageY < touches.startY && swiper.translate <= swiper.maxTranslate() || pageY > touches.startY && swiper.translate >= swiper.minTranslate()) {\n        data.isTouched = false;\n        data.isMoved = false;\n        return;\n      }\n    } else if (rtl && (pageX > touches.startX && -swiper.translate <= swiper.maxTranslate() || pageX < touches.startX && -swiper.translate >= swiper.minTranslate())) {\n      return;\n    } else if (!rtl && (pageX < touches.startX && swiper.translate <= swiper.maxTranslate() || pageX > touches.startX && swiper.translate >= swiper.minTranslate())) {\n      return;\n    }\n  }\n  if (document.activeElement && document.activeElement.matches(data.focusableElements) && document.activeElement !== e.target && e.pointerType !== 'mouse') {\n    document.activeElement.blur();\n  }\n  if (document.activeElement) {\n    if (e.target === document.activeElement && e.target.matches(data.focusableElements)) {\n      data.isMoved = true;\n      swiper.allowClick = false;\n      return;\n    }\n  }\n  if (data.allowTouchCallbacks) {\n    swiper.emit('touchMove', e);\n  }\n  touches.previousX = touches.currentX;\n  touches.previousY = touches.currentY;\n  touches.currentX = pageX;\n  touches.currentY = pageY;\n  const diffX = touches.currentX - touches.startX;\n  const diffY = touches.currentY - touches.startY;\n  if (swiper.params.threshold && Math.sqrt(diffX ** 2 + diffY ** 2) < swiper.params.threshold) return;\n  if (typeof data.isScrolling === 'undefined') {\n    let touchAngle;\n    if (swiper.isHorizontal() && touches.currentY === touches.startY || swiper.isVertical() && touches.currentX === touches.startX) {\n      data.isScrolling = false;\n    } else {\n      // eslint-disable-next-line\n      if (diffX * diffX + diffY * diffY >= 25) {\n        touchAngle = Math.atan2(Math.abs(diffY), Math.abs(diffX)) * 180 / Math.PI;\n        data.isScrolling = swiper.isHorizontal() ? touchAngle > params.touchAngle : 90 - touchAngle > params.touchAngle;\n      }\n    }\n  }\n  if (data.isScrolling) {\n    swiper.emit('touchMoveOpposite', e);\n  }\n  if (typeof data.startMoving === 'undefined') {\n    if (touches.currentX !== touches.startX || touches.currentY !== touches.startY) {\n      data.startMoving = true;\n    }\n  }\n  if (data.isScrolling || e.type === 'touchmove' && data.preventTouchMoveFromPointerMove) {\n    data.isTouched = false;\n    return;\n  }\n  if (!data.startMoving) {\n    return;\n  }\n  swiper.allowClick = false;\n  if (!params.cssMode && e.cancelable) {\n    e.preventDefault();\n  }\n  if (params.touchMoveStopPropagation && !params.nested) {\n    e.stopPropagation();\n  }\n  let diff = swiper.isHorizontal() ? diffX : diffY;\n  let touchesDiff = swiper.isHorizontal() ? touches.currentX - touches.previousX : touches.currentY - touches.previousY;\n  if (params.oneWayMovement) {\n    diff = Math.abs(diff) * (rtl ? 1 : -1);\n    touchesDiff = Math.abs(touchesDiff) * (rtl ? 1 : -1);\n  }\n  touches.diff = diff;\n  diff *= params.touchRatio;\n  if (rtl) {\n    diff = -diff;\n    touchesDiff = -touchesDiff;\n  }\n  const prevTouchesDirection = swiper.touchesDirection;\n  swiper.swipeDirection = diff > 0 ? 'prev' : 'next';\n  swiper.touchesDirection = touchesDiff > 0 ? 'prev' : 'next';\n  const isLoop = swiper.params.loop && !params.cssMode;\n  const allowLoopFix = swiper.touchesDirection === 'next' && swiper.allowSlideNext || swiper.touchesDirection === 'prev' && swiper.allowSlidePrev;\n  if (!data.isMoved) {\n    if (isLoop && allowLoopFix) {\n      swiper.loopFix({\n        direction: swiper.swipeDirection\n      });\n    }\n    data.startTranslate = swiper.getTranslate();\n    swiper.setTransition(0);\n    if (swiper.animating) {\n      const evt = new window.CustomEvent('transitionend', {\n        bubbles: true,\n        cancelable: true,\n        detail: {\n          bySwiperTouchMove: true\n        }\n      });\n      swiper.wrapperEl.dispatchEvent(evt);\n    }\n    data.allowMomentumBounce = false;\n    // Grab Cursor\n    if (params.grabCursor && (swiper.allowSlideNext === true || swiper.allowSlidePrev === true)) {\n      swiper.setGrabCursor(true);\n    }\n    swiper.emit('sliderFirstMove', e);\n  }\n  let loopFixed;\n  new Date().getTime();\n  if (params._loopSwapReset !== false && data.isMoved && data.allowThresholdMove && prevTouchesDirection !== swiper.touchesDirection && isLoop && allowLoopFix && Math.abs(diff) >= 1) {\n    Object.assign(touches, {\n      startX: pageX,\n      startY: pageY,\n      currentX: pageX,\n      currentY: pageY,\n      startTranslate: data.currentTranslate\n    });\n    data.loopSwapReset = true;\n    data.startTranslate = data.currentTranslate;\n    return;\n  }\n  swiper.emit('sliderMove', e);\n  data.isMoved = true;\n  data.currentTranslate = diff + data.startTranslate;\n  let disableParentSwiper = true;\n  let resistanceRatio = params.resistanceRatio;\n  if (params.touchReleaseOnEdges) {\n    resistanceRatio = 0;\n  }\n  if (diff > 0) {\n    if (isLoop && allowLoopFix && !loopFixed && data.allowThresholdMove && data.currentTranslate > (params.centeredSlides ? swiper.minTranslate() - swiper.slidesSizesGrid[swiper.activeIndex + 1] - (params.slidesPerView !== 'auto' && swiper.slides.length - params.slidesPerView >= 2 ? swiper.slidesSizesGrid[swiper.activeIndex + 1] + swiper.params.spaceBetween : 0) - swiper.params.spaceBetween : swiper.minTranslate())) {\n      swiper.loopFix({\n        direction: 'prev',\n        setTranslate: true,\n        activeSlideIndex: 0\n      });\n    }\n    if (data.currentTranslate > swiper.minTranslate()) {\n      disableParentSwiper = false;\n      if (params.resistance) {\n        data.currentTranslate = swiper.minTranslate() - 1 + (-swiper.minTranslate() + data.startTranslate + diff) ** resistanceRatio;\n      }\n    }\n  } else if (diff < 0) {\n    if (isLoop && allowLoopFix && !loopFixed && data.allowThresholdMove && data.currentTranslate < (params.centeredSlides ? swiper.maxTranslate() + swiper.slidesSizesGrid[swiper.slidesSizesGrid.length - 1] + swiper.params.spaceBetween + (params.slidesPerView !== 'auto' && swiper.slides.length - params.slidesPerView >= 2 ? swiper.slidesSizesGrid[swiper.slidesSizesGrid.length - 1] + swiper.params.spaceBetween : 0) : swiper.maxTranslate())) {\n      swiper.loopFix({\n        direction: 'next',\n        setTranslate: true,\n        activeSlideIndex: swiper.slides.length - (params.slidesPerView === 'auto' ? swiper.slidesPerViewDynamic() : Math.ceil(parseFloat(params.slidesPerView, 10)))\n      });\n    }\n    if (data.currentTranslate < swiper.maxTranslate()) {\n      disableParentSwiper = false;\n      if (params.resistance) {\n        data.currentTranslate = swiper.maxTranslate() + 1 - (swiper.maxTranslate() - data.startTranslate - diff) ** resistanceRatio;\n      }\n    }\n  }\n  if (disableParentSwiper) {\n    e.preventedByNestedSwiper = true;\n  }\n\n  // Directions locks\n  if (!swiper.allowSlideNext && swiper.swipeDirection === 'next' && data.currentTranslate < data.startTranslate) {\n    data.currentTranslate = data.startTranslate;\n  }\n  if (!swiper.allowSlidePrev && swiper.swipeDirection === 'prev' && data.currentTranslate > data.startTranslate) {\n    data.currentTranslate = data.startTranslate;\n  }\n  if (!swiper.allowSlidePrev && !swiper.allowSlideNext) {\n    data.currentTranslate = data.startTranslate;\n  }\n\n  // Threshold\n  if (params.threshold > 0) {\n    if (Math.abs(diff) > params.threshold || data.allowThresholdMove) {\n      if (!data.allowThresholdMove) {\n        data.allowThresholdMove = true;\n        touches.startX = touches.currentX;\n        touches.startY = touches.currentY;\n        data.currentTranslate = data.startTranslate;\n        touches.diff = swiper.isHorizontal() ? touches.currentX - touches.startX : touches.currentY - touches.startY;\n        return;\n      }\n    } else {\n      data.currentTranslate = data.startTranslate;\n      return;\n    }\n  }\n  if (!params.followFinger || params.cssMode) return;\n\n  // Update active index in free mode\n  if (params.freeMode && params.freeMode.enabled && swiper.freeMode || params.watchSlidesProgress) {\n    swiper.updateActiveIndex();\n    swiper.updateSlidesClasses();\n  }\n  if (params.freeMode && params.freeMode.enabled && swiper.freeMode) {\n    swiper.freeMode.onTouchMove();\n  }\n  // Update progress\n  swiper.updateProgress(data.currentTranslate);\n  // Update translate\n  swiper.setTranslate(data.currentTranslate);\n}\n\nfunction onTouchEnd(event) {\n  const swiper = this;\n  const data = swiper.touchEventsData;\n  let e = event;\n  if (e.originalEvent) e = e.originalEvent;\n  let targetTouch;\n  const isTouchEvent = e.type === 'touchend' || e.type === 'touchcancel';\n  if (!isTouchEvent) {\n    if (data.touchId !== null) return; // return from pointer if we use touch\n    if (e.pointerId !== data.pointerId) return;\n    targetTouch = e;\n  } else {\n    targetTouch = [...e.changedTouches].find(t => t.identifier === data.touchId);\n    if (!targetTouch || targetTouch.identifier !== data.touchId) return;\n  }\n  if (['pointercancel', 'pointerout', 'pointerleave', 'contextmenu'].includes(e.type)) {\n    const proceed = ['pointercancel', 'contextmenu'].includes(e.type) && (swiper.browser.isSafari || swiper.browser.isWebView);\n    if (!proceed) {\n      return;\n    }\n  }\n  data.pointerId = null;\n  data.touchId = null;\n  const {\n    params,\n    touches,\n    rtlTranslate: rtl,\n    slidesGrid,\n    enabled\n  } = swiper;\n  if (!enabled) return;\n  if (!params.simulateTouch && e.pointerType === 'mouse') return;\n  if (data.allowTouchCallbacks) {\n    swiper.emit('touchEnd', e);\n  }\n  data.allowTouchCallbacks = false;\n  if (!data.isTouched) {\n    if (data.isMoved && params.grabCursor) {\n      swiper.setGrabCursor(false);\n    }\n    data.isMoved = false;\n    data.startMoving = false;\n    return;\n  }\n\n  // Return Grab Cursor\n  if (params.grabCursor && data.isMoved && data.isTouched && (swiper.allowSlideNext === true || swiper.allowSlidePrev === true)) {\n    swiper.setGrabCursor(false);\n  }\n\n  // Time diff\n  const touchEndTime = now();\n  const timeDiff = touchEndTime - data.touchStartTime;\n\n  // Tap, doubleTap, Click\n  if (swiper.allowClick) {\n    const pathTree = e.path || e.composedPath && e.composedPath();\n    swiper.updateClickedSlide(pathTree && pathTree[0] || e.target, pathTree);\n    swiper.emit('tap click', e);\n    if (timeDiff < 300 && touchEndTime - data.lastClickTime < 300) {\n      swiper.emit('doubleTap doubleClick', e);\n    }\n  }\n  data.lastClickTime = now();\n  nextTick(() => {\n    if (!swiper.destroyed) swiper.allowClick = true;\n  });\n  if (!data.isTouched || !data.isMoved || !swiper.swipeDirection || touches.diff === 0 && !data.loopSwapReset || data.currentTranslate === data.startTranslate && !data.loopSwapReset) {\n    data.isTouched = false;\n    data.isMoved = false;\n    data.startMoving = false;\n    return;\n  }\n  data.isTouched = false;\n  data.isMoved = false;\n  data.startMoving = false;\n  let currentPos;\n  if (params.followFinger) {\n    currentPos = rtl ? swiper.translate : -swiper.translate;\n  } else {\n    currentPos = -data.currentTranslate;\n  }\n  if (params.cssMode) {\n    return;\n  }\n  if (params.freeMode && params.freeMode.enabled) {\n    swiper.freeMode.onTouchEnd({\n      currentPos\n    });\n    return;\n  }\n\n  // Find current slide\n  const swipeToLast = currentPos >= -swiper.maxTranslate() && !swiper.params.loop;\n  let stopIndex = 0;\n  let groupSize = swiper.slidesSizesGrid[0];\n  for (let i = 0; i < slidesGrid.length; i += i < params.slidesPerGroupSkip ? 1 : params.slidesPerGroup) {\n    const increment = i < params.slidesPerGroupSkip - 1 ? 1 : params.slidesPerGroup;\n    if (typeof slidesGrid[i + increment] !== 'undefined') {\n      if (swipeToLast || currentPos >= slidesGrid[i] && currentPos < slidesGrid[i + increment]) {\n        stopIndex = i;\n        groupSize = slidesGrid[i + increment] - slidesGrid[i];\n      }\n    } else if (swipeToLast || currentPos >= slidesGrid[i]) {\n      stopIndex = i;\n      groupSize = slidesGrid[slidesGrid.length - 1] - slidesGrid[slidesGrid.length - 2];\n    }\n  }\n  let rewindFirstIndex = null;\n  let rewindLastIndex = null;\n  if (params.rewind) {\n    if (swiper.isBeginning) {\n      rewindLastIndex = params.virtual && params.virtual.enabled && swiper.virtual ? swiper.virtual.slides.length - 1 : swiper.slides.length - 1;\n    } else if (swiper.isEnd) {\n      rewindFirstIndex = 0;\n    }\n  }\n  // Find current slide size\n  const ratio = (currentPos - slidesGrid[stopIndex]) / groupSize;\n  const increment = stopIndex < params.slidesPerGroupSkip - 1 ? 1 : params.slidesPerGroup;\n  if (timeDiff > params.longSwipesMs) {\n    // Long touches\n    if (!params.longSwipes) {\n      swiper.slideTo(swiper.activeIndex);\n      return;\n    }\n    if (swiper.swipeDirection === 'next') {\n      if (ratio >= params.longSwipesRatio) swiper.slideTo(params.rewind && swiper.isEnd ? rewindFirstIndex : stopIndex + increment);else swiper.slideTo(stopIndex);\n    }\n    if (swiper.swipeDirection === 'prev') {\n      if (ratio > 1 - params.longSwipesRatio) {\n        swiper.slideTo(stopIndex + increment);\n      } else if (rewindLastIndex !== null && ratio < 0 && Math.abs(ratio) > params.longSwipesRatio) {\n        swiper.slideTo(rewindLastIndex);\n      } else {\n        swiper.slideTo(stopIndex);\n      }\n    }\n  } else {\n    // Short swipes\n    if (!params.shortSwipes) {\n      swiper.slideTo(swiper.activeIndex);\n      return;\n    }\n    const isNavButtonTarget = swiper.navigation && (e.target === swiper.navigation.nextEl || e.target === swiper.navigation.prevEl);\n    if (!isNavButtonTarget) {\n      if (swiper.swipeDirection === 'next') {\n        swiper.slideTo(rewindFirstIndex !== null ? rewindFirstIndex : stopIndex + increment);\n      }\n      if (swiper.swipeDirection === 'prev') {\n        swiper.slideTo(rewindLastIndex !== null ? rewindLastIndex : stopIndex);\n      }\n    } else if (e.target === swiper.navigation.nextEl) {\n      swiper.slideTo(stopIndex + increment);\n    } else {\n      swiper.slideTo(stopIndex);\n    }\n  }\n}\n\nfunction onResize() {\n  const swiper = this;\n  const {\n    params,\n    el\n  } = swiper;\n  if (el && el.offsetWidth === 0) return;\n\n  // Breakpoints\n  if (params.breakpoints) {\n    swiper.setBreakpoint();\n  }\n\n  // Save locks\n  const {\n    allowSlideNext,\n    allowSlidePrev,\n    snapGrid\n  } = swiper;\n  const isVirtual = swiper.virtual && swiper.params.virtual.enabled;\n\n  // Disable locks on resize\n  swiper.allowSlideNext = true;\n  swiper.allowSlidePrev = true;\n  swiper.updateSize();\n  swiper.updateSlides();\n  swiper.updateSlidesClasses();\n  const isVirtualLoop = isVirtual && params.loop;\n  if ((params.slidesPerView === 'auto' || params.slidesPerView > 1) && swiper.isEnd && !swiper.isBeginning && !swiper.params.centeredSlides && !isVirtualLoop) {\n    swiper.slideTo(swiper.slides.length - 1, 0, false, true);\n  } else {\n    if (swiper.params.loop && !isVirtual) {\n      swiper.slideToLoop(swiper.realIndex, 0, false, true);\n    } else {\n      swiper.slideTo(swiper.activeIndex, 0, false, true);\n    }\n  }\n  if (swiper.autoplay && swiper.autoplay.running && swiper.autoplay.paused) {\n    clearTimeout(swiper.autoplay.resizeTimeout);\n    swiper.autoplay.resizeTimeout = setTimeout(() => {\n      if (swiper.autoplay && swiper.autoplay.running && swiper.autoplay.paused) {\n        swiper.autoplay.resume();\n      }\n    }, 500);\n  }\n  // Return locks after resize\n  swiper.allowSlidePrev = allowSlidePrev;\n  swiper.allowSlideNext = allowSlideNext;\n  if (swiper.params.watchOverflow && snapGrid !== swiper.snapGrid) {\n    swiper.checkOverflow();\n  }\n}\n\nfunction onClick(e) {\n  const swiper = this;\n  if (!swiper.enabled) return;\n  if (!swiper.allowClick) {\n    if (swiper.params.preventClicks) e.preventDefault();\n    if (swiper.params.preventClicksPropagation && swiper.animating) {\n      e.stopPropagation();\n      e.stopImmediatePropagation();\n    }\n  }\n}\n\nfunction onScroll() {\n  const swiper = this;\n  const {\n    wrapperEl,\n    rtlTranslate,\n    enabled\n  } = swiper;\n  if (!enabled) return;\n  swiper.previousTranslate = swiper.translate;\n  if (swiper.isHorizontal()) {\n    swiper.translate = -wrapperEl.scrollLeft;\n  } else {\n    swiper.translate = -wrapperEl.scrollTop;\n  }\n  // eslint-disable-next-line\n  if (swiper.translate === 0) swiper.translate = 0;\n  swiper.updateActiveIndex();\n  swiper.updateSlidesClasses();\n  let newProgress;\n  const translatesDiff = swiper.maxTranslate() - swiper.minTranslate();\n  if (translatesDiff === 0) {\n    newProgress = 0;\n  } else {\n    newProgress = (swiper.translate - swiper.minTranslate()) / translatesDiff;\n  }\n  if (newProgress !== swiper.progress) {\n    swiper.updateProgress(rtlTranslate ? -swiper.translate : swiper.translate);\n  }\n  swiper.emit('setTranslate', swiper.translate, false);\n}\n\nfunction onLoad(e) {\n  const swiper = this;\n  processLazyPreloader(swiper, e.target);\n  if (swiper.params.cssMode || swiper.params.slidesPerView !== 'auto' && !swiper.params.autoHeight) {\n    return;\n  }\n  swiper.update();\n}\n\nfunction onDocumentTouchStart() {\n  const swiper = this;\n  if (swiper.documentTouchHandlerProceeded) return;\n  swiper.documentTouchHandlerProceeded = true;\n  if (swiper.params.touchReleaseOnEdges) {\n    swiper.el.style.touchAction = 'auto';\n  }\n}\n\nconst events = (swiper, method) => {\n  const document = getDocument();\n  const {\n    params,\n    el,\n    wrapperEl,\n    device\n  } = swiper;\n  const capture = !!params.nested;\n  const domMethod = method === 'on' ? 'addEventListener' : 'removeEventListener';\n  const swiperMethod = method;\n  if (!el || typeof el === 'string') return;\n\n  // Touch Events\n  document[domMethod]('touchstart', swiper.onDocumentTouchStart, {\n    passive: false,\n    capture\n  });\n  el[domMethod]('touchstart', swiper.onTouchStart, {\n    passive: false\n  });\n  el[domMethod]('pointerdown', swiper.onTouchStart, {\n    passive: false\n  });\n  document[domMethod]('touchmove', swiper.onTouchMove, {\n    passive: false,\n    capture\n  });\n  document[domMethod]('pointermove', swiper.onTouchMove, {\n    passive: false,\n    capture\n  });\n  document[domMethod]('touchend', swiper.onTouchEnd, {\n    passive: true\n  });\n  document[domMethod]('pointerup', swiper.onTouchEnd, {\n    passive: true\n  });\n  document[domMethod]('pointercancel', swiper.onTouchEnd, {\n    passive: true\n  });\n  document[domMethod]('touchcancel', swiper.onTouchEnd, {\n    passive: true\n  });\n  document[domMethod]('pointerout', swiper.onTouchEnd, {\n    passive: true\n  });\n  document[domMethod]('pointerleave', swiper.onTouchEnd, {\n    passive: true\n  });\n  document[domMethod]('contextmenu', swiper.onTouchEnd, {\n    passive: true\n  });\n\n  // Prevent Links Clicks\n  if (params.preventClicks || params.preventClicksPropagation) {\n    el[domMethod]('click', swiper.onClick, true);\n  }\n  if (params.cssMode) {\n    wrapperEl[domMethod]('scroll', swiper.onScroll);\n  }\n\n  // Resize handler\n  if (params.updateOnWindowResize) {\n    swiper[swiperMethod](device.ios || device.android ? 'resize orientationchange observerUpdate' : 'resize observerUpdate', onResize, true);\n  } else {\n    swiper[swiperMethod]('observerUpdate', onResize, true);\n  }\n\n  // Images loader\n  el[domMethod]('load', swiper.onLoad, {\n    capture: true\n  });\n};\nfunction attachEvents() {\n  const swiper = this;\n  const {\n    params\n  } = swiper;\n  swiper.onTouchStart = onTouchStart.bind(swiper);\n  swiper.onTouchMove = onTouchMove.bind(swiper);\n  swiper.onTouchEnd = onTouchEnd.bind(swiper);\n  swiper.onDocumentTouchStart = onDocumentTouchStart.bind(swiper);\n  if (params.cssMode) {\n    swiper.onScroll = onScroll.bind(swiper);\n  }\n  swiper.onClick = onClick.bind(swiper);\n  swiper.onLoad = onLoad.bind(swiper);\n  events(swiper, 'on');\n}\nfunction detachEvents() {\n  const swiper = this;\n  events(swiper, 'off');\n}\nvar events$1 = {\n  attachEvents,\n  detachEvents\n};\n\nconst isGridEnabled = (swiper, params) => {\n  return swiper.grid && params.grid && params.grid.rows > 1;\n};\nfunction setBreakpoint() {\n  const swiper = this;\n  const {\n    realIndex,\n    initialized,\n    params,\n    el\n  } = swiper;\n  const breakpoints = params.breakpoints;\n  if (!breakpoints || breakpoints && Object.keys(breakpoints).length === 0) return;\n  const document = getDocument();\n\n  // Get breakpoint for window/container width and update parameters\n  const breakpointsBase = params.breakpointsBase === 'window' || !params.breakpointsBase ? params.breakpointsBase : 'container';\n  const breakpointContainer = ['window', 'container'].includes(params.breakpointsBase) || !params.breakpointsBase ? swiper.el : document.querySelector(params.breakpointsBase);\n  const breakpoint = swiper.getBreakpoint(breakpoints, breakpointsBase, breakpointContainer);\n  if (!breakpoint || swiper.currentBreakpoint === breakpoint) return;\n  const breakpointOnlyParams = breakpoint in breakpoints ? breakpoints[breakpoint] : undefined;\n  const breakpointParams = breakpointOnlyParams || swiper.originalParams;\n  const wasMultiRow = isGridEnabled(swiper, params);\n  const isMultiRow = isGridEnabled(swiper, breakpointParams);\n  const wasGrabCursor = swiper.params.grabCursor;\n  const isGrabCursor = breakpointParams.grabCursor;\n  const wasEnabled = params.enabled;\n  if (wasMultiRow && !isMultiRow) {\n    el.classList.remove(`${params.containerModifierClass}grid`, `${params.containerModifierClass}grid-column`);\n    swiper.emitContainerClasses();\n  } else if (!wasMultiRow && isMultiRow) {\n    el.classList.add(`${params.containerModifierClass}grid`);\n    if (breakpointParams.grid.fill && breakpointParams.grid.fill === 'column' || !breakpointParams.grid.fill && params.grid.fill === 'column') {\n      el.classList.add(`${params.containerModifierClass}grid-column`);\n    }\n    swiper.emitContainerClasses();\n  }\n  if (wasGrabCursor && !isGrabCursor) {\n    swiper.unsetGrabCursor();\n  } else if (!wasGrabCursor && isGrabCursor) {\n    swiper.setGrabCursor();\n  }\n\n  // Toggle navigation, pagination, scrollbar\n  ['navigation', 'pagination', 'scrollbar'].forEach(prop => {\n    if (typeof breakpointParams[prop] === 'undefined') return;\n    const wasModuleEnabled = params[prop] && params[prop].enabled;\n    const isModuleEnabled = breakpointParams[prop] && breakpointParams[prop].enabled;\n    if (wasModuleEnabled && !isModuleEnabled) {\n      swiper[prop].disable();\n    }\n    if (!wasModuleEnabled && isModuleEnabled) {\n      swiper[prop].enable();\n    }\n  });\n  const directionChanged = breakpointParams.direction && breakpointParams.direction !== params.direction;\n  const needsReLoop = params.loop && (breakpointParams.slidesPerView !== params.slidesPerView || directionChanged);\n  const wasLoop = params.loop;\n  if (directionChanged && initialized) {\n    swiper.changeDirection();\n  }\n  extend(swiper.params, breakpointParams);\n  const isEnabled = swiper.params.enabled;\n  const hasLoop = swiper.params.loop;\n  Object.assign(swiper, {\n    allowTouchMove: swiper.params.allowTouchMove,\n    allowSlideNext: swiper.params.allowSlideNext,\n    allowSlidePrev: swiper.params.allowSlidePrev\n  });\n  if (wasEnabled && !isEnabled) {\n    swiper.disable();\n  } else if (!wasEnabled && isEnabled) {\n    swiper.enable();\n  }\n  swiper.currentBreakpoint = breakpoint;\n  swiper.emit('_beforeBreakpoint', breakpointParams);\n  if (initialized) {\n    if (needsReLoop) {\n      swiper.loopDestroy();\n      swiper.loopCreate(realIndex);\n      swiper.updateSlides();\n    } else if (!wasLoop && hasLoop) {\n      swiper.loopCreate(realIndex);\n      swiper.updateSlides();\n    } else if (wasLoop && !hasLoop) {\n      swiper.loopDestroy();\n    }\n  }\n  swiper.emit('breakpoint', breakpointParams);\n}\n\nfunction getBreakpoint(breakpoints, base, containerEl) {\n  if (base === void 0) {\n    base = 'window';\n  }\n  if (!breakpoints || base === 'container' && !containerEl) return undefined;\n  let breakpoint = false;\n  const window = getWindow();\n  const currentHeight = base === 'window' ? window.innerHeight : containerEl.clientHeight;\n  const points = Object.keys(breakpoints).map(point => {\n    if (typeof point === 'string' && point.indexOf('@') === 0) {\n      const minRatio = parseFloat(point.substr(1));\n      const value = currentHeight * minRatio;\n      return {\n        value,\n        point\n      };\n    }\n    return {\n      value: point,\n      point\n    };\n  });\n  points.sort((a, b) => parseInt(a.value, 10) - parseInt(b.value, 10));\n  for (let i = 0; i < points.length; i += 1) {\n    const {\n      point,\n      value\n    } = points[i];\n    if (base === 'window') {\n      if (window.matchMedia(`(min-width: ${value}px)`).matches) {\n        breakpoint = point;\n      }\n    } else if (value <= containerEl.clientWidth) {\n      breakpoint = point;\n    }\n  }\n  return breakpoint || 'max';\n}\n\nvar breakpoints = {\n  setBreakpoint,\n  getBreakpoint\n};\n\nfunction prepareClasses(entries, prefix) {\n  const resultClasses = [];\n  entries.forEach(item => {\n    if (typeof item === 'object') {\n      Object.keys(item).forEach(classNames => {\n        if (item[classNames]) {\n          resultClasses.push(prefix + classNames);\n        }\n      });\n    } else if (typeof item === 'string') {\n      resultClasses.push(prefix + item);\n    }\n  });\n  return resultClasses;\n}\nfunction addClasses() {\n  const swiper = this;\n  const {\n    classNames,\n    params,\n    rtl,\n    el,\n    device\n  } = swiper;\n  // prettier-ignore\n  const suffixes = prepareClasses(['initialized', params.direction, {\n    'free-mode': swiper.params.freeMode && params.freeMode.enabled\n  }, {\n    'autoheight': params.autoHeight\n  }, {\n    'rtl': rtl\n  }, {\n    'grid': params.grid && params.grid.rows > 1\n  }, {\n    'grid-column': params.grid && params.grid.rows > 1 && params.grid.fill === 'column'\n  }, {\n    'android': device.android\n  }, {\n    'ios': device.ios\n  }, {\n    'css-mode': params.cssMode\n  }, {\n    'centered': params.cssMode && params.centeredSlides\n  }, {\n    'watch-progress': params.watchSlidesProgress\n  }], params.containerModifierClass);\n  classNames.push(...suffixes);\n  el.classList.add(...classNames);\n  swiper.emitContainerClasses();\n}\n\nfunction removeClasses() {\n  const swiper = this;\n  const {\n    el,\n    classNames\n  } = swiper;\n  if (!el || typeof el === 'string') return;\n  el.classList.remove(...classNames);\n  swiper.emitContainerClasses();\n}\n\nvar classes = {\n  addClasses,\n  removeClasses\n};\n\nfunction checkOverflow() {\n  const swiper = this;\n  const {\n    isLocked: wasLocked,\n    params\n  } = swiper;\n  const {\n    slidesOffsetBefore\n  } = params;\n  if (slidesOffsetBefore) {\n    const lastSlideIndex = swiper.slides.length - 1;\n    const lastSlideRightEdge = swiper.slidesGrid[lastSlideIndex] + swiper.slidesSizesGrid[lastSlideIndex] + slidesOffsetBefore * 2;\n    swiper.isLocked = swiper.size > lastSlideRightEdge;\n  } else {\n    swiper.isLocked = swiper.snapGrid.length === 1;\n  }\n  if (params.allowSlideNext === true) {\n    swiper.allowSlideNext = !swiper.isLocked;\n  }\n  if (params.allowSlidePrev === true) {\n    swiper.allowSlidePrev = !swiper.isLocked;\n  }\n  if (wasLocked && wasLocked !== swiper.isLocked) {\n    swiper.isEnd = false;\n  }\n  if (wasLocked !== swiper.isLocked) {\n    swiper.emit(swiper.isLocked ? 'lock' : 'unlock');\n  }\n}\nvar checkOverflow$1 = {\n  checkOverflow\n};\n\nvar defaults = {\n  init: true,\n  direction: 'horizontal',\n  oneWayMovement: false,\n  swiperElementNodeName: 'SWIPER-CONTAINER',\n  touchEventsTarget: 'wrapper',\n  initialSlide: 0,\n  speed: 300,\n  cssMode: false,\n  updateOnWindowResize: true,\n  resizeObserver: true,\n  nested: false,\n  createElements: false,\n  eventsPrefix: 'swiper',\n  enabled: true,\n  focusableElements: 'input, select, option, textarea, button, video, label',\n  // Overrides\n  width: null,\n  height: null,\n  //\n  preventInteractionOnTransition: false,\n  // ssr\n  userAgent: null,\n  url: null,\n  // To support iOS's swipe-to-go-back gesture (when being used in-app).\n  edgeSwipeDetection: false,\n  edgeSwipeThreshold: 20,\n  // Autoheight\n  autoHeight: false,\n  // Set wrapper width\n  setWrapperSize: false,\n  // Virtual Translate\n  virtualTranslate: false,\n  // Effects\n  effect: 'slide',\n  // 'slide' or 'fade' or 'cube' or 'coverflow' or 'flip'\n\n  // Breakpoints\n  breakpoints: undefined,\n  breakpointsBase: 'window',\n  // Slides grid\n  spaceBetween: 0,\n  slidesPerView: 1,\n  slidesPerGroup: 1,\n  slidesPerGroupSkip: 0,\n  slidesPerGroupAuto: false,\n  centeredSlides: false,\n  centeredSlidesBounds: false,\n  slidesOffsetBefore: 0,\n  // in px\n  slidesOffsetAfter: 0,\n  // in px\n  normalizeSlideIndex: true,\n  centerInsufficientSlides: false,\n  // Disable swiper and hide navigation when container not overflow\n  watchOverflow: true,\n  // Round length\n  roundLengths: false,\n  // Touches\n  touchRatio: 1,\n  touchAngle: 45,\n  simulateTouch: true,\n  shortSwipes: true,\n  longSwipes: true,\n  longSwipesRatio: 0.5,\n  longSwipesMs: 300,\n  followFinger: true,\n  allowTouchMove: true,\n  threshold: 5,\n  touchMoveStopPropagation: false,\n  touchStartPreventDefault: true,\n  touchStartForcePreventDefault: false,\n  touchReleaseOnEdges: false,\n  // Unique Navigation Elements\n  uniqueNavElements: true,\n  // Resistance\n  resistance: true,\n  resistanceRatio: 0.85,\n  // Progress\n  watchSlidesProgress: false,\n  // Cursor\n  grabCursor: false,\n  // Clicks\n  preventClicks: true,\n  preventClicksPropagation: true,\n  slideToClickedSlide: false,\n  // loop\n  loop: false,\n  loopAddBlankSlides: true,\n  loopAdditionalSlides: 0,\n  loopPreventsSliding: true,\n  // rewind\n  rewind: false,\n  // Swiping/no swiping\n  allowSlidePrev: true,\n  allowSlideNext: true,\n  swipeHandler: null,\n  // '.swipe-handler',\n  noSwiping: true,\n  noSwipingClass: 'swiper-no-swiping',\n  noSwipingSelector: null,\n  // Passive Listeners\n  passiveListeners: true,\n  maxBackfaceHiddenSlides: 10,\n  // NS\n  containerModifierClass: 'swiper-',\n  // NEW\n  slideClass: 'swiper-slide',\n  slideBlankClass: 'swiper-slide-blank',\n  slideActiveClass: 'swiper-slide-active',\n  slideVisibleClass: 'swiper-slide-visible',\n  slideFullyVisibleClass: 'swiper-slide-fully-visible',\n  slideNextClass: 'swiper-slide-next',\n  slidePrevClass: 'swiper-slide-prev',\n  wrapperClass: 'swiper-wrapper',\n  lazyPreloaderClass: 'swiper-lazy-preloader',\n  lazyPreloadPrevNext: 0,\n  // Callbacks\n  runCallbacksOnInit: true,\n  // Internals\n  _emitClasses: false\n};\n\nfunction moduleExtendParams(params, allModulesParams) {\n  return function extendParams(obj) {\n    if (obj === void 0) {\n      obj = {};\n    }\n    const moduleParamName = Object.keys(obj)[0];\n    const moduleParams = obj[moduleParamName];\n    if (typeof moduleParams !== 'object' || moduleParams === null) {\n      extend(allModulesParams, obj);\n      return;\n    }\n    if (params[moduleParamName] === true) {\n      params[moduleParamName] = {\n        enabled: true\n      };\n    }\n    if (moduleParamName === 'navigation' && params[moduleParamName] && params[moduleParamName].enabled && !params[moduleParamName].prevEl && !params[moduleParamName].nextEl) {\n      params[moduleParamName].auto = true;\n    }\n    if (['pagination', 'scrollbar'].indexOf(moduleParamName) >= 0 && params[moduleParamName] && params[moduleParamName].enabled && !params[moduleParamName].el) {\n      params[moduleParamName].auto = true;\n    }\n    if (!(moduleParamName in params && 'enabled' in moduleParams)) {\n      extend(allModulesParams, obj);\n      return;\n    }\n    if (typeof params[moduleParamName] === 'object' && !('enabled' in params[moduleParamName])) {\n      params[moduleParamName].enabled = true;\n    }\n    if (!params[moduleParamName]) params[moduleParamName] = {\n      enabled: false\n    };\n    extend(allModulesParams, obj);\n  };\n}\n\n/* eslint no-param-reassign: \"off\" */\nconst prototypes = {\n  eventsEmitter,\n  update,\n  translate,\n  transition,\n  slide,\n  loop,\n  grabCursor,\n  events: events$1,\n  breakpoints,\n  checkOverflow: checkOverflow$1,\n  classes\n};\nconst extendedDefaults = {};\nclass Swiper {\n  constructor() {\n    let el;\n    let params;\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    if (args.length === 1 && args[0].constructor && Object.prototype.toString.call(args[0]).slice(8, -1) === 'Object') {\n      params = args[0];\n    } else {\n      [el, params] = args;\n    }\n    if (!params) params = {};\n    params = extend({}, params);\n    if (el && !params.el) params.el = el;\n    const document = getDocument();\n    if (params.el && typeof params.el === 'string' && document.querySelectorAll(params.el).length > 1) {\n      const swipers = [];\n      document.querySelectorAll(params.el).forEach(containerEl => {\n        const newParams = extend({}, params, {\n          el: containerEl\n        });\n        swipers.push(new Swiper(newParams));\n      });\n      // eslint-disable-next-line no-constructor-return\n      return swipers;\n    }\n\n    // Swiper Instance\n    const swiper = this;\n    swiper.__swiper__ = true;\n    swiper.support = getSupport();\n    swiper.device = getDevice({\n      userAgent: params.userAgent\n    });\n    swiper.browser = getBrowser();\n    swiper.eventsListeners = {};\n    swiper.eventsAnyListeners = [];\n    swiper.modules = [...swiper.__modules__];\n    if (params.modules && Array.isArray(params.modules)) {\n      swiper.modules.push(...params.modules);\n    }\n    const allModulesParams = {};\n    swiper.modules.forEach(mod => {\n      mod({\n        params,\n        swiper,\n        extendParams: moduleExtendParams(params, allModulesParams),\n        on: swiper.on.bind(swiper),\n        once: swiper.once.bind(swiper),\n        off: swiper.off.bind(swiper),\n        emit: swiper.emit.bind(swiper)\n      });\n    });\n\n    // Extend defaults with modules params\n    const swiperParams = extend({}, defaults, allModulesParams);\n\n    // Extend defaults with passed params\n    swiper.params = extend({}, swiperParams, extendedDefaults, params);\n    swiper.originalParams = extend({}, swiper.params);\n    swiper.passedParams = extend({}, params);\n\n    // add event listeners\n    if (swiper.params && swiper.params.on) {\n      Object.keys(swiper.params.on).forEach(eventName => {\n        swiper.on(eventName, swiper.params.on[eventName]);\n      });\n    }\n    if (swiper.params && swiper.params.onAny) {\n      swiper.onAny(swiper.params.onAny);\n    }\n\n    // Extend Swiper\n    Object.assign(swiper, {\n      enabled: swiper.params.enabled,\n      el,\n      // Classes\n      classNames: [],\n      // Slides\n      slides: [],\n      slidesGrid: [],\n      snapGrid: [],\n      slidesSizesGrid: [],\n      // isDirection\n      isHorizontal() {\n        return swiper.params.direction === 'horizontal';\n      },\n      isVertical() {\n        return swiper.params.direction === 'vertical';\n      },\n      // Indexes\n      activeIndex: 0,\n      realIndex: 0,\n      //\n      isBeginning: true,\n      isEnd: false,\n      // Props\n      translate: 0,\n      previousTranslate: 0,\n      progress: 0,\n      velocity: 0,\n      animating: false,\n      cssOverflowAdjustment() {\n        // Returns 0 unless `translate` is > 2**23\n        // Should be subtracted from css values to prevent overflow\n        return Math.trunc(this.translate / 2 ** 23) * 2 ** 23;\n      },\n      // Locks\n      allowSlideNext: swiper.params.allowSlideNext,\n      allowSlidePrev: swiper.params.allowSlidePrev,\n      // Touch Events\n      touchEventsData: {\n        isTouched: undefined,\n        isMoved: undefined,\n        allowTouchCallbacks: undefined,\n        touchStartTime: undefined,\n        isScrolling: undefined,\n        currentTranslate: undefined,\n        startTranslate: undefined,\n        allowThresholdMove: undefined,\n        // Form elements to match\n        focusableElements: swiper.params.focusableElements,\n        // Last click time\n        lastClickTime: 0,\n        clickTimeout: undefined,\n        // Velocities\n        velocities: [],\n        allowMomentumBounce: undefined,\n        startMoving: undefined,\n        pointerId: null,\n        touchId: null\n      },\n      // Clicks\n      allowClick: true,\n      // Touches\n      allowTouchMove: swiper.params.allowTouchMove,\n      touches: {\n        startX: 0,\n        startY: 0,\n        currentX: 0,\n        currentY: 0,\n        diff: 0\n      },\n      // Images\n      imagesToLoad: [],\n      imagesLoaded: 0\n    });\n    swiper.emit('_swiper');\n\n    // Init\n    if (swiper.params.init) {\n      swiper.init();\n    }\n\n    // Return app instance\n    // eslint-disable-next-line no-constructor-return\n    return swiper;\n  }\n  getDirectionLabel(property) {\n    if (this.isHorizontal()) {\n      return property;\n    }\n    // prettier-ignore\n    return {\n      'width': 'height',\n      'margin-top': 'margin-left',\n      'margin-bottom ': 'margin-right',\n      'margin-left': 'margin-top',\n      'margin-right': 'margin-bottom',\n      'padding-left': 'padding-top',\n      'padding-right': 'padding-bottom',\n      'marginRight': 'marginBottom'\n    }[property];\n  }\n  getSlideIndex(slideEl) {\n    const {\n      slidesEl,\n      params\n    } = this;\n    const slides = elementChildren(slidesEl, `.${params.slideClass}, swiper-slide`);\n    const firstSlideIndex = elementIndex(slides[0]);\n    return elementIndex(slideEl) - firstSlideIndex;\n  }\n  getSlideIndexByData(index) {\n    return this.getSlideIndex(this.slides.find(slideEl => slideEl.getAttribute('data-swiper-slide-index') * 1 === index));\n  }\n  getSlideIndexWhenGrid(index) {\n    if (this.grid && this.params.grid && this.params.grid.rows > 1) {\n      if (this.params.grid.fill === 'column') {\n        index = Math.floor(index / this.params.grid.rows);\n      } else if (this.params.grid.fill === 'row') {\n        index = index % Math.ceil(this.slides.length / this.params.grid.rows);\n      }\n    }\n    return index;\n  }\n  recalcSlides() {\n    const swiper = this;\n    const {\n      slidesEl,\n      params\n    } = swiper;\n    swiper.slides = elementChildren(slidesEl, `.${params.slideClass}, swiper-slide`);\n  }\n  enable() {\n    const swiper = this;\n    if (swiper.enabled) return;\n    swiper.enabled = true;\n    if (swiper.params.grabCursor) {\n      swiper.setGrabCursor();\n    }\n    swiper.emit('enable');\n  }\n  disable() {\n    const swiper = this;\n    if (!swiper.enabled) return;\n    swiper.enabled = false;\n    if (swiper.params.grabCursor) {\n      swiper.unsetGrabCursor();\n    }\n    swiper.emit('disable');\n  }\n  setProgress(progress, speed) {\n    const swiper = this;\n    progress = Math.min(Math.max(progress, 0), 1);\n    const min = swiper.minTranslate();\n    const max = swiper.maxTranslate();\n    const current = (max - min) * progress + min;\n    swiper.translateTo(current, typeof speed === 'undefined' ? 0 : speed);\n    swiper.updateActiveIndex();\n    swiper.updateSlidesClasses();\n  }\n  emitContainerClasses() {\n    const swiper = this;\n    if (!swiper.params._emitClasses || !swiper.el) return;\n    const cls = swiper.el.className.split(' ').filter(className => {\n      return className.indexOf('swiper') === 0 || className.indexOf(swiper.params.containerModifierClass) === 0;\n    });\n    swiper.emit('_containerClasses', cls.join(' '));\n  }\n  getSlideClasses(slideEl) {\n    const swiper = this;\n    if (swiper.destroyed) return '';\n    return slideEl.className.split(' ').filter(className => {\n      return className.indexOf('swiper-slide') === 0 || className.indexOf(swiper.params.slideClass) === 0;\n    }).join(' ');\n  }\n  emitSlidesClasses() {\n    const swiper = this;\n    if (!swiper.params._emitClasses || !swiper.el) return;\n    const updates = [];\n    swiper.slides.forEach(slideEl => {\n      const classNames = swiper.getSlideClasses(slideEl);\n      updates.push({\n        slideEl,\n        classNames\n      });\n      swiper.emit('_slideClass', slideEl, classNames);\n    });\n    swiper.emit('_slideClasses', updates);\n  }\n  slidesPerViewDynamic(view, exact) {\n    if (view === void 0) {\n      view = 'current';\n    }\n    if (exact === void 0) {\n      exact = false;\n    }\n    const swiper = this;\n    const {\n      params,\n      slides,\n      slidesGrid,\n      slidesSizesGrid,\n      size: swiperSize,\n      activeIndex\n    } = swiper;\n    let spv = 1;\n    if (typeof params.slidesPerView === 'number') return params.slidesPerView;\n    if (params.centeredSlides) {\n      let slideSize = slides[activeIndex] ? Math.ceil(slides[activeIndex].swiperSlideSize) : 0;\n      let breakLoop;\n      for (let i = activeIndex + 1; i < slides.length; i += 1) {\n        if (slides[i] && !breakLoop) {\n          slideSize += Math.ceil(slides[i].swiperSlideSize);\n          spv += 1;\n          if (slideSize > swiperSize) breakLoop = true;\n        }\n      }\n      for (let i = activeIndex - 1; i >= 0; i -= 1) {\n        if (slides[i] && !breakLoop) {\n          slideSize += slides[i].swiperSlideSize;\n          spv += 1;\n          if (slideSize > swiperSize) breakLoop = true;\n        }\n      }\n    } else {\n      // eslint-disable-next-line\n      if (view === 'current') {\n        for (let i = activeIndex + 1; i < slides.length; i += 1) {\n          const slideInView = exact ? slidesGrid[i] + slidesSizesGrid[i] - slidesGrid[activeIndex] < swiperSize : slidesGrid[i] - slidesGrid[activeIndex] < swiperSize;\n          if (slideInView) {\n            spv += 1;\n          }\n        }\n      } else {\n        // previous\n        for (let i = activeIndex - 1; i >= 0; i -= 1) {\n          const slideInView = slidesGrid[activeIndex] - slidesGrid[i] < swiperSize;\n          if (slideInView) {\n            spv += 1;\n          }\n        }\n      }\n    }\n    return spv;\n  }\n  update() {\n    const swiper = this;\n    if (!swiper || swiper.destroyed) return;\n    const {\n      snapGrid,\n      params\n    } = swiper;\n    // Breakpoints\n    if (params.breakpoints) {\n      swiper.setBreakpoint();\n    }\n    [...swiper.el.querySelectorAll('[loading=\"lazy\"]')].forEach(imageEl => {\n      if (imageEl.complete) {\n        processLazyPreloader(swiper, imageEl);\n      }\n    });\n    swiper.updateSize();\n    swiper.updateSlides();\n    swiper.updateProgress();\n    swiper.updateSlidesClasses();\n    function setTranslate() {\n      const translateValue = swiper.rtlTranslate ? swiper.translate * -1 : swiper.translate;\n      const newTranslate = Math.min(Math.max(translateValue, swiper.maxTranslate()), swiper.minTranslate());\n      swiper.setTranslate(newTranslate);\n      swiper.updateActiveIndex();\n      swiper.updateSlidesClasses();\n    }\n    let translated;\n    if (params.freeMode && params.freeMode.enabled && !params.cssMode) {\n      setTranslate();\n      if (params.autoHeight) {\n        swiper.updateAutoHeight();\n      }\n    } else {\n      if ((params.slidesPerView === 'auto' || params.slidesPerView > 1) && swiper.isEnd && !params.centeredSlides) {\n        const slides = swiper.virtual && params.virtual.enabled ? swiper.virtual.slides : swiper.slides;\n        translated = swiper.slideTo(slides.length - 1, 0, false, true);\n      } else {\n        translated = swiper.slideTo(swiper.activeIndex, 0, false, true);\n      }\n      if (!translated) {\n        setTranslate();\n      }\n    }\n    if (params.watchOverflow && snapGrid !== swiper.snapGrid) {\n      swiper.checkOverflow();\n    }\n    swiper.emit('update');\n  }\n  changeDirection(newDirection, needUpdate) {\n    if (needUpdate === void 0) {\n      needUpdate = true;\n    }\n    const swiper = this;\n    const currentDirection = swiper.params.direction;\n    if (!newDirection) {\n      // eslint-disable-next-line\n      newDirection = currentDirection === 'horizontal' ? 'vertical' : 'horizontal';\n    }\n    if (newDirection === currentDirection || newDirection !== 'horizontal' && newDirection !== 'vertical') {\n      return swiper;\n    }\n    swiper.el.classList.remove(`${swiper.params.containerModifierClass}${currentDirection}`);\n    swiper.el.classList.add(`${swiper.params.containerModifierClass}${newDirection}`);\n    swiper.emitContainerClasses();\n    swiper.params.direction = newDirection;\n    swiper.slides.forEach(slideEl => {\n      if (newDirection === 'vertical') {\n        slideEl.style.width = '';\n      } else {\n        slideEl.style.height = '';\n      }\n    });\n    swiper.emit('changeDirection');\n    if (needUpdate) swiper.update();\n    return swiper;\n  }\n  changeLanguageDirection(direction) {\n    const swiper = this;\n    if (swiper.rtl && direction === 'rtl' || !swiper.rtl && direction === 'ltr') return;\n    swiper.rtl = direction === 'rtl';\n    swiper.rtlTranslate = swiper.params.direction === 'horizontal' && swiper.rtl;\n    if (swiper.rtl) {\n      swiper.el.classList.add(`${swiper.params.containerModifierClass}rtl`);\n      swiper.el.dir = 'rtl';\n    } else {\n      swiper.el.classList.remove(`${swiper.params.containerModifierClass}rtl`);\n      swiper.el.dir = 'ltr';\n    }\n    swiper.update();\n  }\n  mount(element) {\n    const swiper = this;\n    if (swiper.mounted) return true;\n\n    // Find el\n    let el = element || swiper.params.el;\n    if (typeof el === 'string') {\n      el = document.querySelector(el);\n    }\n    if (!el) {\n      return false;\n    }\n    el.swiper = swiper;\n    if (el.parentNode && el.parentNode.host && el.parentNode.host.nodeName === swiper.params.swiperElementNodeName.toUpperCase()) {\n      swiper.isElement = true;\n    }\n    const getWrapperSelector = () => {\n      return `.${(swiper.params.wrapperClass || '').trim().split(' ').join('.')}`;\n    };\n    const getWrapper = () => {\n      if (el && el.shadowRoot && el.shadowRoot.querySelector) {\n        const res = el.shadowRoot.querySelector(getWrapperSelector());\n        // Children needs to return slot items\n        return res;\n      }\n      return elementChildren(el, getWrapperSelector())[0];\n    };\n    // Find Wrapper\n    let wrapperEl = getWrapper();\n    if (!wrapperEl && swiper.params.createElements) {\n      wrapperEl = createElement('div', swiper.params.wrapperClass);\n      el.append(wrapperEl);\n      elementChildren(el, `.${swiper.params.slideClass}`).forEach(slideEl => {\n        wrapperEl.append(slideEl);\n      });\n    }\n    Object.assign(swiper, {\n      el,\n      wrapperEl,\n      slidesEl: swiper.isElement && !el.parentNode.host.slideSlots ? el.parentNode.host : wrapperEl,\n      hostEl: swiper.isElement ? el.parentNode.host : el,\n      mounted: true,\n      // RTL\n      rtl: el.dir.toLowerCase() === 'rtl' || elementStyle(el, 'direction') === 'rtl',\n      rtlTranslate: swiper.params.direction === 'horizontal' && (el.dir.toLowerCase() === 'rtl' || elementStyle(el, 'direction') === 'rtl'),\n      wrongRTL: elementStyle(wrapperEl, 'display') === '-webkit-box'\n    });\n    return true;\n  }\n  init(el) {\n    const swiper = this;\n    if (swiper.initialized) return swiper;\n    const mounted = swiper.mount(el);\n    if (mounted === false) return swiper;\n    swiper.emit('beforeInit');\n\n    // Set breakpoint\n    if (swiper.params.breakpoints) {\n      swiper.setBreakpoint();\n    }\n\n    // Add Classes\n    swiper.addClasses();\n\n    // Update size\n    swiper.updateSize();\n\n    // Update slides\n    swiper.updateSlides();\n    if (swiper.params.watchOverflow) {\n      swiper.checkOverflow();\n    }\n\n    // Set Grab Cursor\n    if (swiper.params.grabCursor && swiper.enabled) {\n      swiper.setGrabCursor();\n    }\n\n    // Slide To Initial Slide\n    if (swiper.params.loop && swiper.virtual && swiper.params.virtual.enabled) {\n      swiper.slideTo(swiper.params.initialSlide + swiper.virtual.slidesBefore, 0, swiper.params.runCallbacksOnInit, false, true);\n    } else {\n      swiper.slideTo(swiper.params.initialSlide, 0, swiper.params.runCallbacksOnInit, false, true);\n    }\n\n    // Create loop\n    if (swiper.params.loop) {\n      swiper.loopCreate(undefined, true);\n    }\n\n    // Attach events\n    swiper.attachEvents();\n    const lazyElements = [...swiper.el.querySelectorAll('[loading=\"lazy\"]')];\n    if (swiper.isElement) {\n      lazyElements.push(...swiper.hostEl.querySelectorAll('[loading=\"lazy\"]'));\n    }\n    lazyElements.forEach(imageEl => {\n      if (imageEl.complete) {\n        processLazyPreloader(swiper, imageEl);\n      } else {\n        imageEl.addEventListener('load', e => {\n          processLazyPreloader(swiper, e.target);\n        });\n      }\n    });\n    preload(swiper);\n\n    // Init Flag\n    swiper.initialized = true;\n    preload(swiper);\n\n    // Emit\n    swiper.emit('init');\n    swiper.emit('afterInit');\n    return swiper;\n  }\n  destroy(deleteInstance, cleanStyles) {\n    if (deleteInstance === void 0) {\n      deleteInstance = true;\n    }\n    if (cleanStyles === void 0) {\n      cleanStyles = true;\n    }\n    const swiper = this;\n    const {\n      params,\n      el,\n      wrapperEl,\n      slides\n    } = swiper;\n    if (typeof swiper.params === 'undefined' || swiper.destroyed) {\n      return null;\n    }\n    swiper.emit('beforeDestroy');\n\n    // Init Flag\n    swiper.initialized = false;\n\n    // Detach events\n    swiper.detachEvents();\n\n    // Destroy loop\n    if (params.loop) {\n      swiper.loopDestroy();\n    }\n\n    // Cleanup styles\n    if (cleanStyles) {\n      swiper.removeClasses();\n      if (el && typeof el !== 'string') {\n        el.removeAttribute('style');\n      }\n      if (wrapperEl) {\n        wrapperEl.removeAttribute('style');\n      }\n      if (slides && slides.length) {\n        slides.forEach(slideEl => {\n          slideEl.classList.remove(params.slideVisibleClass, params.slideFullyVisibleClass, params.slideActiveClass, params.slideNextClass, params.slidePrevClass);\n          slideEl.removeAttribute('style');\n          slideEl.removeAttribute('data-swiper-slide-index');\n        });\n      }\n    }\n    swiper.emit('destroy');\n\n    // Detach emitter events\n    Object.keys(swiper.eventsListeners).forEach(eventName => {\n      swiper.off(eventName);\n    });\n    if (deleteInstance !== false) {\n      if (swiper.el && typeof swiper.el !== 'string') {\n        swiper.el.swiper = null;\n      }\n      deleteProps(swiper);\n    }\n    swiper.destroyed = true;\n    return null;\n  }\n  static extendDefaults(newDefaults) {\n    extend(extendedDefaults, newDefaults);\n  }\n  static get extendedDefaults() {\n    return extendedDefaults;\n  }\n  static get defaults() {\n    return defaults;\n  }\n  static installModule(mod) {\n    if (!Swiper.prototype.__modules__) Swiper.prototype.__modules__ = [];\n    const modules = Swiper.prototype.__modules__;\n    if (typeof mod === 'function' && modules.indexOf(mod) < 0) {\n      modules.push(mod);\n    }\n  }\n  static use(module) {\n    if (Array.isArray(module)) {\n      module.forEach(m => Swiper.installModule(m));\n      return Swiper;\n    }\n    Swiper.installModule(module);\n    return Swiper;\n  }\n}\nObject.keys(prototypes).forEach(prototypeGroup => {\n  Object.keys(prototypes[prototypeGroup]).forEach(protoMethod => {\n    Swiper.prototype[protoMethod] = prototypes[prototypeGroup][protoMethod];\n  });\n});\nSwiper.use([Resize, Observer]);\n\nexport { Swiper as S, defaults as d };\n", "import { g as getDocument } from '../shared/ssr-window.esm.mjs';\n\n/* eslint no-underscore-dangle: \"off\" */\n/* eslint no-use-before-define: \"off\" */\nfunction Autoplay(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on,\n    emit,\n    params\n  } = _ref;\n  swiper.autoplay = {\n    running: false,\n    paused: false,\n    timeLeft: 0\n  };\n  extendParams({\n    autoplay: {\n      enabled: false,\n      delay: 3000,\n      waitForTransition: true,\n      disableOnInteraction: false,\n      stopOnLastSlide: false,\n      reverseDirection: false,\n      pauseOnMouseEnter: false\n    }\n  });\n  let timeout;\n  let raf;\n  let autoplayDelayTotal = params && params.autoplay ? params.autoplay.delay : 3000;\n  let autoplayDelayCurrent = params && params.autoplay ? params.autoplay.delay : 3000;\n  let autoplayTimeLeft;\n  let autoplayStartTime = new Date().getTime();\n  let wasPaused;\n  let isTouched;\n  let pausedByTouch;\n  let touchStartTimeout;\n  let slideChanged;\n  let pausedByInteraction;\n  let pausedByPointerEnter;\n  function onTransitionEnd(e) {\n    if (!swiper || swiper.destroyed || !swiper.wrapperEl) return;\n    if (e.target !== swiper.wrapperEl) return;\n    swiper.wrapperEl.removeEventListener('transitionend', onTransitionEnd);\n    if (pausedByPointerEnter || e.detail && e.detail.bySwiperTouchMove) {\n      return;\n    }\n    resume();\n  }\n  const calcTimeLeft = () => {\n    if (swiper.destroyed || !swiper.autoplay.running) return;\n    if (swiper.autoplay.paused) {\n      wasPaused = true;\n    } else if (wasPaused) {\n      autoplayDelayCurrent = autoplayTimeLeft;\n      wasPaused = false;\n    }\n    const timeLeft = swiper.autoplay.paused ? autoplayTimeLeft : autoplayStartTime + autoplayDelayCurrent - new Date().getTime();\n    swiper.autoplay.timeLeft = timeLeft;\n    emit('autoplayTimeLeft', timeLeft, timeLeft / autoplayDelayTotal);\n    raf = requestAnimationFrame(() => {\n      calcTimeLeft();\n    });\n  };\n  const getSlideDelay = () => {\n    let activeSlideEl;\n    if (swiper.virtual && swiper.params.virtual.enabled) {\n      activeSlideEl = swiper.slides.find(slideEl => slideEl.classList.contains('swiper-slide-active'));\n    } else {\n      activeSlideEl = swiper.slides[swiper.activeIndex];\n    }\n    if (!activeSlideEl) return undefined;\n    const currentSlideDelay = parseInt(activeSlideEl.getAttribute('data-swiper-autoplay'), 10);\n    return currentSlideDelay;\n  };\n  const run = delayForce => {\n    if (swiper.destroyed || !swiper.autoplay.running) return;\n    cancelAnimationFrame(raf);\n    calcTimeLeft();\n    let delay = typeof delayForce === 'undefined' ? swiper.params.autoplay.delay : delayForce;\n    autoplayDelayTotal = swiper.params.autoplay.delay;\n    autoplayDelayCurrent = swiper.params.autoplay.delay;\n    const currentSlideDelay = getSlideDelay();\n    if (!Number.isNaN(currentSlideDelay) && currentSlideDelay > 0 && typeof delayForce === 'undefined') {\n      delay = currentSlideDelay;\n      autoplayDelayTotal = currentSlideDelay;\n      autoplayDelayCurrent = currentSlideDelay;\n    }\n    autoplayTimeLeft = delay;\n    const speed = swiper.params.speed;\n    const proceed = () => {\n      if (!swiper || swiper.destroyed) return;\n      if (swiper.params.autoplay.reverseDirection) {\n        if (!swiper.isBeginning || swiper.params.loop || swiper.params.rewind) {\n          swiper.slidePrev(speed, true, true);\n          emit('autoplay');\n        } else if (!swiper.params.autoplay.stopOnLastSlide) {\n          swiper.slideTo(swiper.slides.length - 1, speed, true, true);\n          emit('autoplay');\n        }\n      } else {\n        if (!swiper.isEnd || swiper.params.loop || swiper.params.rewind) {\n          swiper.slideNext(speed, true, true);\n          emit('autoplay');\n        } else if (!swiper.params.autoplay.stopOnLastSlide) {\n          swiper.slideTo(0, speed, true, true);\n          emit('autoplay');\n        }\n      }\n      if (swiper.params.cssMode) {\n        autoplayStartTime = new Date().getTime();\n        requestAnimationFrame(() => {\n          run();\n        });\n      }\n    };\n    if (delay > 0) {\n      clearTimeout(timeout);\n      timeout = setTimeout(() => {\n        proceed();\n      }, delay);\n    } else {\n      requestAnimationFrame(() => {\n        proceed();\n      });\n    }\n\n    // eslint-disable-next-line\n    return delay;\n  };\n  const start = () => {\n    autoplayStartTime = new Date().getTime();\n    swiper.autoplay.running = true;\n    run();\n    emit('autoplayStart');\n  };\n  const stop = () => {\n    swiper.autoplay.running = false;\n    clearTimeout(timeout);\n    cancelAnimationFrame(raf);\n    emit('autoplayStop');\n  };\n  const pause = (internal, reset) => {\n    if (swiper.destroyed || !swiper.autoplay.running) return;\n    clearTimeout(timeout);\n    if (!internal) {\n      pausedByInteraction = true;\n    }\n    const proceed = () => {\n      emit('autoplayPause');\n      if (swiper.params.autoplay.waitForTransition) {\n        swiper.wrapperEl.addEventListener('transitionend', onTransitionEnd);\n      } else {\n        resume();\n      }\n    };\n    swiper.autoplay.paused = true;\n    if (reset) {\n      if (slideChanged) {\n        autoplayTimeLeft = swiper.params.autoplay.delay;\n      }\n      slideChanged = false;\n      proceed();\n      return;\n    }\n    const delay = autoplayTimeLeft || swiper.params.autoplay.delay;\n    autoplayTimeLeft = delay - (new Date().getTime() - autoplayStartTime);\n    if (swiper.isEnd && autoplayTimeLeft < 0 && !swiper.params.loop) return;\n    if (autoplayTimeLeft < 0) autoplayTimeLeft = 0;\n    proceed();\n  };\n  const resume = () => {\n    if (swiper.isEnd && autoplayTimeLeft < 0 && !swiper.params.loop || swiper.destroyed || !swiper.autoplay.running) return;\n    autoplayStartTime = new Date().getTime();\n    if (pausedByInteraction) {\n      pausedByInteraction = false;\n      run(autoplayTimeLeft);\n    } else {\n      run();\n    }\n    swiper.autoplay.paused = false;\n    emit('autoplayResume');\n  };\n  const onVisibilityChange = () => {\n    if (swiper.destroyed || !swiper.autoplay.running) return;\n    const document = getDocument();\n    if (document.visibilityState === 'hidden') {\n      pausedByInteraction = true;\n      pause(true);\n    }\n    if (document.visibilityState === 'visible') {\n      resume();\n    }\n  };\n  const onPointerEnter = e => {\n    if (e.pointerType !== 'mouse') return;\n    pausedByInteraction = true;\n    pausedByPointerEnter = true;\n    if (swiper.animating || swiper.autoplay.paused) return;\n    pause(true);\n  };\n  const onPointerLeave = e => {\n    if (e.pointerType !== 'mouse') return;\n    pausedByPointerEnter = false;\n    if (swiper.autoplay.paused) {\n      resume();\n    }\n  };\n  const attachMouseEvents = () => {\n    if (swiper.params.autoplay.pauseOnMouseEnter) {\n      swiper.el.addEventListener('pointerenter', onPointerEnter);\n      swiper.el.addEventListener('pointerleave', onPointerLeave);\n    }\n  };\n  const detachMouseEvents = () => {\n    if (swiper.el && typeof swiper.el !== 'string') {\n      swiper.el.removeEventListener('pointerenter', onPointerEnter);\n      swiper.el.removeEventListener('pointerleave', onPointerLeave);\n    }\n  };\n  const attachDocumentEvents = () => {\n    const document = getDocument();\n    document.addEventListener('visibilitychange', onVisibilityChange);\n  };\n  const detachDocumentEvents = () => {\n    const document = getDocument();\n    document.removeEventListener('visibilitychange', onVisibilityChange);\n  };\n  on('init', () => {\n    if (swiper.params.autoplay.enabled) {\n      attachMouseEvents();\n      attachDocumentEvents();\n      start();\n    }\n  });\n  on('destroy', () => {\n    detachMouseEvents();\n    detachDocumentEvents();\n    if (swiper.autoplay.running) {\n      stop();\n    }\n  });\n  on('_freeModeStaticRelease', () => {\n    if (pausedByTouch || pausedByInteraction) {\n      resume();\n    }\n  });\n  on('_freeModeNoMomentumRelease', () => {\n    if (!swiper.params.autoplay.disableOnInteraction) {\n      pause(true, true);\n    } else {\n      stop();\n    }\n  });\n  on('beforeTransitionStart', (_s, speed, internal) => {\n    if (swiper.destroyed || !swiper.autoplay.running) return;\n    if (internal || !swiper.params.autoplay.disableOnInteraction) {\n      pause(true, true);\n    } else {\n      stop();\n    }\n  });\n  on('sliderFirstMove', () => {\n    if (swiper.destroyed || !swiper.autoplay.running) return;\n    if (swiper.params.autoplay.disableOnInteraction) {\n      stop();\n      return;\n    }\n    isTouched = true;\n    pausedByTouch = false;\n    pausedByInteraction = false;\n    touchStartTimeout = setTimeout(() => {\n      pausedByInteraction = true;\n      pausedByTouch = true;\n      pause(true);\n    }, 200);\n  });\n  on('touchEnd', () => {\n    if (swiper.destroyed || !swiper.autoplay.running || !isTouched) return;\n    clearTimeout(touchStartTimeout);\n    clearTimeout(timeout);\n    if (swiper.params.autoplay.disableOnInteraction) {\n      pausedByTouch = false;\n      isTouched = false;\n      return;\n    }\n    if (pausedByTouch && swiper.params.cssMode) resume();\n    pausedByTouch = false;\n    isTouched = false;\n  });\n  on('slideChange', () => {\n    if (swiper.destroyed || !swiper.autoplay.running) return;\n    slideChanged = true;\n  });\n  Object.assign(swiper.autoplay, {\n    start,\n    stop,\n    pause,\n    resume\n  });\n}\n\nexport { Autoplay as default };\n", "import type { DOMElementOptions, StylesObject } from '../../common/config/types';\n\n/**\n * DOM utility functions for safe DOM manipulation\n */\n\n/**\n * Safely query a single element\n */\nexport const querySelector = (selector: string): Element | null => {\n    try {\n        return document.querySelector(selector);\n    } catch {\n        return null;\n    }\n};\n\n/**\n * Safely query multiple elements\n */\nexport const querySelectorAll = (selector: string): NodeListOf<Element> => {\n    try {\n        return document.querySelectorAll(selector);\n    } catch {\n        return document.querySelectorAll(''); // Returns empty NodeList\n    }\n};\n\n/**\n * Safely get element by ID\n */\nexport const getElementById = (id: string): HTMLElement | null => {\n    try {\n        return document.getElementById(id);\n    } catch {\n        return null;\n    }\n};\n\n/**\n * Safely create element with options\n */\nexport const createElement = (\n    tagName: string,\n    options: DOMElementOptions = {},\n    innerHTML = ''\n): HTMLElement => {\n    try {\n        const element = document.createElement(tagName);\n        \n        // Set attributes\n        for (const [key, value] of Object.entries(options)) {\n            if (key === 'className') {\n                element.className = String(value);\n            } else if (key === 'id') {\n                element.id = String(value);\n            } else {\n                element.setAttribute(key, String(value));\n            }\n        }\n        \n        if (innerHTML) {\n            element.innerHTML = innerHTML;\n        }\n        \n        return element;\n    } catch {\n        return document.createElement('div'); // Fallback\n    }\n};\n\n/**\n * Safely append child element\n */\nexport const appendChild = (parent: Element, child: Element): void => {\n    try {\n        parent.appendChild(child);\n    } catch {\n        // Silently handle append errors\n    }\n};\n\n/**\n * Safely prepend child element\n */\nexport const prependChild = (parent: Element, child: Element): void => {\n    try {\n        parent.prepend(child);\n    } catch {\n        // Silently handle prepend errors\n    }\n};\n\n/**\n * Safely remove element\n */\nexport const removeElement = (element: Element): void => {\n    try {\n        element.remove();\n    } catch {\n        // Silently handle removal errors\n    }\n};\n\n/**\n * Safely set styles on element\n */\nexport const setStyles = (element: HTMLElement, styles: StylesObject): void => {\n    try {\n        for (const [property, value] of Object.entries(styles)) {\n            element.style.setProperty(property, String(value));\n        }\n    } catch {\n        // Silently handle style errors\n    }\n};\n", "/**\n * Application constants for TagTiles extension\n */\n\n// Mobile detection constants\nexport const MO<PERSON>LE_DETECTION = {\n  USER_AGENT_SUBSTR_START: 0,\n  USER_AGENT_SUBSTR_LENGTH: 4,\n} as const;\n\n// Swiper configuration constants\nexport const SWIPER_CONFIG = {\n  MOBILE: {\n    SPACE_BETWEEN: 80,\n    SLIDES_PER_VIEW: 4,\n  },\n  DESKTOP: {\n    SPACE_BETWEEN: 10,\n    SLIDES_PER_VIEW: 7,\n  },\n  AUTOPLAY_DELAY: 3000,\n} as const;\n\n// Error handling constants\nexport const ERROR_HANDLING = {\n  MAX_ERROR_LOG_ENTRIES: 50,\n  DOM_READY_TIMEOUT: 5000,\n} as const;\n\n// UI styling constants\nexport const UI_STYLES = {\n  SOCIAL_ICON_WIDTH: 32,\n  SOCIAL_ICON_MARGIN_LEFT: 20,\n  TAG_TEXT_FONT_SIZE: 14,\n  TAG_CONTAINER_PADDING_TOP: 10,\n  TAG_CONTAINER_MARGIN_TOP: 5,\n} as const;\n\n// Array and index constants\nexport const ARRAY_CONSTANTS = {\n  EMPTY_LENGTH: 0,\n  FIRST_INDEX: 0,\n  NOT_FOUND_INDEX: -1,\n  NEXT_ITEM_OFFSET: 1,\n  LAST_ITEM_OFFSET: -1,\n} as const;\n\n// Timing constants\nexport const TIMING = {\n  CHECK_INTERVAL: 10,\n  DATA_CHECK_INTERVAL: 100,\n} as const;\n\n// DOM element constants\nexport const DOM_ELEMENTS = {\n  SWIPER_TAG_CONTAINER_ID: 'swiperTagContainer',\n  SWIPER_TAG_WRAPPER_ID: 'swiperTagWrapper',\n} as const;\n\n// CSS class constants\nexport const CSS_CLASSES = {\n  SWIPER: 'swiper',\n  SWIPER_WRAPPER: 'swiper-wrapper',\n  SWIPER_SLIDE: 'swiper-slide',\n  SWIPER_SLIDE_TAG: 'swiper-slide-tag',\n  SWIPER_SLIDE_TAG_INNER: 'swiper-slide-tag-inner',\n  SWIPER_SLIDE_TAG_INNER_MOBILE: 'swiper-slide-tag-inner-mobile',\n  TAG_SWIPER: 'tagSwiper',\n  TAG_TILES: 'TagTiles',\n  TAG_TILE: 'TagTile',\n  TAG_TILE_NAME: 'TagTile-name',\n  TAG_TILE_DESCRIPTION: 'TagTile-description',\n  TAG_TEXT_OUTER_CONTAINER: 'TagTextOuterContainer',\n  TAG_TEXT_CONTAINER: 'TagTextContainer',\n  TAG_TEXT_ICON: 'TagTextIcon',\n} as const;\n\n// CSS selector constants\nexport const CSS_SELECTORS = {\n  TAGS_PAGE_CONTENT: '#content .container .TagsPage-content',\n  APP_CONTENT: '.App-content',\n} as const;\n\n// Extension configuration constants\nexport const EXTENSION_CONFIG = {\n  ID: 'wusong8899-tag-tiles',\n  TRANSLATION_PREFIX: 'wusong8899-tag-tiles',\n} as const;\n\n// Social media platform constants\nexport const SOCIAL_PLATFORMS = [\n  'Kick',\n  'Facebook',\n  'Twitter',\n  'YouTube',\n  'Instagram'\n] as const;\n\nexport type SocialPlatform = typeof SOCIAL_PLATFORMS[number];\n", "import { MO<PERSON>LE_DETECTION, SWIPER_CONFIG } from '../../common/config/constants';\nimport type { MobileConfig } from '../../common/config/types';\n\n/**\n * Mobile detection utility functions\n */\n\n/**\n * Check if the current device is mobile\n */\nexport const isMobileDevice = (): boolean => {\n    try {\n        const userAgent = navigator.userAgent;\n        const mobileIndicator = userAgent.substring(\n            MOBILE_DETECTION.USER_AGENT_SUBSTR_START,\n            MOBILE_DETECTION.USER_AGENT_SUBSTR_LENGTH\n        );\n        return mobileIndicator === 'Mobi';\n    } catch {\n        return false;\n    }\n};\n\n/**\n * Get swiper configuration based on device type\n */\nexport const getSwiperConfig = (): MobileConfig => {\n    if (isMobileDevice()) {\n        return {\n            spaceBetween: SWIPER_CONFIG.MOBILE.SPACE_BETWEEN,\n            slidesPerView: SWIPER_CONFIG.MOBILE.SLIDES_PER_VIEW,\n        };\n    }\n    \n    return {\n        spaceBetween: SWIPER_CONFIG.DESKTOP.SPACE_BETWEEN,\n        slidesPerView: SWIPER_CONFIG.DESKTOP.SLIDES_PER_VIEW,\n    };\n};\n", "import type { RootConfig, Environment } from './types';\nimport {\n  EXTENSION_CONFIG,\n  TIMING,\n  DOM_ELEMENTS,\n  SWIPER_CONFIG\n} from './constants';\n\nexport const defaultConfig: RootConfig = {\n  env: (process.env.NODE_ENV as Environment) || 'production',\n  app: {\n    extensionId: EXTENSION_CONFIG.ID,\n    translationPrefix: EXTENSION_CONFIG.TRANSLATION_PREFIX,\n  },\n  tagTiles: {\n    autoplayDelay: SWIPER_CONFIG.AUTOPLAY_DELAY,\n    checkInterval: TIMING.CHECK_INTERVAL,\n    dataCheckInterval: TIMING.DATA_CHECK_INTERVAL,\n    mobile: {\n      spaceBetween: SWIPER_CONFIG.MOBILE.SPACE_BETWEEN,\n      slidesPerView: SWIPER_CONFIG.MOBILE.SLIDES_PER_VIEW,\n    },\n    desktop: {\n      spaceBetween: SWIPER_CONFIG.DESKTOP.SPACE_BETWEEN,\n      slidesPerView: SWIPER_CONFIG.DESKTOP.SLIDES_PER_VIEW,\n    },\n  },\n  ui: {\n    tagContainerId: DOM_ELEMENTS.SWIPER_TAG_CONTAINER_ID,\n    tagWrapperId: DOM_ELEMENTS.SWIPER_TAG_WRAPPER_ID,\n  },\n};\n", "import Swiper from 'swiper';\nimport { Autoplay } from 'swiper/modules';\nimport app from 'flarum/forum/app';\nimport * as DOMUtils from '../utils/dom-utils';\nimport { isMobileDevice, getSwiperConfig } from '../utils/mobile-detection';\nimport { ARRAY_CONSTANTS, SWIPER_CONFIG } from '../../common/config/constants';\nimport { defaultConfig } from '../../common/config';\nimport type { TagData } from '../../common/config/types';\n\n/**\n * Tag Tiles Manager for converting TagTiles to swiper layout\n */\nexport class TagTilesManager {\n\n    /**\n     * Change category layout to swiper-based layout\n     */\n    changeCategoryLayout(): void {\n        try {\n            if (DOMUtils.getElementById(defaultConfig.ui.tagContainerId)) {\n                return; // Already exists\n            }\n\n            // Try immediate processing first\n            const tagTiles = DOMUtils.querySelectorAll(\".TagTile\");\n            if (tagTiles.length > ARRAY_CONSTANTS.EMPTY_LENGTH) {\n                this.processTagTiles(tagTiles);\n            } else {\n                // If no TagTiles found immediately, wait and retry\n                this.waitForTagTilesAndProcess();\n            }\n        } catch {\n            // Silently handle category layout errors\n        }\n    }\n\n    /**\n     * Wait for TagTiles to be available and process them\n     */\n    private waitForTagTilesAndProcess(): void {\n        const maxAttempts = 10;\n        const attemptInterval = 200;\n        let attempts = 0;\n\n        const checkAndProcess = (): void => {\n            attempts += ARRAY_CONSTANTS.NEXT_ITEM_OFFSET;\n            const tagTiles = DOMUtils.querySelectorAll(\".TagTile\");\n\n            if (tagTiles.length > ARRAY_CONSTANTS.EMPTY_LENGTH) {\n                // TagTiles found, process them\n                this.processTagTiles(tagTiles);\n            } else if (attempts < maxAttempts) {\n                // TagTiles not found yet, try again\n                setTimeout(checkAndProcess, attemptInterval);\n            }\n            // If max attempts reached and no TagTiles found, silently fail\n        };\n\n        checkAndProcess();\n    }\n\n    /**\n     * Process the found TagTiles\n     */\n    private processTagTiles(tagTiles: NodeListOf<Element>): void {\n        try {\n            const container = this.createTagSwiperContainer();\n            if (!container) {\n                return;\n            }\n\n            const swiper = this.createTagSwiper(container);\n            if (!swiper) {\n                return;\n            }\n\n            const wrapper = this.createTagSwiperWrapper(swiper);\n            if (!wrapper) {\n                return;\n            }\n\n            this.populateTagSlides(wrapper, tagTiles);\n            this.appendTagContainer(container);\n            this.addTagSwiperContent(container);\n            this.removeOriginalTagTiles();\n            this.setupMobileStyles();\n            this.initializeTagSwiper();\n\n            // Notify other extensions that the tags layout has changed\n            this.notifyTagsLayoutChanged();\n        } catch {\n            // Silently handle tag processing errors\n        }\n    }\n\n    /**\n     * Create tag swiper container\n     */\n    private createTagSwiperContainer(): HTMLElement {\n        const container = DOMUtils.createElement('div', {\n            className: 'swiperTagContainer',\n            id: defaultConfig.ui.tagContainerId\n        });\n\n        const textContainer = DOMUtils.createElement('div', {\n            className: 'TagTextOuterContainer'\n        });\n\n        DOMUtils.appendChild(container, textContainer);\n        return container;\n    }\n\n    /**\n     * Create tag swiper element\n     */\n    private createTagSwiper(container: HTMLElement): HTMLElement {\n        const textContainer = container.querySelector('.TagTextOuterContainer');\n        const swiper = DOMUtils.createElement('div', {\n            className: 'swiper tagSwiper'\n        });\n\n        if (textContainer) {\n            DOMUtils.appendChild(textContainer, swiper);\n        }\n\n        return swiper;\n    }\n\n    /**\n     * Create tag swiper wrapper\n     */\n    private createTagSwiperWrapper(swiper: HTMLElement): HTMLElement {\n        const wrapper = DOMUtils.createElement('div', {\n            className: 'swiper-wrapper',\n            id: defaultConfig.ui.tagWrapperId\n        });\n        DOMUtils.appendChild(swiper, wrapper);\n        return wrapper;\n    }\n\n    /**\n     * Populate tag slides\n     */\n    private populateTagSlides(wrapper: HTMLElement, tagTiles: NodeListOf<Element>): void {\n        const isMobile = isMobileDevice();\n\n        for (const tag of tagTiles) {\n            const tagElement = tag as HTMLElement;\n            const tagData = this.extractTagData(tagElement);\n\n            if (tagData) {\n                const slide = this.createTagSlide(tagData, isMobile);\n                DOMUtils.appendChild(wrapper, slide);\n            }\n        }\n    }\n\n    /**\n     * Extract tag data from DOM element\n     */\n    private extractTagData(tag: HTMLElement): TagData | void {\n        const linkElement = tag.querySelector('a') as HTMLAnchorElement;\n        const nameElement = tag.querySelector('.TagTile-name') as HTMLElement;\n        const descElement = tag.querySelector('.TagTile-description') as HTMLElement;\n\n        if (!linkElement || !nameElement) {\n            return;\n        }\n\n        // Get background from flarum-tag-background plugin or fallback to computed style\n        const backgroundImage = this.getTagBackgroundImage(linkElement.href, tag);\n        const computedStyle = globalThis.getComputedStyle(tag);\n        const background = backgroundImage || computedStyle.background;\n\n        let description = '';\n        let descColor = '';\n        if (descElement) {\n            description = descElement.textContent || '';\n            descColor = globalThis.getComputedStyle(descElement).color;\n        }\n\n        return {\n            url: linkElement.href,\n            background: background,\n            name: nameElement.textContent || '',\n            nameColor: globalThis.getComputedStyle(nameElement).color,\n            description,\n            descColor\n        };\n    }\n\n    /**\n     * Get tag background image from flarum-tag-background plugin\n     */\n    private getTagBackgroundImage(tagUrl: string, tagElement: HTMLElement): string | void {\n        try {\n            // Extract tag slug from URL\n            const url = new URL(tagUrl, globalThis.location.origin);\n            const parts = url.pathname.split('/').filter(Boolean);\n            const tIndex = parts.indexOf('t');\n            const tagsIndex = parts.indexOf('tags');\n\n            let slug = '';\n\n            if (tIndex !== ARRAY_CONSTANTS.NOT_FOUND_INDEX && parts[tIndex + ARRAY_CONSTANTS.NEXT_ITEM_OFFSET]) {\n                slug = parts[tIndex + ARRAY_CONSTANTS.NEXT_ITEM_OFFSET];\n            } else if (tagsIndex !== ARRAY_CONSTANTS.NOT_FOUND_INDEX && parts[tagsIndex + ARRAY_CONSTANTS.NEXT_ITEM_OFFSET]) {\n                slug = parts[tagsIndex + ARRAY_CONSTANTS.NEXT_ITEM_OFFSET];\n            } else if (parts.length > ARRAY_CONSTANTS.EMPTY_LENGTH) {\n                slug = parts[parts.length + ARRAY_CONSTANTS.LAST_ITEM_OFFSET]; // Get the last part of the URL\n            }\n\n            if (!slug) {\n                return;\n            }\n\n            // Get background URL using the same logic as flarum-tag-background\n            const bgUrl = this.getTagBackgroundUrlBySlug(slug);\n\n            if (bgUrl) {\n                return `url(${bgUrl})`;\n            }\n\n            return;\n        } catch {\n            // Fallback to checking inline styles set by flarum-tag-background\n            const inlineBackground = tagElement.style.background;\n            if (inlineBackground && inlineBackground.includes('url(')) {\n                return inlineBackground;\n            }\n            return;\n        }\n    }\n\n    /**\n     * Get tag background URL by slug - shared logic with flarum-tag-background\n     */\n    private getTagBackgroundUrlBySlug(slug: string): string | void {\n        try {\n            // Get tag from Flarum store\n            const tags = app.store.all('tags') as unknown[];\n            const tagModel = tags.find((tagItem: unknown) => {\n                const tagRecord = tagItem as Record<string, unknown>;\n                let tagSlug = '';\n\n                if (typeof tagRecord.slug === 'function') {\n                    tagSlug = tagRecord.slug();\n                } else if (tagRecord.attribute && typeof tagRecord.attribute === 'function') {\n                    tagSlug = tagRecord.attribute('slug');\n                }\n\n                return tagSlug === slug;\n            });\n\n            if (!tagModel) {\n                return;\n            }\n\n            // Get background URL from tag model\n            const tagRecord = tagModel as Record<string, unknown>;\n\n            if (tagRecord.attribute && typeof tagRecord.attribute === 'function') {\n                const bgUrl = tagRecord.attribute('wusong8899BackgroundURL');\n                if (bgUrl) {\n                    return bgUrl;\n                }\n            }\n\n            return;\n        } catch {\n            return;\n        }\n    }\n\n    /**\n     * Create individual tag slide\n     */\n    private createTagSlide(tagData: TagData, isMobile: boolean): HTMLElement {\n        const slide = DOMUtils.createElement('div', {\n            className: 'swiper-slide swiper-slide-tag'\n        });\n\n        let innerClass = 'swiper-slide-tag-inner';\n        if (isMobile) {\n            innerClass = 'swiper-slide-tag-inner-mobile';\n        }\n\n        const backgroundStyle = `background:${tagData.background};background-size: cover;background-position: center;background-repeat: no-repeat;`;\n\n        // Check if there's a background image (from flarum-tag-background plugin)\n        const hasBackgroundImage = this.hasBackgroundImage(tagData.background);\n\n        // If there's a background image, hide the text; otherwise show it\n        let textContent = '';\n        if (!hasBackgroundImage) {\n            textContent = `\n            <div style='font-weight:bold;font-size:14px;color:${tagData.nameColor}'>\n                ${tagData.name}\n            </div>\n        `;\n        }\n\n        slide.innerHTML = `\n            <a href='${tagData.url}'>\n                <div class='${innerClass}' style='${backgroundStyle}'>\n                    ${textContent}\n                </div>\n            </a>\n        `;\n\n        return slide;\n    }\n\n    /**\n     * Check if background contains an image URL\n     */\n    private hasBackgroundImage(background: string): boolean {\n        if (!background) {\n            return false;\n        }\n\n        // Check if background contains url() function\n        return background.includes('url(') && !background.includes('url()');\n    }\n\n    /**\n     * Append tag container to DOM\n     */\n    private appendTagContainer(container: HTMLElement): void {\n        const contentElement = DOMUtils.querySelector(\"#content .container .TagsPage-content\");\n        if (contentElement) {\n            DOMUtils.prependChild(contentElement, container);\n        }\n    }\n\n    /**\n     * Add additional content to tag container\n     */\n    private addTagSwiperContent(container: HTMLElement): void {\n        const textContainer = container.querySelector('.TagTextOuterContainer');\n        if (textContainer) {\n            const titleElement = DOMUtils.createElement('div', {\n                className: 'TagTextContainer'\n            }, \"<div class='TagTextIcon'></div>中文玩家社区资讯\");\n\n            DOMUtils.prependChild(textContainer, titleElement);\n\n            const socialButtons = this.createSocialButtonsHTML();\n            textContainer.insertAdjacentHTML('beforeend', socialButtons);\n        }\n    }\n\n    /**\n     * Create social buttons HTML\n     */\n    private createSocialButtonsHTML(): string {\n        const extensionId = defaultConfig.app.extensionId;\n\n        // Define social media platforms with their settings keys and default icons\n        const socialPlatforms = [\n            {\n                urlKey: `${extensionId}.SocialKickUrl`,\n                iconKey: `${extensionId}.SocialKickIcon`,\n                defaultIcon: ''\n            },\n            {\n                urlKey: `${extensionId}.SocialFacebookUrl`,\n                iconKey: `${extensionId}.SocialFacebookIcon`,\n                defaultIcon: ''\n            },\n            {\n                urlKey: `${extensionId}.SocialTwitterUrl`,\n                iconKey: `${extensionId}.SocialTwitterIcon`,\n                defaultIcon: ''\n            },\n            {\n                urlKey: `${extensionId}.SocialYouTubeUrl`,\n                iconKey: `${extensionId}.SocialYouTubeIcon`,\n                defaultIcon: ''\n            },\n            {\n                urlKey: `${extensionId}.SocialInstagramUrl`,\n                iconKey: `${extensionId}.SocialInstagramIcon`,\n                defaultIcon: ''\n            }\n        ];\n\n        // Generate social buttons HTML\n        const socialButtons = socialPlatforms\n            .map((platform, index) => {\n                const url = app.forum.attribute(platform.urlKey) || '';\n                const iconUrl = app.forum.attribute(platform.iconKey) || platform.defaultIcon;\n\n                // Only render button if both URL and icon are provided\n                if (!url.trim() || !iconUrl.trim()) {\n                    return '';\n                }\n\n                let marginStyle = '';\n                if (index > ARRAY_CONSTANTS.FIRST_INDEX) {\n                    marginStyle = 'margin-left: 20px;';\n                }\n                return `<img onClick=\"window.open('${url}', '_blank')\" style=\"width: 32px;${marginStyle}\" src=\"${iconUrl}\">`;\n            })\n            .filter(button => button !== '') // Remove empty buttons\n            .join('');\n\n        // Only render the container if there are social buttons\n        if (!socialButtons) {\n            return '';\n        }\n\n        return `\n            <div style=\"text-align:center;padding-top: 10px;\">\n                <button class=\"Button Button--primary\" type=\"button\" style=\"font-weight: normal !important; color:#ffa000; background: #1a1d2e !important;border-radius: 2rem !important;\">\n                    <div style=\"margin-top: 5px;\" class=\"Button-label\">\n                        ${socialButtons}\n                    </div>\n                </button>\n            </div>\n        `;\n    }\n\n    /**\n     * Remove original tag tiles\n     */\n    private removeOriginalTagTiles(): void {\n        const tagTiles = DOMUtils.querySelector(\".TagTiles\");\n        if (tagTiles) {\n            DOMUtils.removeElement(tagTiles);\n        }\n    }\n\n    /**\n     * Setup mobile-specific styles\n     */\n    private setupMobileStyles(): void {\n        if (isMobileDevice()) {\n            const app = DOMUtils.getElementById(\"app\");\n            const appContent = DOMUtils.querySelector(\".App-content\") as HTMLElement;\n\n            if (app) {\n                DOMUtils.setStyles(app, { 'overflow-x': 'hidden' });\n            }\n\n            if (appContent) {\n                DOMUtils.setStyles(appContent, {\n                    'min-height': 'auto',\n                    'background': ''\n                });\n            }\n        }\n    }\n\n    /**\n     * Initialize tag swiper\n     */\n    private initializeTagSwiper(): void {\n        try {\n            const config = getSwiperConfig();\n            const swiperInstance = new Swiper(\".tagSwiper\", {\n                loop: true,\n                spaceBetween: config.spaceBetween,\n                slidesPerView: config.slidesPerView,\n                autoplay: {\n                    delay: SWIPER_CONFIG.AUTOPLAY_DELAY,\n                    disableOnInteraction: false,\n                },\n                modules: [Autoplay]\n            });\n            // Store reference to prevent garbage collection\n            if (swiperInstance) {\n                // Swiper initialized successfully\n            }\n        } catch {\n            // Silently handle tag swiper initialization errors\n        }\n    }\n\n    /**\n     * Notify other extensions that the tags layout has changed\n     */\n    private notifyTagsLayoutChanged(): void {\n        try {\n            // Dispatch custom event to notify other extensions\n            const event = new CustomEvent('tagsLayoutChanged', {\n                detail: {\n                    extensionId: defaultConfig.app.extensionId,\n                    layoutType: 'swiper'\n                }\n            });\n            document.dispatchEvent(event);\n        } catch {\n            // Silently handle event dispatch errors\n        }\n    }\n}\n", "import { ERROR_HANDLING } from '../../common/config/constants';\nimport type { ErrorLogEntry } from '../../common/config/types';\n\n/**\n * Error handling utility for the TagTiles extension\n */\nexport class ErrorHandler {\n    private static instance: <PERSON>rror<PERSON>andler;\n    private errorLog: ErrorLogEntry[] = [];\n    private isInitialized = false;\n\n    private constructor() {}\n\n    /**\n     * Get singleton instance\n     */\n    public static getInstance(): ErrorHandler {\n        if (!ErrorHandler.instance) {\n            ErrorHandler.instance = new ErrorHandler();\n        }\n        return ErrorHandler.instance;\n    }\n\n    /**\n     * Initialize error handler\n     */\n    public initialize(): boolean {\n        try {\n            if (this.isInitialized) {\n                return true;\n            }\n\n            // Set up global error handling\n            this.setupGlobalErrorHandling();\n            this.isInitialized = true;\n            return true;\n        } catch {\n            return false;\n        }\n    }\n\n    /**\n     * Handle synchronous errors\n     */\n    public handleSync<T>(fn: () => T, context: string): T | undefined {\n        try {\n            return fn();\n        } catch (error) {\n            this.logError(error as Error, context);\n            return undefined;\n        }\n    }\n\n    /**\n     * Handle asynchronous errors\n     */\n    public async handleAsync<T>(fn: () => Promise<T>, context: string): Promise<T | undefined> {\n        try {\n            return await fn();\n        } catch (error) {\n            this.logError(error as Error, context);\n            return undefined;\n        }\n    }\n\n    /**\n     * Log error with context\n     */\n    private logError(error: Error, context: string): void {\n        try {\n            const entry: ErrorLogEntry = {\n                timestamp: new Date(),\n                error,\n                context,\n            };\n\n            this.errorLog.push(entry);\n\n            // Keep log size manageable\n            if (this.errorLog.length > ERROR_HANDLING.MAX_ERROR_LOG_ENTRIES) {\n                this.errorLog.shift();\n            }\n\n            // Log to console in development\n            if (process.env.NODE_ENV === 'development') {\n                console.warn(`[TagTiles] Error in ${context}:`, error);\n            }\n        } catch {\n            // Silently handle logging errors\n        }\n    }\n\n    /**\n     * Set up global error handling\n     */\n    private setupGlobalErrorHandling(): void {\n        try {\n            // Handle unhandled promise rejections\n            window.addEventListener('unhandledrejection', (event) => {\n                this.logError(\n                    new Error(String(event.reason)),\n                    'Unhandled Promise Rejection'\n                );\n            });\n        } catch {\n            // Silently handle setup errors\n        }\n    }\n\n    /**\n     * Get error log (for debugging)\n     */\n    public getErrorLog(): ErrorLogEntry[] {\n        return [...this.errorLog];\n    }\n\n    /**\n     * Clear error log\n     */\n    public clearErrorLog(): void {\n        this.errorLog = [];\n    }\n}\n", "import app from 'flarum/forum/app';\nimport { defaultConfig } from '../../common/config';\n\n/**\n * Configuration manager for the TagTiles extension\n */\nexport class ConfigManager {\n    private static instance: ConfigManager;\n\n    private constructor() {}\n\n    /**\n     * Get singleton instance\n     */\n    public static getInstance(): ConfigManager {\n        if (!ConfigManager.instance) {\n            ConfigManager.instance = new ConfigManager();\n        }\n        return ConfigManager.instance;\n    }\n\n    /**\n     * Check if current page is tags page\n     */\n    public isTagsPage(): boolean {\n        try {\n            const currentRoute = app.current.get('routeName');\n            return currentRoute === 'tags';\n        } catch {\n            // Fallback: check URL\n            try {\n                return window.location.pathname.includes('/tags');\n            } catch {\n                return false;\n            }\n        }\n    }\n\n    /**\n     * Get extension configuration\n     */\n    public getConfig() {\n        return defaultConfig;\n    }\n\n    /**\n     * Check if extension is properly configured\n     */\n    public isConfigured(): boolean {\n        try {\n            // Check if at least one social media platform is configured\n            const socialPlatforms = ['Kick', 'Facebook', 'Twitter', 'YouTube', 'Instagram'];\n            \n            for (const platform of socialPlatforms) {\n                const url = app.forum.attribute(`${defaultConfig.app.extensionId}.Social${platform}Url`);\n                const icon = app.forum.attribute(`${defaultConfig.app.extensionId}.Social${platform}Icon`);\n                \n                if (url && icon) {\n                    return true;\n                }\n            }\n            \n            return false;\n        } catch {\n            return false;\n        }\n    }\n}\n", "import { extend } from 'flarum/common/extend';\nimport app from 'flarum/forum/app';\nimport TagsPage from 'flarum/tags/components/TagsPage';\n\nimport { TagTilesManager } from './components/tag-tiles-manager';\nimport { <PERSON>rror<PERSON>andler } from './utils/error-handler';\nimport { ConfigManager } from './utils/config-manager';\nimport { defaultConfig } from '../common/config';\n\n/**\n * Main extension initializer for TagTiles\n */\napp.initializers.add(defaultConfig.app.extensionId, () => {\n    const errorHandler = ErrorHandler.getInstance();\n    const configManager = ConfigManager.getInstance();\n\n    // Initialize error handling\n    if (!errorHandler.initialize()) {\n        return;\n    }\n\n    const tagTilesManager = new TagTilesManager();\n\n    // Extend TagsPage to setup UI components when the page loads\n    extend(TagsPage.prototype, 'oncreate', function tagsPageOnCreateExtension(_vnode: unknown) {\n        errorHandler.handleSync(() => {\n            if (configManager.isTagsPage()) {\n                // Force UI components setup\n                const DOM_READY_DELAY = 100;\n                setTimeout(() => {\n                    tagTilesManager.changeCategoryLayout();\n                }, DOM_READY_DELAY);\n            }\n        }, 'TagsPage oncreate extension');\n    });\n\n    extend(TagsPage.prototype, 'onupdate', function tagsPageOnUpdateExtension(_vnode: unknown) {\n        errorHandler.handleSync(() => {\n            // Check if swiper container doesn't exist and create it\n            if (!document.getElementById(defaultConfig.ui.tagContainerId)) {\n                const DOM_READY_DELAY = 100;\n                setTimeout(() => {\n                    tagTilesManager.changeCategoryLayout();\n                }, DOM_READY_DELAY);\n            }\n        }, 'TagsPage onupdate extension');\n    });\n});\n"], "names": ["isObject", "obj", "extend", "target", "src", "noExtend", "key", "ssrDocument", "getDocument", "doc", "ssrWindow", "callback", "id", "getWindow", "win", "classesToTokens", "classes", "c", "deleteProps", "object", "nextTick", "delay", "now", "getComputedStyle", "el", "window", "style", "getTranslate", "axis", "matrix", "curTransform", "transformMatrix", "curStyle", "o", "isNode", "node", "to", "i", "nextSource", "keysArray", "nextIndex", "len", "<PERSON><PERSON><PERSON>", "desc", "setCSSProperty", "varName", "varValue", "animateCSSModeScroll", "_ref", "swiper", "targetPosition", "side", "startPosition", "startTime", "time", "duration", "dir", "isOutOfBound", "current", "animate", "progress", "easeProgress", "currentPosition", "elementChildren", "element", "selector", "children", "elementIsChildOfSlot", "slot", "elementsQueue", "elementToCheck", "elementIsChildOf", "parent", "<PERSON><PERSON><PERSON><PERSON>", "showWarning", "text", "createElement", "tag", "elementPrevAll", "prevEls", "prev", "elementNextAll", "nextEls", "next", "elementStyle", "prop", "elementIndex", "child", "elementParents", "parents", "elementOuterSize", "size", "<PERSON><PERSON><PERSON><PERSON>", "support", "calcSupport", "document", "getSupport", "deviceCached", "calcDevice", "_temp", "userAgent", "platform", "ua", "device", "screenWidth", "screenHeight", "android", "ipad", "ipod", "iphone", "windows", "macos", "iPadScreens", "getDevice", "overrides", "browser", "calcB<PERSON>er", "needPerspectiveFix", "<PERSON><PERSON><PERSON><PERSON>", "major", "minor", "num", "isWebView", "isSafariB<PERSON><PERSON>", "need3dFix", "<PERSON><PERSON><PERSON><PERSON>", "Resize", "on", "emit", "observer", "animationFrame", "resize<PERSON><PERSON>ler", "createObserver", "entries", "width", "height", "newWidth", "newHeight", "_ref2", "contentBoxSize", "contentRect", "removeObserver", "orientationChangeHandler", "Observer", "extendParams", "observers", "attach", "options", "ObserverFunc", "mutations", "observerUpdate", "init", "containerParents", "destroy", "eventsEmitter", "events", "handler", "priority", "self", "method", "event", "once<PERSON><PERSON><PERSON>", "_len", "args", "_key", "index", "<PERSON><PERSON><PERSON><PERSON>", "data", "context", "_len2", "_key2", "updateSize", "updateSlides", "getDirectionPropertyValue", "label", "params", "wrapperEl", "slidesEl", "swiperSize", "rtl", "wrongRTL", "isVirtual", "previousSlidesLength", "slides", "<PERSON><PERSON><PERSON><PERSON>", "snapGrid", "slidesGrid", "slidesSizesGrid", "offsetBefore", "offsetAfter", "previousSnapGridLength", "previousSlidesGridLength", "spaceBetween", "slidePosition", "prevSlideSize", "slideEl", "gridEnabled", "slideSize", "shouldResetSlideSize", "slide", "slideStyles", "currentTransform", "currentWebKitTransform", "paddingLeft", "paddingRight", "marginLeft", "marginRight", "boxSizing", "clientWidth", "offsetWidth", "newSlidesGrid", "slidesGridItem", "groups", "groupSize", "_", "slideIndex", "allSlidesSize", "slideSizeValue", "maxSnap", "snap", "offsetSize", "allSlidesOffset", "snapIndex", "addToSnapGrid", "addToSlidesGrid", "v", "backFaceHiddenClass", "hasClassBackfaceClassAdded", "updateAutoHeight", "speed", "activeSlides", "getSlideByIndex", "updateSlidesOffset", "minusOffset", "toggleSlideClasses$1", "condition", "className", "updateSlidesProgress", "translate", "offsetCenter", "slideOffset", "slideProgress", "originalSlideProgress", "slideBefore", "slideAfter", "isFullyVisible", "isVisible", "updateProgress", "multiplier", "translatesDiff", "isBeginning", "isEnd", "progressLoop", "wasBeginning", "wasEnd", "isBeginningRounded", "isEndRounded", "firstSlideIndex", "lastSlideIndex", "firstSlideTranslate", "lastSlideTranslate", "translateMax", "translateAbs", "toggleSlideClasses", "updateSlidesClasses", "activeIndex", "getFilteredSlide", "activeSlide", "prevSlide", "nextSlide", "processLazyPreloader", "imageEl", "slideSelector", "lazyEl", "unlazy", "preload", "amount", "<PERSON><PERSON><PERSON><PERSON>iew", "activeColumn", "preloadColumns", "slideIndexLastInView", "realIndex", "getActiveIndexByTranslate", "updateActiveIndex", "newActiveIndex", "previousIndex", "previousRealIndex", "previousSnapIndex", "getVirtualRealIndex", "aIndex", "skip", "firstSlideInColumn", "activeSlideIndex", "updateClickedSlide", "path", "pathEl", "slideFound", "update", "getSwiperTranslate", "currentTranslate", "setTranslate", "byController", "x", "y", "z", "newProgress", "minTranslate", "maxTranslate", "translateTo", "runCallbacks", "translateBounds", "internal", "newTranslate", "isH", "e", "setTransition", "transitionEmit", "direction", "step", "transitionStart", "transitionEnd", "transition", "slideTo", "initial", "enabled", "normalizedTranslate", "normalizedGrid", "normalizedGridNext", "t", "slideToLoop", "newIndex", "targetSlideIndex", "cols", "centeredSlides", "needLoopFix", "slideNext", "animating", "perGroup", "increment", "slidePrev", "rtlTranslate", "normalize", "val", "normalizedSnapGrid", "isFreeMode", "prevSnap", "prevSnapIndex", "prevIndex", "lastIndex", "slideReset", "slideToClosest", "threshold", "currentSnap", "nextSnap", "slideToClickedSlide", "slideToIndex", "isGrid", "loopCreate", "slideRealIndex", "initSlides", "clearBlankSlides", "slidesPerGroup", "shouldFillGroup", "shouldFillGrid", "addBlankSlides", "amountOfSlides", "slidesToAdd", "loopFix", "byMousewheel", "allowSlidePrev", "allowSlideNext", "initialSlide", "loopedSlides", "prependSlidesIndexes", "appendSlidesIndexes", "isInitialOverflow", "isNext", "isPrev", "slidesPrepended", "slidesAppended", "activeColIndexWithShift", "colIndexToPrepend", "currentSlideTranslate", "diff", "shift", "loopParams", "loop<PERSON><PERSON><PERSON>", "newSlidesOrder", "loop", "setGrabCursor", "moving", "unsetGrabCursor", "grabCursor", "closestElement", "base", "__closestFrom", "found", "preventEdgeSwipe", "startX", "edgeSwipeDetection", "edgeSwipeThreshold", "onTouchStart", "touches", "targetEl", "swipingClassHasValue", "eventPath", "noSwipingSelector", "isTargetShadow", "startY", "preventDefault", "shouldPreventDefault", "onTouchMove", "targetTouch", "pageX", "pageY", "diffX", "diffY", "touchAngle", "touchesDiff", "prevTouchesDirection", "isLoop", "allowLoopFix", "evt", "disableParentSwiper", "resistanceRatio", "onTouchEnd", "touchEndTime", "timeDiff", "pathTree", "currentPos", "swipeToLast", "stopIndex", "rewindFirstIndex", "rewindLastIndex", "ratio", "onResize", "isVirtualLoop", "onClick", "onScroll", "onLoad", "onDocumentTouchStart", "capture", "dom<PERSON>ethod", "swiperMethod", "attachEvents", "detachEvents", "events$1", "isGridEnabled", "setBreakpoint", "initialized", "breakpoints", "breakpointsBase", "breakpoint<PERSON><PERSON><PERSON>", "breakpoint", "breakpointP<PERSON>ms", "wasMultiRow", "isMultiRow", "wasGrabCursor", "isGrabCursor", "wasEnabled", "wasModuleEnabled", "isModuleEnabled", "directionChanged", "needsReLoop", "<PERSON><PERSON><PERSON>", "isEnabled", "<PERSON><PERSON><PERSON>", "getBreakpoint", "containerEl", "currentHeight", "points", "point", "minRatio", "b", "value", "prepareClasses", "prefix", "resultClasses", "item", "classNames", "addClasses", "suffixes", "removeClasses", "checkOverflow", "wasLocked", "slidesOffsetBefore", "lastSlideRightEdge", "checkOverflow$1", "defaults", "moduleExtendParams", "allModulesParams", "moduleParamName", "moduleParams", "prototypes", "extendedDefaults", "Swiper", "swipers", "newParams", "mod", "swiperParams", "eventName", "property", "min", "cls", "updates", "view", "exact", "spv", "breakLoop", "translateValue", "translated", "newDirection", "needUpdate", "currentDirection", "getWrapperSelector", "lazyElements", "deleteInstance", "cleanStyles", "newDefaults", "modules", "module", "m", "prototypeGroup", "protoMethod", "Autoplay", "timeout", "raf", "autoplayDelayTotal", "autoplayDelayCurrent", "autoplayTimeLeft", "autoplayStartTime", "wasPaused", "isTouched", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "touchStartTimeout", "slideChanged", "pausedByInteraction", "pausedByPointerEnter", "onTransitionEnd", "resume", "calcTimeLeft", "timeLeft", "getSlideDelay", "activeSlideEl", "run", "delayForce", "currentSlideDelay", "proceed", "start", "stop", "pause", "reset", "onVisibilityChange", "onPointerEnter", "onPointerLeave", "attachMouseEvents", "detachMouseEvents", "attachDocumentEvents", "detachDocumentEvents", "_s", "querySelector", "querySelectorAll", "getElementById", "tagName", "innerHTML", "append<PERSON><PERSON><PERSON>", "prepend<PERSON>hild", "removeElement", "setStyles", "styles", "MOBILE_DETECTION", "SWIPER_CONFIG", "ERROR_HANDLING", "ARRAY_CONSTANTS", "TIMING", "DOM_ELEMENTS", "EXTENSION_CONFIG", "isMobileDevice", "getSwiperConfig", "defaultConfig", "TagTilesManager", "DOMUtils.getElementById", "tagTiles", "DOMUtils.querySelectorAll", "attempts", "checkAndProcess", "container", "wrapper", "DOMUtils.createElement", "textContainer", "DOMUtils.appendChild", "isMobile", "tagElement", "tagData", "linkElement", "nameElement", "desc<PERSON><PERSON>", "backgroundImage", "computedStyle", "background", "description", "descColor", "tagUrl", "parts", "tIndex", "tagsIndex", "slug", "bgUrl", "inlineBackground", "tagModel", "app", "tagItem", "tagRecord", "tagSlug", "innerClass", "backgroundStyle", "hasBackgroundImage", "textContent", "contentElement", "DOMUtils.querySelector", "DOMUtils.prependChild", "titleElement", "socialButtons", "extensionId", "url", "iconUrl", "marginStyle", "button", "DOMUtils.removeElement", "appContent", "DOMUtils.setStyles", "config", "swiperInstance", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fn", "error", "entry", "ConfigManager", "socialPlatforms", "icon", "<PERSON><PERSON><PERSON><PERSON>", "config<PERSON><PERSON><PERSON>", "tagTilesManager", "TagsPage", "_vnode"], "mappings": "gCAYA,SAASA,GAASC,EAAK,CACrB,OAAOA,IAAQ,MAAQ,OAAOA,GAAQ,UAAY,gBAAiBA,GAAOA,EAAI,cAAgB,MAChG,CACA,SAASC,GAAOC,EAAQC,EAAK,CACvBD,IAAW,SACbA,EAAS,CAAA,GAEPC,IAAQ,SACVA,EAAM,CAAA,GAER,MAAMC,EAAW,CAAC,YAAa,cAAe,WAAW,EACzD,OAAO,KAAKD,CAAG,EAAE,OAAOE,GAAOD,EAAS,QAAQC,CAAG,EAAI,CAAC,EAAE,QAAQA,GAAO,CACnE,OAAOH,EAAOG,CAAG,EAAM,IAAaH,EAAOG,CAAG,EAAIF,EAAIE,CAAG,EAAWN,GAASI,EAAIE,CAAG,CAAC,GAAKN,GAASG,EAAOG,CAAG,CAAC,GAAK,OAAO,KAAKF,EAAIE,CAAG,CAAC,EAAE,OAAS,GACpJJ,GAAOC,EAAOG,CAAG,EAAGF,EAAIE,CAAG,CAAC,CAEhC,CAAC,CACH,CACA,MAAMC,GAAc,CAClB,KAAM,CAAA,EACN,kBAAmB,CAAC,EACpB,qBAAsB,CAAC,EACvB,cAAe,CACb,MAAO,CAAC,EACR,SAAU,EACd,EACE,eAAgB,CACd,OAAO,IACT,EACA,kBAAmB,CACjB,MAAO,CAAA,CACT,EACA,gBAAiB,CACf,OAAO,IACT,EACA,aAAc,CACZ,MAAO,CACL,WAAY,CAAC,CACnB,CACE,EACA,eAAgB,CACd,MAAO,CACL,SAAU,CAAA,EACV,WAAY,CAAA,EACZ,MAAO,CAAA,EACP,cAAe,CAAC,EAChB,sBAAuB,CACrB,MAAO,CAAA,CACT,CACN,CACE,EACA,iBAAkB,CAChB,MAAO,CAAA,CACT,EACA,YAAa,CACX,OAAO,IACT,EACA,SAAU,CACR,KAAM,GACN,KAAM,GACN,SAAU,GACV,KAAM,GACN,OAAQ,GACR,SAAU,GACV,SAAU,GACV,OAAQ,EACZ,CACA,EACA,SAASC,GAAc,CACrB,MAAMC,EAAM,OAAO,SAAa,IAAc,SAAW,CAAA,EACzDP,OAAAA,GAAOO,EAAKF,EAAW,EAChBE,CACT,CACA,MAAMC,GAAY,CAChB,SAAUH,GACV,UAAW,CACT,UAAW,EACf,EACE,SAAU,CACR,KAAM,GACN,KAAM,GACN,SAAU,GACV,KAAM,GACN,OAAQ,GACR,SAAU,GACV,SAAU,GACV,OAAQ,EACZ,EACE,QAAS,CACP,cAAe,CAAC,EAChB,WAAY,CAAC,EACb,IAAK,CAAC,EACN,MAAO,CAAC,CACZ,EACE,YAAa,UAAuB,CAClC,OAAO,IACT,EACA,kBAAmB,CAAC,EACpB,qBAAsB,CAAC,EACvB,kBAAmB,CACjB,MAAO,CACL,kBAAmB,CACjB,MAAO,EACT,CACN,CACE,EACA,OAAQ,CAAC,EACT,MAAO,CAAC,EACR,OAAQ,CAAA,EACR,YAAa,CAAC,EACd,cAAe,CAAC,EAChB,YAAa,CACX,MAAO,CAAA,CACT,EACA,sBAAsBI,EAAU,CAC9B,OAAI,OAAO,WAAe,KACxBA,EAAQ,EACD,MAEF,WAAWA,EAAU,CAAC,CAC/B,EACA,qBAAqBC,EAAI,CACnB,OAAO,WAAe,KAG1B,aAAaA,CAAE,CACjB,CACF,EACA,SAASC,GAAY,CACnB,MAAMC,EAAM,OAAO,OAAW,IAAc,OAAS,CAAA,EACrDZ,OAAAA,GAAOY,EAAKJ,EAAS,EACdI,CACT,CC7IA,SAASC,GAAgBC,EAAS,CAChC,OAAIA,IAAY,SACdA,EAAU,IAELA,EAAQ,OAAO,MAAM,GAAG,EAAE,OAAOC,GAAK,CAAC,CAACA,EAAE,KAAI,CAAE,CACzD,CAEA,SAASC,GAAYjB,EAAK,CACxB,MAAMkB,EAASlB,EACf,OAAO,KAAKkB,CAAM,EAAE,QAAQb,GAAO,CACjC,GAAI,CACFa,EAAOb,CAAG,EAAI,IAChB,MAAY,CAEZ,CACA,GAAI,CACF,OAAOa,EAAOb,CAAG,CACnB,MAAY,CAEZ,CACF,CAAC,CACH,CACA,SAASc,GAAST,EAAUU,EAAO,CACjC,OAAIA,IAAU,SACZA,EAAQ,GAEH,WAAWV,EAAUU,CAAK,CACnC,CACA,SAASC,GAAM,CACb,OAAO,KAAK,IAAG,CACjB,CACA,SAASC,GAAiBC,EAAI,CAC5B,MAAMC,EAASZ,EAAS,EACxB,IAAIa,EACJ,OAAID,EAAO,mBACTC,EAAQD,EAAO,iBAAiBD,EAAI,IAAI,GAEtC,CAACE,GAASF,EAAG,eACfE,EAAQF,EAAG,cAERE,IACHA,EAAQF,EAAG,OAENE,CACT,CACA,SAASC,GAAaH,EAAII,EAAM,CAC1BA,IAAS,SACXA,EAAO,KAET,MAAMH,EAASZ,EAAS,EACxB,IAAIgB,EACAC,EACAC,EACJ,MAAMC,EAAWT,GAAiBC,CAAE,EACpC,OAAIC,EAAO,iBACTK,EAAeE,EAAS,WAAaA,EAAS,gBAC1CF,EAAa,MAAM,GAAG,EAAE,OAAS,IACnCA,EAAeA,EAAa,MAAM,IAAI,EAAE,IAAI,GAAK,EAAE,QAAQ,IAAK,GAAG,CAAC,EAAE,KAAK,IAAI,GAIjFC,EAAkB,IAAIN,EAAO,gBAAgBK,IAAiB,OAAS,GAAKA,CAAY,IAExFC,EAAkBC,EAAS,cAAgBA,EAAS,YAAcA,EAAS,aAAeA,EAAS,aAAeA,EAAS,WAAaA,EAAS,iBAAiB,WAAW,EAAE,QAAQ,aAAc,oBAAoB,EACzNH,EAASE,EAAgB,WAAW,MAAM,GAAG,GAE3CH,IAAS,MAEPH,EAAO,gBAAiBK,EAAeC,EAAgB,IAElDF,EAAO,SAAW,GAAIC,EAAe,WAAWD,EAAO,EAAE,CAAC,EAE9DC,EAAe,WAAWD,EAAO,CAAC,CAAC,GAEtCD,IAAS,MAEPH,EAAO,gBAAiBK,EAAeC,EAAgB,IAElDF,EAAO,SAAW,GAAIC,EAAe,WAAWD,EAAO,EAAE,CAAC,EAE9DC,EAAe,WAAWD,EAAO,CAAC,CAAC,GAEnCC,GAAgB,CACzB,CACA,SAAS9B,EAASiC,EAAG,CACnB,OAAO,OAAOA,GAAM,UAAYA,IAAM,MAAQA,EAAE,aAAe,OAAO,UAAU,SAAS,KAAKA,CAAC,EAAE,MAAM,EAAG,EAAE,IAAM,QACpH,CACA,SAASC,GAAOC,EAAM,CAEpB,OAAI,OAAO,OAAW,KAAe,OAAO,OAAO,YAAgB,IAC1DA,aAAgB,YAElBA,IAASA,EAAK,WAAa,GAAKA,EAAK,WAAa,GAC3D,CACA,SAASjC,GAAS,CAChB,MAAMkC,EAAK,OAAO,UAAU,QAAU,EAAI,OAAY,UAAU,CAAC,CAAC,EAC5D/B,EAAW,CAAC,YAAa,cAAe,WAAW,EACzD,QAASgC,EAAI,EAAGA,EAAI,UAAU,OAAQA,GAAK,EAAG,CAC5C,MAAMC,EAAaD,EAAI,GAAK,UAAU,QAAUA,EAAI,OAAY,UAAUA,CAAC,EAC3E,GAAgCC,GAAe,MAAQ,CAACJ,GAAOI,CAAU,EAAG,CAC1E,MAAMC,EAAY,OAAO,KAAK,OAAOD,CAAU,CAAC,EAAE,OAAOhC,GAAOD,EAAS,QAAQC,CAAG,EAAI,CAAC,EACzF,QAASkC,EAAY,EAAGC,EAAMF,EAAU,OAAQC,EAAYC,EAAKD,GAAa,EAAG,CAC/E,MAAME,EAAUH,EAAUC,CAAS,EAC7BG,EAAO,OAAO,yBAAyBL,EAAYI,CAAO,EAC5DC,IAAS,QAAaA,EAAK,aACzB3C,EAASoC,EAAGM,CAAO,CAAC,GAAK1C,EAASsC,EAAWI,CAAO,CAAC,EACnDJ,EAAWI,CAAO,EAAE,WACtBN,EAAGM,CAAO,EAAIJ,EAAWI,CAAO,EAEhCxC,EAAOkC,EAAGM,CAAO,EAAGJ,EAAWI,CAAO,CAAC,EAEhC,CAAC1C,EAASoC,EAAGM,CAAO,CAAC,GAAK1C,EAASsC,EAAWI,CAAO,CAAC,GAC/DN,EAAGM,CAAO,EAAI,CAAA,EACVJ,EAAWI,CAAO,EAAE,WACtBN,EAAGM,CAAO,EAAIJ,EAAWI,CAAO,EAEhCxC,EAAOkC,EAAGM,CAAO,EAAGJ,EAAWI,CAAO,CAAC,GAGzCN,EAAGM,CAAO,EAAIJ,EAAWI,CAAO,EAGtC,CACF,CACF,CACA,OAAON,CACT,CACA,SAASQ,EAAepB,EAAIqB,EAASC,EAAU,CAC7CtB,EAAG,MAAM,YAAYqB,EAASC,CAAQ,CACxC,CACA,SAASC,GAAqBC,EAAM,CAClC,GAAI,CACF,OAAAC,EACA,eAAAC,EACA,KAAAC,CACJ,EAAMH,EACJ,MAAMvB,EAASZ,EAAS,EAClBuC,EAAgB,CAACH,EAAO,UAC9B,IAAII,EAAY,KACZC,EACJ,MAAMC,EAAWN,EAAO,OAAO,MAC/BA,EAAO,UAAU,MAAM,eAAiB,OACxCxB,EAAO,qBAAqBwB,EAAO,cAAc,EACjD,MAAMO,EAAMN,EAAiBE,EAAgB,OAAS,OAChDK,EAAe,CAACC,EAASvD,IACtBqD,IAAQ,QAAUE,GAAWvD,GAAUqD,IAAQ,QAAUE,GAAWvD,EAEvEwD,EAAU,IAAM,CACpBL,EAAO,IAAI,KAAI,EAAG,QAAO,EACrBD,IAAc,OAChBA,EAAYC,GAEd,MAAMM,EAAW,KAAK,IAAI,KAAK,KAAKN,EAAOD,GAAaE,EAAU,CAAC,EAAG,CAAC,EACjEM,EAAe,GAAM,KAAK,IAAID,EAAW,KAAK,EAAE,EAAI,EAC1D,IAAIE,EAAkBV,EAAgBS,GAAgBX,EAAiBE,GAOvE,GANIK,EAAaK,EAAiBZ,CAAc,IAC9CY,EAAkBZ,GAEpBD,EAAO,UAAU,SAAS,CACxB,CAACE,CAAI,EAAGW,CACd,CAAK,EACGL,EAAaK,EAAiBZ,CAAc,EAAG,CACjDD,EAAO,UAAU,MAAM,SAAW,SAClCA,EAAO,UAAU,MAAM,eAAiB,GACxC,WAAW,IAAM,CACfA,EAAO,UAAU,MAAM,SAAW,GAClCA,EAAO,UAAU,SAAS,CACxB,CAACE,CAAI,EAAGW,CAClB,CAAS,CACH,CAAC,EACDrC,EAAO,qBAAqBwB,EAAO,cAAc,EACjD,MACF,CACAA,EAAO,eAAiBxB,EAAO,sBAAsBkC,CAAO,CAC9D,EACAA,EAAO,CACT,CAIA,SAASI,EAAgBC,EAASC,EAAU,CACtCA,IAAa,SACfA,EAAW,IAEb,MAAMxC,EAASZ,EAAS,EAClBqD,EAAW,CAAC,GAAGF,EAAQ,QAAQ,EAIrC,OAHIvC,EAAO,iBAAmBuC,aAAmB,iBAC/CE,EAAS,KAAK,GAAGF,EAAQ,iBAAgB,CAAE,EAExCC,EAGEC,EAAS,OAAO1C,GAAMA,EAAG,QAAQyC,CAAQ,CAAC,EAFxCC,CAGX,CACA,SAASC,GAAqB3C,EAAI4C,EAAM,CAEtC,MAAMC,EAAgB,CAACD,CAAI,EAC3B,KAAOC,EAAc,OAAS,GAAG,CAC/B,MAAMC,EAAiBD,EAAc,MAAK,EAC1C,GAAI7C,IAAO8C,EACT,MAAO,GAETD,EAAc,KAAK,GAAGC,EAAe,SAAU,GAAIA,EAAe,WAAaA,EAAe,WAAW,SAAW,CAAA,EAAK,GAAIA,EAAe,iBAAmBA,EAAe,iBAAgB,EAAK,CAAA,CAAG,CACxM,CACF,CACA,SAASC,GAAiB/C,EAAIgD,EAAQ,CACpC,MAAM/C,EAASZ,EAAS,EACxB,IAAI4D,EAAUD,EAAO,SAAShD,CAAE,EAChC,MAAI,CAACiD,GAAWhD,EAAO,iBAAmB+C,aAAkB,kBAE1DC,EADiB,CAAC,GAAGD,EAAO,iBAAgB,CAAE,EAC3B,SAAShD,CAAE,EACzBiD,IACHA,EAAUN,GAAqB3C,EAAIgD,CAAM,IAGtCC,CACT,CACA,SAASC,GAAYC,EAAM,CACzB,GAAI,CACF,QAAQ,KAAKA,CAAI,EACjB,MACF,MAAc,CAEd,CACF,CACA,SAASC,GAAcC,EAAK7D,EAAS,CAC/BA,IAAY,SACdA,EAAU,CAAA,GAEZ,MAAMQ,EAAK,SAAS,cAAcqD,CAAG,EACrC,OAAArD,EAAG,UAAU,IAAI,GAAI,MAAM,QAAQR,CAAO,EAAIA,EAAUD,GAAgBC,CAAO,CAAE,EAC1EQ,CACT,CAeA,SAASsD,GAAetD,EAAIyC,EAAU,CACpC,MAAMc,EAAU,CAAA,EAChB,KAAOvD,EAAG,wBAAwB,CAChC,MAAMwD,EAAOxD,EAAG,uBACZyC,EACEe,EAAK,QAAQf,CAAQ,GAAGc,EAAQ,KAAKC,CAAI,EACxCD,EAAQ,KAAKC,CAAI,EACxBxD,EAAKwD,CACP,CACA,OAAOD,CACT,CACA,SAASE,GAAezD,EAAIyC,EAAU,CACpC,MAAMiB,EAAU,CAAA,EAChB,KAAO1D,EAAG,oBAAoB,CAC5B,MAAM2D,EAAO3D,EAAG,mBACZyC,EACEkB,EAAK,QAAQlB,CAAQ,GAAGiB,EAAQ,KAAKC,CAAI,EACxCD,EAAQ,KAAKC,CAAI,EACxB3D,EAAK2D,CACP,CACA,OAAOD,CACT,CACA,SAASE,EAAa5D,EAAI6D,EAAM,CAE9B,OADexE,EAAS,EACV,iBAAiBW,EAAI,IAAI,EAAE,iBAAiB6D,CAAI,CAChE,CACA,SAASC,GAAa9D,EAAI,CACxB,IAAI+D,EAAQ/D,EACRa,EACJ,GAAIkD,EAAO,CAGT,IAFAlD,EAAI,GAEIkD,EAAQA,EAAM,mBAAqB,MACrCA,EAAM,WAAa,IAAGlD,GAAK,GAEjC,OAAOA,CACT,CAEF,CACA,SAASmD,GAAehE,EAAIyC,EAAU,CACpC,MAAMwB,EAAU,CAAA,EAChB,IAAIjB,EAAShD,EAAG,cAChB,KAAOgD,GAIHiB,EAAQ,KAAKjB,CAAM,EAErBA,EAASA,EAAO,cAElB,OAAOiB,CACT,CAWA,SAASC,GAAiBlE,EAAImE,EAAMC,EAAgB,CAClD,MAAMnE,EAASZ,EAAS,EAEtB,OAAOW,EAAGmE,IAAS,QAAU,cAAgB,cAAc,EAAI,WAAWlE,EAAO,iBAAiBD,EAAI,IAAI,EAAE,iBAAiBmE,IAAS,QAAU,eAAiB,YAAY,CAAC,EAAI,WAAWlE,EAAO,iBAAiBD,EAAI,IAAI,EAAE,iBAAiBmE,IAAS,QAAU,cAAgB,eAAe,CAAC,CAGvS,CC1TA,IAAIE,GACJ,SAASC,IAAc,CACrB,MAAMrE,EAASZ,EAAS,EAClBkF,EAAWvF,EAAW,EAC5B,MAAO,CACL,aAAcuF,EAAS,iBAAmBA,EAAS,gBAAgB,OAAS,mBAAoBA,EAAS,gBAAgB,MACzH,MAAO,CAAC,EAAE,iBAAkBtE,GAAUA,EAAO,eAAiBsE,aAAoBtE,EAAO,cAC7F,CACA,CACA,SAASuE,IAAa,CACpB,OAAKH,KACHA,GAAUC,GAAW,GAEhBD,EACT,CAEA,IAAII,GACJ,SAASC,GAAWC,EAAO,CACzB,GAAI,CACF,UAAAC,CACJ,EAAMD,IAAU,OAAS,CAAA,EAAKA,EAC5B,MAAMN,EAAUG,GAAU,EACpBvE,EAASZ,EAAS,EAClBwF,EAAW5E,EAAO,UAAU,SAC5B6E,EAAKF,GAAa3E,EAAO,UAAU,UACnC8E,EAAS,CACb,IAAK,GACL,QAAS,EACb,EACQC,EAAc/E,EAAO,OAAO,MAC5BgF,EAAehF,EAAO,OAAO,OAC7BiF,EAAUJ,EAAG,MAAM,6BAA6B,EACtD,IAAIK,EAAOL,EAAG,MAAM,sBAAsB,EAC1C,MAAMM,EAAON,EAAG,MAAM,yBAAyB,EACzCO,EAAS,CAACF,GAAQL,EAAG,MAAM,4BAA4B,EACvDQ,EAAUT,IAAa,QAC7B,IAAIU,EAAQV,IAAa,WAGzB,MAAMW,EAAc,CAAC,YAAa,YAAa,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,UAAU,EACrK,MAAI,CAACL,GAAQI,GAASlB,EAAQ,OAASmB,EAAY,QAAQ,GAAGR,CAAW,IAAIC,CAAY,EAAE,GAAK,IAC9FE,EAAOL,EAAG,MAAM,qBAAqB,EAChCK,IAAMA,EAAO,CAAC,EAAG,EAAG,QAAQ,GACjCI,EAAQ,IAINL,GAAW,CAACI,IACdP,EAAO,GAAK,UACZA,EAAO,QAAU,KAEfI,GAAQE,GAAUD,KACpBL,EAAO,GAAK,MACZA,EAAO,IAAM,IAIRA,CACT,CACA,SAASU,GAAUC,EAAW,CAC5B,OAAIA,IAAc,SAChBA,EAAY,CAAA,GAETjB,KACHA,GAAeC,GAAWgB,CAAS,GAE9BjB,EACT,CAEA,IAAIkB,GACJ,SAASC,IAAc,CACrB,MAAM3F,EAASZ,EAAS,EAClB0F,EAASU,GAAS,EACxB,IAAII,EAAqB,GACzB,SAASC,GAAW,CAClB,MAAMhB,EAAK7E,EAAO,UAAU,UAAU,YAAW,EACjD,OAAO6E,EAAG,QAAQ,QAAQ,GAAK,GAAKA,EAAG,QAAQ,QAAQ,EAAI,GAAKA,EAAG,QAAQ,SAAS,EAAI,CAC1F,CACA,GAAIgB,EAAQ,EAAI,CACd,MAAMhB,EAAK,OAAO7E,EAAO,UAAU,SAAS,EAC5C,GAAI6E,EAAG,SAAS,UAAU,EAAG,CAC3B,KAAM,CAACiB,EAAOC,CAAK,EAAIlB,EAAG,MAAM,UAAU,EAAE,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC,EAAE,MAAM,GAAG,EAAE,IAAImB,GAAO,OAAOA,CAAG,CAAC,EAC9FJ,EAAqBE,EAAQ,IAAMA,IAAU,IAAMC,EAAQ,CAC7D,CACF,CACA,MAAME,EAAY,+CAA+C,KAAKjG,EAAO,UAAU,SAAS,EAC1FkG,EAAkBL,EAAQ,EAC1BM,EAAYD,GAAmBD,GAAanB,EAAO,IACzD,MAAO,CACL,SAAUc,GAAsBM,EAChC,mBAAAN,EACA,UAAAO,EACA,UAAAF,CACJ,CACA,CACA,SAASG,IAAa,CACpB,OAAKV,KACHA,GAAUC,GAAW,GAEhBD,EACT,CAEA,SAASW,GAAO9E,EAAM,CACpB,GAAI,CACF,OAAAC,EACA,GAAA8E,EACA,KAAAC,CACJ,EAAMhF,EACJ,MAAMvB,EAASZ,EAAS,EACxB,IAAIoH,EAAW,KACXC,EAAiB,KACrB,MAAMC,EAAgB,IAAM,CACtB,CAAClF,GAAUA,EAAO,WAAa,CAACA,EAAO,cAC3C+E,EAAK,cAAc,EACnBA,EAAK,QAAQ,EACf,EACMI,EAAiB,IAAM,CACvB,CAACnF,GAAUA,EAAO,WAAa,CAACA,EAAO,cAC3CgF,EAAW,IAAI,eAAeI,GAAW,CACvCH,EAAiBzG,EAAO,sBAAsB,IAAM,CAClD,KAAM,CACJ,MAAA6G,EACA,OAAAC,CACV,EAAYtF,EACJ,IAAIuF,EAAWF,EACXG,EAAYF,EAChBF,EAAQ,QAAQK,GAAS,CACvB,GAAI,CACF,eAAAC,EACA,YAAAC,EACA,OAAAzI,CACZ,EAAcuI,EACAvI,GAAUA,IAAW8C,EAAO,KAChCuF,EAAWI,EAAcA,EAAY,OAASD,EAAe,CAAC,GAAKA,GAAgB,WACnFF,EAAYG,EAAcA,EAAY,QAAUD,EAAe,CAAC,GAAKA,GAAgB,UACvF,CAAC,GACGH,IAAaF,GAASG,IAAcF,IACtCJ,EAAa,CAEjB,CAAC,CACH,CAAC,EACDF,EAAS,QAAQhF,EAAO,EAAE,EAC5B,EACM4F,EAAiB,IAAM,CACvBX,GACFzG,EAAO,qBAAqByG,CAAc,EAExCD,GAAYA,EAAS,WAAahF,EAAO,KAC3CgF,EAAS,UAAUhF,EAAO,EAAE,EAC5BgF,EAAW,KAEf,EACMa,EAA2B,IAAM,CACjC,CAAC7F,GAAUA,EAAO,WAAa,CAACA,EAAO,aAC3C+E,EAAK,mBAAmB,CAC1B,EACAD,EAAG,OAAQ,IAAM,CACf,GAAI9E,EAAO,OAAO,gBAAkB,OAAOxB,EAAO,eAAmB,IAAa,CAChF2G,EAAc,EACd,MACF,CACA3G,EAAO,iBAAiB,SAAU0G,CAAa,EAC/C1G,EAAO,iBAAiB,oBAAqBqH,CAAwB,CACvE,CAAC,EACDf,EAAG,UAAW,IAAM,CAClBc,EAAc,EACdpH,EAAO,oBAAoB,SAAU0G,CAAa,EAClD1G,EAAO,oBAAoB,oBAAqBqH,CAAwB,CAC1E,CAAC,CACH,CAEA,SAASC,GAAS/F,EAAM,CACtB,GAAI,CACF,OAAAC,EACA,aAAA+F,EACA,GAAAjB,EACA,KAAAC,CACJ,EAAMhF,EACJ,MAAMiG,EAAY,CAAA,EACZxH,EAASZ,EAAS,EAClBqI,EAAS,SAAU/I,EAAQgJ,EAAS,CACpCA,IAAY,SACdA,EAAU,CAAA,GAEZ,MAAMC,EAAe3H,EAAO,kBAAoBA,EAAO,uBACjDwG,EAAW,IAAImB,EAAaC,GAAa,CAI7C,GAAIpG,EAAO,oBAAqB,OAChC,GAAIoG,EAAU,SAAW,EAAG,CAC1BrB,EAAK,iBAAkBqB,EAAU,CAAC,CAAC,EACnC,MACF,CACA,MAAMC,EAAiB,UAA0B,CAC/CtB,EAAK,iBAAkBqB,EAAU,CAAC,CAAC,CACrC,EACI5H,EAAO,sBACTA,EAAO,sBAAsB6H,CAAc,EAE3C7H,EAAO,WAAW6H,EAAgB,CAAC,CAEvC,CAAC,EACDrB,EAAS,QAAQ9H,EAAQ,CACvB,WAAY,OAAOgJ,EAAQ,WAAe,IAAc,GAAOA,EAAQ,WACvE,UAAWlG,EAAO,YAAc,OAAOkG,EAAQ,UAAc,IAAc,GAAOA,GAAS,UAC3F,cAAe,OAAOA,EAAQ,cAAkB,IAAc,GAAOA,EAAQ,aACnF,CAAK,EACDF,EAAU,KAAKhB,CAAQ,CACzB,EACMsB,EAAO,IAAM,CACjB,GAAKtG,EAAO,OAAO,SACnB,IAAIA,EAAO,OAAO,eAAgB,CAChC,MAAMuG,EAAmBhE,GAAevC,EAAO,MAAM,EACrD,QAASZ,EAAI,EAAGA,EAAImH,EAAiB,OAAQnH,GAAK,EAChD6G,EAAOM,EAAiBnH,CAAC,CAAC,CAE9B,CAEA6G,EAAOjG,EAAO,OAAQ,CACpB,UAAWA,EAAO,OAAO,oBAC/B,CAAK,EAGDiG,EAAOjG,EAAO,UAAW,CACvB,WAAY,EAClB,CAAK,EACH,EACMwG,EAAU,IAAM,CACpBR,EAAU,QAAQhB,GAAY,CAC5BA,EAAS,WAAU,CACrB,CAAC,EACDgB,EAAU,OAAO,EAAGA,EAAU,MAAM,CACtC,EACAD,EAAa,CACX,SAAU,GACV,eAAgB,GAChB,qBAAsB,EAC1B,CAAG,EACDjB,EAAG,OAAQwB,CAAI,EACfxB,EAAG,UAAW0B,CAAO,CACvB,CAIA,IAAIC,GAAgB,CAClB,GAAGC,EAAQC,EAASC,EAAU,CAC5B,MAAMC,EAAO,KAEb,GADI,CAACA,EAAK,iBAAmBA,EAAK,WAC9B,OAAOF,GAAY,WAAY,OAAOE,EAC1C,MAAMC,EAASF,EAAW,UAAY,OACtC,OAAAF,EAAO,MAAM,GAAG,EAAE,QAAQK,GAAS,CAC5BF,EAAK,gBAAgBE,CAAK,IAAGF,EAAK,gBAAgBE,CAAK,EAAI,CAAA,GAChEF,EAAK,gBAAgBE,CAAK,EAAED,CAAM,EAAEH,CAAO,CAC7C,CAAC,EACME,CACT,EACA,KAAKH,EAAQC,EAASC,EAAU,CAC9B,MAAMC,EAAO,KAEb,GADI,CAACA,EAAK,iBAAmBA,EAAK,WAC9B,OAAOF,GAAY,WAAY,OAAOE,EAC1C,SAASG,GAAc,CACrBH,EAAK,IAAIH,EAAQM,CAAW,EACxBA,EAAY,gBACd,OAAOA,EAAY,eAErB,QAASC,EAAO,UAAU,OAAQC,EAAO,IAAI,MAAMD,CAAI,EAAGE,EAAO,EAAGA,EAAOF,EAAME,IAC/ED,EAAKC,CAAI,EAAI,UAAUA,CAAI,EAE7BR,EAAQ,MAAME,EAAMK,CAAI,CAC1B,CACA,OAAAF,EAAY,eAAiBL,EACtBE,EAAK,GAAGH,EAAQM,EAAaJ,CAAQ,CAC9C,EACA,MAAMD,EAASC,EAAU,CACvB,MAAMC,EAAO,KAEb,GADI,CAACA,EAAK,iBAAmBA,EAAK,WAC9B,OAAOF,GAAY,WAAY,OAAOE,EAC1C,MAAMC,EAASF,EAAW,UAAY,OACtC,OAAIC,EAAK,mBAAmB,QAAQF,CAAO,EAAI,GAC7CE,EAAK,mBAAmBC,CAAM,EAAEH,CAAO,EAElCE,CACT,EACA,OAAOF,EAAS,CACd,MAAME,EAAO,KAEb,GADI,CAACA,EAAK,iBAAmBA,EAAK,WAC9B,CAACA,EAAK,mBAAoB,OAAOA,EACrC,MAAMO,EAAQP,EAAK,mBAAmB,QAAQF,CAAO,EACrD,OAAIS,GAAS,GACXP,EAAK,mBAAmB,OAAOO,EAAO,CAAC,EAElCP,CACT,EACA,IAAIH,EAAQC,EAAS,CACnB,MAAME,EAAO,KAEb,MADI,CAACA,EAAK,iBAAmBA,EAAK,WAC9B,CAACA,EAAK,iBACVH,EAAO,MAAM,GAAG,EAAE,QAAQK,GAAS,CAC7B,OAAOJ,EAAY,IACrBE,EAAK,gBAAgBE,CAAK,EAAI,CAAA,EACrBF,EAAK,gBAAgBE,CAAK,GACnCF,EAAK,gBAAgBE,CAAK,EAAE,QAAQ,CAACM,EAAcD,IAAU,EACvDC,IAAiBV,GAAWU,EAAa,gBAAkBA,EAAa,iBAAmBV,IAC7FE,EAAK,gBAAgBE,CAAK,EAAE,OAAOK,EAAO,CAAC,CAE/C,CAAC,CAEL,CAAC,EACMP,CACT,EACA,MAAO,CACL,MAAMA,EAAO,KAEb,GADI,CAACA,EAAK,iBAAmBA,EAAK,WAC9B,CAACA,EAAK,gBAAiB,OAAOA,EAClC,IAAIH,EACAY,EACAC,EACJ,QAASC,EAAQ,UAAU,OAAQN,EAAO,IAAI,MAAMM,CAAK,EAAGC,EAAQ,EAAGA,EAAQD,EAAOC,IACpFP,EAAKO,CAAK,EAAI,UAAUA,CAAK,EAE/B,OAAI,OAAOP,EAAK,CAAC,GAAM,UAAY,MAAM,QAAQA,EAAK,CAAC,CAAC,GACtDR,EAASQ,EAAK,CAAC,EACfI,EAAOJ,EAAK,MAAM,EAAGA,EAAK,MAAM,EAChCK,EAAUV,IAEVH,EAASQ,EAAK,CAAC,EAAE,OACjBI,EAAOJ,EAAK,CAAC,EAAE,KACfK,EAAUL,EAAK,CAAC,EAAE,SAAWL,GAE/BS,EAAK,QAAQC,CAAO,GACA,MAAM,QAAQb,CAAM,EAAIA,EAASA,EAAO,MAAM,GAAG,GACzD,QAAQK,GAAS,CACvBF,EAAK,oBAAsBA,EAAK,mBAAmB,QACrDA,EAAK,mBAAmB,QAAQQ,GAAgB,CAC9CA,EAAa,MAAME,EAAS,CAACR,EAAO,GAAGO,CAAI,CAAC,CAC9C,CAAC,EAECT,EAAK,iBAAmBA,EAAK,gBAAgBE,CAAK,GACpDF,EAAK,gBAAgBE,CAAK,EAAE,QAAQM,GAAgB,CAClDA,EAAa,MAAME,EAASD,CAAI,CAClC,CAAC,CAEL,CAAC,EACMT,CACT,CACF,EAEA,SAASa,IAAa,CACpB,MAAM1H,EAAS,KACf,IAAIqF,EACAC,EACJ,MAAM/G,EAAKyB,EAAO,GACd,OAAOA,EAAO,OAAO,MAAU,KAAeA,EAAO,OAAO,QAAU,KACxEqF,EAAQrF,EAAO,OAAO,MAEtBqF,EAAQ9G,EAAG,YAET,OAAOyB,EAAO,OAAO,OAAW,KAAeA,EAAO,OAAO,SAAW,KAC1EsF,EAAStF,EAAO,OAAO,OAEvBsF,EAAS/G,EAAG,aAEV,EAAA8G,IAAU,GAAKrF,EAAO,aAAY,GAAMsF,IAAW,GAAKtF,EAAO,gBAKnEqF,EAAQA,EAAQ,SAASlD,EAAa5D,EAAI,cAAc,GAAK,EAAG,EAAE,EAAI,SAAS4D,EAAa5D,EAAI,eAAe,GAAK,EAAG,EAAE,EACzH+G,EAASA,EAAS,SAASnD,EAAa5D,EAAI,aAAa,GAAK,EAAG,EAAE,EAAI,SAAS4D,EAAa5D,EAAI,gBAAgB,GAAK,EAAG,EAAE,EACvH,OAAO,MAAM8G,CAAK,IAAGA,EAAQ,GAC7B,OAAO,MAAMC,CAAM,IAAGA,EAAS,GACnC,OAAO,OAAOtF,EAAQ,CACpB,MAAAqF,EACA,OAAAC,EACA,KAAMtF,EAAO,aAAY,EAAKqF,EAAQC,CAC1C,CAAG,EACH,CAEA,SAASqC,IAAe,CACtB,MAAM3H,EAAS,KACf,SAAS4H,EAA0B1I,EAAM2I,EAAO,CAC9C,OAAO,WAAW3I,EAAK,iBAAiBc,EAAO,kBAAkB6H,CAAK,CAAC,GAAK,CAAC,CAC/E,CACA,MAAMC,EAAS9H,EAAO,OAChB,CACJ,UAAA+H,EACA,SAAAC,EACA,KAAMC,EACN,aAAcC,EACd,SAAAC,CACJ,EAAMnI,EACEoI,EAAYpI,EAAO,SAAW8H,EAAO,QAAQ,QAC7CO,EAAuBD,EAAYpI,EAAO,QAAQ,OAAO,OAASA,EAAO,OAAO,OAChFsI,EAASxH,EAAgBkH,EAAU,IAAIhI,EAAO,OAAO,UAAU,gBAAgB,EAC/EuI,EAAeH,EAAYpI,EAAO,QAAQ,OAAO,OAASsI,EAAO,OACvE,IAAIE,EAAW,CAAA,EACf,MAAMC,EAAa,CAAA,EACbC,EAAkB,CAAA,EACxB,IAAIC,EAAeb,EAAO,mBACtB,OAAOa,GAAiB,aAC1BA,EAAeb,EAAO,mBAAmB,KAAK9H,CAAM,GAEtD,IAAI4I,EAAcd,EAAO,kBACrB,OAAOc,GAAgB,aACzBA,EAAcd,EAAO,kBAAkB,KAAK9H,CAAM,GAEpD,MAAM6I,EAAyB7I,EAAO,SAAS,OACzC8I,EAA2B9I,EAAO,WAAW,OACnD,IAAI+I,EAAejB,EAAO,aACtBkB,EAAgB,CAACL,EACjBM,EAAgB,EAChB7B,EAAQ,EACZ,GAAI,OAAOa,EAAe,IACxB,OAEE,OAAOc,GAAiB,UAAYA,EAAa,QAAQ,GAAG,GAAK,EACnEA,EAAe,WAAWA,EAAa,QAAQ,IAAK,EAAE,CAAC,EAAI,IAAMd,EACxD,OAAOc,GAAiB,WACjCA,EAAe,WAAWA,CAAY,GAExC/I,EAAO,YAAc,CAAC+I,EAGtBT,EAAO,QAAQY,GAAW,CACpBhB,EACFgB,EAAQ,MAAM,WAAa,GAE3BA,EAAQ,MAAM,YAAc,GAE9BA,EAAQ,MAAM,aAAe,GAC7BA,EAAQ,MAAM,UAAY,EAC5B,CAAC,EAGGpB,EAAO,gBAAkBA,EAAO,UAClCnI,EAAeoI,EAAW,kCAAmC,EAAE,EAC/DpI,EAAeoI,EAAW,iCAAkC,EAAE,GAEhE,MAAMoB,EAAcrB,EAAO,MAAQA,EAAO,KAAK,KAAO,GAAK9H,EAAO,KAC9DmJ,EACFnJ,EAAO,KAAK,WAAWsI,CAAM,EACpBtI,EAAO,MAChBA,EAAO,KAAK,YAAW,EAIzB,IAAIoJ,EACJ,MAAMC,EAAuBvB,EAAO,gBAAkB,QAAUA,EAAO,aAAe,OAAO,KAAKA,EAAO,WAAW,EAAE,OAAOzK,GACpH,OAAOyK,EAAO,YAAYzK,CAAG,EAAE,cAAkB,GACzD,EAAE,OAAS,EACZ,QAAS+B,EAAI,EAAGA,EAAImJ,EAAcnJ,GAAK,EAAG,CACxCgK,EAAY,EACZ,IAAIE,EAKJ,GAJIhB,EAAOlJ,CAAC,IAAGkK,EAAQhB,EAAOlJ,CAAC,GAC3B+J,GACFnJ,EAAO,KAAK,YAAYZ,EAAGkK,EAAOhB,CAAM,EAEtC,EAAAA,EAAOlJ,CAAC,GAAK+C,EAAamH,EAAO,SAAS,IAAM,QAEpD,IAAIxB,EAAO,gBAAkB,OAAQ,CAC/BuB,IACFf,EAAOlJ,CAAC,EAAE,MAAMY,EAAO,kBAAkB,OAAO,CAAC,EAAI,IAEvD,MAAMuJ,EAAc,iBAAiBD,CAAK,EACpCE,EAAmBF,EAAM,MAAM,UAC/BG,EAAyBH,EAAM,MAAM,gBAO3C,GANIE,IACFF,EAAM,MAAM,UAAY,QAEtBG,IACFH,EAAM,MAAM,gBAAkB,QAE5BxB,EAAO,aACTsB,EAAYpJ,EAAO,aAAY,EAAKyC,GAAiB6G,EAAO,OAAa,EAAI7G,GAAiB6G,EAAO,QAAc,MAC9G,CAEL,MAAMjE,EAAQuC,EAA0B2B,EAAa,OAAO,EACtDG,EAAc9B,EAA0B2B,EAAa,cAAc,EACnEI,EAAe/B,EAA0B2B,EAAa,eAAe,EACrEK,EAAahC,EAA0B2B,EAAa,aAAa,EACjEM,EAAcjC,EAA0B2B,EAAa,cAAc,EACnEO,EAAYP,EAAY,iBAAiB,YAAY,EAC3D,GAAIO,GAAaA,IAAc,aAC7BV,EAAY/D,EAAQuE,EAAaC,MAC5B,CACL,KAAM,CACJ,YAAAE,EACA,YAAAC,EACZ,EAAcV,EACJF,EAAY/D,EAAQqE,EAAcC,EAAeC,EAAaC,GAAeG,GAAcD,EAC7F,CACF,CACIP,IACFF,EAAM,MAAM,UAAYE,GAEtBC,IACFH,EAAM,MAAM,gBAAkBG,GAE5B3B,EAAO,eAAcsB,EAAY,KAAK,MAAMA,CAAS,EAC3D,MACEA,GAAanB,GAAcH,EAAO,cAAgB,GAAKiB,GAAgBjB,EAAO,cAC1EA,EAAO,eAAcsB,EAAY,KAAK,MAAMA,CAAS,GACrDd,EAAOlJ,CAAC,IACVkJ,EAAOlJ,CAAC,EAAE,MAAMY,EAAO,kBAAkB,OAAO,CAAC,EAAI,GAAGoJ,CAAS,MAGjEd,EAAOlJ,CAAC,IACVkJ,EAAOlJ,CAAC,EAAE,gBAAkBgK,GAE9BV,EAAgB,KAAKU,CAAS,EAC1BtB,EAAO,gBACTkB,EAAgBA,EAAgBI,EAAY,EAAIH,EAAgB,EAAIF,EAChEE,IAAkB,GAAK7J,IAAM,IAAG4J,EAAgBA,EAAgBf,EAAa,EAAIc,GACjF3J,IAAM,IAAG4J,EAAgBA,EAAgBf,EAAa,EAAIc,GAC1D,KAAK,IAAIC,CAAa,EAAI,EAAI,MAAMA,EAAgB,GACpDlB,EAAO,eAAckB,EAAgB,KAAK,MAAMA,CAAa,GAC7D5B,EAAQU,EAAO,iBAAmB,GAAGU,EAAS,KAAKQ,CAAa,EACpEP,EAAW,KAAKO,CAAa,IAEzBlB,EAAO,eAAckB,EAAgB,KAAK,MAAMA,CAAa,IAC5D5B,EAAQ,KAAK,IAAIpH,EAAO,OAAO,mBAAoBoH,CAAK,GAAKpH,EAAO,OAAO,iBAAmB,GAAGwI,EAAS,KAAKQ,CAAa,EACjIP,EAAW,KAAKO,CAAa,EAC7BA,EAAgBA,EAAgBI,EAAYL,GAE9C/I,EAAO,aAAeoJ,EAAYL,EAClCE,EAAgBG,EAChBhC,GAAS,EACX,CAaA,GAZApH,EAAO,YAAc,KAAK,IAAIA,EAAO,YAAaiI,CAAU,EAAIW,EAC5DV,GAAOC,IAAaL,EAAO,SAAW,SAAWA,EAAO,SAAW,eACrEC,EAAU,MAAM,MAAQ,GAAG/H,EAAO,YAAc+I,CAAY,MAE1DjB,EAAO,iBACTC,EAAU,MAAM/H,EAAO,kBAAkB,OAAO,CAAC,EAAI,GAAGA,EAAO,YAAc+I,CAAY,MAEvFI,GACFnJ,EAAO,KAAK,kBAAkBoJ,EAAWZ,CAAQ,EAI/C,CAACV,EAAO,eAAgB,CAC1B,MAAMmC,EAAgB,CAAA,EACtB,QAAS7K,EAAI,EAAGA,EAAIoJ,EAAS,OAAQpJ,GAAK,EAAG,CAC3C,IAAI8K,EAAiB1B,EAASpJ,CAAC,EAC3B0I,EAAO,eAAcoC,EAAiB,KAAK,MAAMA,CAAc,GAC/D1B,EAASpJ,CAAC,GAAKY,EAAO,YAAciI,GACtCgC,EAAc,KAAKC,CAAc,CAErC,CACA1B,EAAWyB,EACP,KAAK,MAAMjK,EAAO,YAAciI,CAAU,EAAI,KAAK,MAAMO,EAASA,EAAS,OAAS,CAAC,CAAC,EAAI,GAC5FA,EAAS,KAAKxI,EAAO,YAAciI,CAAU,CAEjD,CACA,GAAIG,GAAaN,EAAO,KAAM,CAC5B,MAAMpF,EAAOgG,EAAgB,CAAC,EAAIK,EAClC,GAAIjB,EAAO,eAAiB,EAAG,CAC7B,MAAMqC,EAAS,KAAK,MAAMnK,EAAO,QAAQ,aAAeA,EAAO,QAAQ,aAAe8H,EAAO,cAAc,EACrGsC,EAAY1H,EAAOoF,EAAO,eAChC,QAAS1I,EAAI,EAAGA,EAAI+K,EAAQ/K,GAAK,EAC/BoJ,EAAS,KAAKA,EAASA,EAAS,OAAS,CAAC,EAAI4B,CAAS,CAE3D,CACA,QAAShL,EAAI,EAAGA,EAAIY,EAAO,QAAQ,aAAeA,EAAO,QAAQ,YAAaZ,GAAK,EAC7E0I,EAAO,iBAAmB,GAC5BU,EAAS,KAAKA,EAASA,EAAS,OAAS,CAAC,EAAI9F,CAAI,EAEpD+F,EAAW,KAAKA,EAAWA,EAAW,OAAS,CAAC,EAAI/F,CAAI,EACxD1C,EAAO,aAAe0C,CAE1B,CAEA,GADI8F,EAAS,SAAW,IAAGA,EAAW,CAAC,CAAC,GACpCO,IAAiB,EAAG,CACtB,MAAM1L,EAAM2C,EAAO,aAAY,GAAMkI,EAAM,aAAelI,EAAO,kBAAkB,aAAa,EAChGsI,EAAO,OAAO,CAAC+B,EAAGC,IACZ,CAACxC,EAAO,SAAWA,EAAO,KAAa,GACvCwC,IAAehC,EAAO,OAAS,CAIpC,EAAE,QAAQY,GAAW,CACpBA,EAAQ,MAAM7L,CAAG,EAAI,GAAG0L,CAAY,IACtC,CAAC,CACH,CACA,GAAIjB,EAAO,gBAAkBA,EAAO,qBAAsB,CACxD,IAAIyC,EAAgB,EACpB7B,EAAgB,QAAQ8B,GAAkB,CACxCD,GAAiBC,GAAkBzB,GAAgB,EACrD,CAAC,EACDwB,GAAiBxB,EACjB,MAAM0B,EAAUF,EAAgBtC,EAAasC,EAAgBtC,EAAa,EAC1EO,EAAWA,EAAS,IAAIkC,GAClBA,GAAQ,EAAU,CAAC/B,EACnB+B,EAAOD,EAAgBA,EAAU7B,EAC9B8B,CACR,CACH,CACA,GAAI5C,EAAO,yBAA0B,CACnC,IAAIyC,EAAgB,EACpB7B,EAAgB,QAAQ8B,GAAkB,CACxCD,GAAiBC,GAAkBzB,GAAgB,EACrD,CAAC,EACDwB,GAAiBxB,EACjB,MAAM4B,GAAc7C,EAAO,oBAAsB,IAAMA,EAAO,mBAAqB,GACnF,GAAIyC,EAAgBI,EAAa1C,EAAY,CAC3C,MAAM2C,GAAmB3C,EAAasC,EAAgBI,GAAc,EACpEnC,EAAS,QAAQ,CAACkC,EAAMG,IAAc,CACpCrC,EAASqC,CAAS,EAAIH,EAAOE,CAC/B,CAAC,EACDnC,EAAW,QAAQ,CAACiC,EAAMG,IAAc,CACtCpC,EAAWoC,CAAS,EAAIH,EAAOE,CACjC,CAAC,CACH,CACF,CAOA,GANA,OAAO,OAAO5K,EAAQ,CACpB,OAAAsI,EACA,SAAAE,EACA,WAAAC,EACA,gBAAAC,CACJ,CAAG,EACGZ,EAAO,gBAAkBA,EAAO,SAAW,CAACA,EAAO,qBAAsB,CAC3EnI,EAAeoI,EAAW,kCAAmC,GAAG,CAACS,EAAS,CAAC,CAAC,IAAI,EAChF7I,EAAeoI,EAAW,iCAAkC,GAAG/H,EAAO,KAAO,EAAI0I,EAAgBA,EAAgB,OAAS,CAAC,EAAI,CAAC,IAAI,EACpI,MAAMoC,EAAgB,CAAC9K,EAAO,SAAS,CAAC,EAClC+K,EAAkB,CAAC/K,EAAO,WAAW,CAAC,EAC5CA,EAAO,SAAWA,EAAO,SAAS,IAAIgL,GAAKA,EAAIF,CAAa,EAC5D9K,EAAO,WAAaA,EAAO,WAAW,IAAIgL,GAAKA,EAAID,CAAe,CACpE,CAeA,GAdIxC,IAAiBF,GACnBrI,EAAO,KAAK,oBAAoB,EAE9BwI,EAAS,SAAWK,IAClB7I,EAAO,OAAO,eAAeA,EAAO,cAAa,EACrDA,EAAO,KAAK,sBAAsB,GAEhCyI,EAAW,SAAWK,GACxB9I,EAAO,KAAK,wBAAwB,EAElC8H,EAAO,qBACT9H,EAAO,mBAAkB,EAE3BA,EAAO,KAAK,eAAe,EACvB,CAACoI,GAAa,CAACN,EAAO,UAAYA,EAAO,SAAW,SAAWA,EAAO,SAAW,QAAS,CAC5F,MAAMmD,EAAsB,GAAGnD,EAAO,sBAAsB,kBACtDoD,EAA6BlL,EAAO,GAAG,UAAU,SAASiL,CAAmB,EAC/E1C,GAAgBT,EAAO,wBACpBoD,GAA4BlL,EAAO,GAAG,UAAU,IAAIiL,CAAmB,EACnEC,GACTlL,EAAO,GAAG,UAAU,OAAOiL,CAAmB,CAElD,CACF,CAEA,SAASE,GAAiBC,EAAO,CAC/B,MAAMpL,EAAS,KACTqL,EAAe,CAAA,EACfjD,EAAYpI,EAAO,SAAWA,EAAO,OAAO,QAAQ,QAC1D,IAAIwF,EAAY,EACZpG,EACA,OAAOgM,GAAU,SACnBpL,EAAO,cAAcoL,CAAK,EACjBA,IAAU,IACnBpL,EAAO,cAAcA,EAAO,OAAO,KAAK,EAE1C,MAAMsL,EAAkBlE,GAClBgB,EACKpI,EAAO,OAAOA,EAAO,oBAAoBoH,CAAK,CAAC,EAEjDpH,EAAO,OAAOoH,CAAK,EAG5B,GAAIpH,EAAO,OAAO,gBAAkB,QAAUA,EAAO,OAAO,cAAgB,EAC1E,GAAIA,EAAO,OAAO,gBACfA,EAAO,eAAiB,IAAI,QAAQsJ,GAAS,CAC5C+B,EAAa,KAAK/B,CAAK,CACzB,CAAC,MAED,KAAKlK,EAAI,EAAGA,EAAI,KAAK,KAAKY,EAAO,OAAO,aAAa,EAAGZ,GAAK,EAAG,CAC9D,MAAMgI,EAAQpH,EAAO,YAAcZ,EACnC,GAAIgI,EAAQpH,EAAO,OAAO,QAAU,CAACoI,EAAW,MAChDiD,EAAa,KAAKC,EAAgBlE,CAAK,CAAC,CAC1C,MAGFiE,EAAa,KAAKC,EAAgBtL,EAAO,WAAW,CAAC,EAIvD,IAAKZ,EAAI,EAAGA,EAAIiM,EAAa,OAAQjM,GAAK,EACxC,GAAI,OAAOiM,EAAajM,CAAC,EAAM,IAAa,CAC1C,MAAMkG,EAAS+F,EAAajM,CAAC,EAAE,aAC/BoG,EAAYF,EAASE,EAAYF,EAASE,CAC5C,EAIEA,GAAaA,IAAc,KAAGxF,EAAO,UAAU,MAAM,OAAS,GAAGwF,CAAS,KAChF,CAEA,SAAS+F,IAAqB,CAC5B,MAAMvL,EAAS,KACTsI,EAAStI,EAAO,OAEhBwL,EAAcxL,EAAO,UAAYA,EAAO,aAAY,EAAKA,EAAO,UAAU,WAAaA,EAAO,UAAU,UAAY,EAC1H,QAASZ,EAAI,EAAGA,EAAIkJ,EAAO,OAAQlJ,GAAK,EACtCkJ,EAAOlJ,CAAC,EAAE,mBAAqBY,EAAO,aAAY,EAAKsI,EAAOlJ,CAAC,EAAE,WAAakJ,EAAOlJ,CAAC,EAAE,WAAaoM,EAAcxL,EAAO,sBAAqB,CAEnJ,CAEA,MAAMyL,GAAuB,CAACvC,EAASwC,EAAWC,IAAc,CAC1DD,GAAa,CAACxC,EAAQ,UAAU,SAASyC,CAAS,EACpDzC,EAAQ,UAAU,IAAIyC,CAAS,EACtB,CAACD,GAAaxC,EAAQ,UAAU,SAASyC,CAAS,GAC3DzC,EAAQ,UAAU,OAAOyC,CAAS,CAEtC,EACA,SAASC,GAAqBC,EAAW,CACnCA,IAAc,SAChBA,EAAY,MAAQ,KAAK,WAAa,GAExC,MAAM7L,EAAS,KACT8H,EAAS9H,EAAO,OAChB,CACJ,OAAAsI,EACA,aAAcJ,EACd,SAAAM,CACJ,EAAMxI,EACJ,GAAIsI,EAAO,SAAW,EAAG,OACrB,OAAOA,EAAO,CAAC,EAAE,kBAAsB,KAAatI,EAAO,mBAAkB,EACjF,IAAI8L,EAAe,CAACD,EAChB3D,IAAK4D,EAAeD,GACxB7L,EAAO,qBAAuB,CAAA,EAC9BA,EAAO,cAAgB,CAAA,EACvB,IAAI+I,EAAejB,EAAO,aACtB,OAAOiB,GAAiB,UAAYA,EAAa,QAAQ,GAAG,GAAK,EACnEA,EAAe,WAAWA,EAAa,QAAQ,IAAK,EAAE,CAAC,EAAI,IAAM/I,EAAO,KAC/D,OAAO+I,GAAiB,WACjCA,EAAe,WAAWA,CAAY,GAExC,QAAS3J,EAAI,EAAGA,EAAIkJ,EAAO,OAAQlJ,GAAK,EAAG,CACzC,MAAMkK,EAAQhB,EAAOlJ,CAAC,EACtB,IAAI2M,EAAczC,EAAM,kBACpBxB,EAAO,SAAWA,EAAO,iBAC3BiE,GAAezD,EAAO,CAAC,EAAE,mBAE3B,MAAM0D,GAAiBF,GAAgBhE,EAAO,eAAiB9H,EAAO,aAAY,EAAK,GAAK+L,IAAgBzC,EAAM,gBAAkBP,GAC9HkD,GAAyBH,EAAetD,EAAS,CAAC,GAAKV,EAAO,eAAiB9H,EAAO,aAAY,EAAK,GAAK+L,IAAgBzC,EAAM,gBAAkBP,GACpJmD,EAAc,EAAEJ,EAAeC,GAC/BI,EAAaD,EAAclM,EAAO,gBAAgBZ,CAAC,EACnDgN,EAAiBF,GAAe,GAAKA,GAAelM,EAAO,KAAOA,EAAO,gBAAgBZ,CAAC,EAC1FiN,EAAYH,GAAe,GAAKA,EAAclM,EAAO,KAAO,GAAKmM,EAAa,GAAKA,GAAcnM,EAAO,MAAQkM,GAAe,GAAKC,GAAcnM,EAAO,KAC3JqM,IACFrM,EAAO,cAAc,KAAKsJ,CAAK,EAC/BtJ,EAAO,qBAAqB,KAAKZ,CAAC,GAEpCqM,GAAqBnC,EAAO+C,EAAWvE,EAAO,iBAAiB,EAC/D2D,GAAqBnC,EAAO8C,EAAgBtE,EAAO,sBAAsB,EACzEwB,EAAM,SAAWpB,EAAM,CAAC8D,EAAgBA,EACxC1C,EAAM,iBAAmBpB,EAAM,CAAC+D,EAAwBA,CAC1D,CACF,CAEA,SAASK,GAAeT,EAAW,CACjC,MAAM7L,EAAS,KACf,GAAI,OAAO6L,EAAc,IAAa,CACpC,MAAMU,EAAavM,EAAO,aAAe,GAAK,EAE9C6L,EAAY7L,GAAUA,EAAO,WAAaA,EAAO,UAAYuM,GAAc,CAC7E,CACA,MAAMzE,EAAS9H,EAAO,OAChBwM,EAAiBxM,EAAO,aAAY,EAAKA,EAAO,aAAY,EAClE,GAAI,CACF,SAAAW,EACA,YAAA8L,EACA,MAAAC,EACA,aAAAC,CACJ,EAAM3M,EACJ,MAAM4M,EAAeH,EACfI,EAASH,EACf,GAAIF,IAAmB,EACrB7L,EAAW,EACX8L,EAAc,GACdC,EAAQ,OACH,CACL/L,GAAYkL,EAAY7L,EAAO,aAAY,GAAMwM,EACjD,MAAMM,EAAqB,KAAK,IAAIjB,EAAY7L,EAAO,aAAY,CAAE,EAAI,EACnE+M,EAAe,KAAK,IAAIlB,EAAY7L,EAAO,aAAY,CAAE,EAAI,EACnEyM,EAAcK,GAAsBnM,GAAY,EAChD+L,EAAQK,GAAgBpM,GAAY,EAChCmM,IAAoBnM,EAAW,GAC/BoM,IAAcpM,EAAW,EAC/B,CACA,GAAImH,EAAO,KAAM,CACf,MAAMkF,EAAkBhN,EAAO,oBAAoB,CAAC,EAC9CiN,EAAiBjN,EAAO,oBAAoBA,EAAO,OAAO,OAAS,CAAC,EACpEkN,EAAsBlN,EAAO,WAAWgN,CAAe,EACvDG,EAAqBnN,EAAO,WAAWiN,CAAc,EACrDG,EAAepN,EAAO,WAAWA,EAAO,WAAW,OAAS,CAAC,EAC7DqN,EAAe,KAAK,IAAIxB,CAAS,EACnCwB,GAAgBH,EAClBP,GAAgBU,EAAeH,GAAuBE,EAEtDT,GAAgBU,EAAeD,EAAeD,GAAsBC,EAElET,EAAe,IAAGA,GAAgB,EACxC,CACA,OAAO,OAAO3M,EAAQ,CACpB,SAAAW,EACA,aAAAgM,EACA,YAAAF,EACA,MAAAC,CACJ,CAAG,GACG5E,EAAO,qBAAuBA,EAAO,gBAAkBA,EAAO,aAAY9H,EAAO,qBAAqB6L,CAAS,EAC/GY,GAAe,CAACG,GAClB5M,EAAO,KAAK,uBAAuB,EAEjC0M,GAAS,CAACG,GACZ7M,EAAO,KAAK,iBAAiB,GAE3B4M,GAAgB,CAACH,GAAeI,GAAU,CAACH,IAC7C1M,EAAO,KAAK,UAAU,EAExBA,EAAO,KAAK,WAAYW,CAAQ,CAClC,CAEA,MAAM2M,GAAqB,CAACpE,EAASwC,EAAWC,IAAc,CACxDD,GAAa,CAACxC,EAAQ,UAAU,SAASyC,CAAS,EACpDzC,EAAQ,UAAU,IAAIyC,CAAS,EACtB,CAACD,GAAaxC,EAAQ,UAAU,SAASyC,CAAS,GAC3DzC,EAAQ,UAAU,OAAOyC,CAAS,CAEtC,EACA,SAAS4B,IAAsB,CAC7B,MAAMvN,EAAS,KACT,CACJ,OAAAsI,EACA,OAAAR,EACA,SAAAE,EACA,YAAAwF,CACJ,EAAMxN,EACEoI,EAAYpI,EAAO,SAAW8H,EAAO,QAAQ,QAC7CqB,EAAcnJ,EAAO,MAAQ8H,EAAO,MAAQA,EAAO,KAAK,KAAO,EAC/D2F,EAAmBzM,GAChBF,EAAgBkH,EAAU,IAAIF,EAAO,UAAU,GAAG9G,CAAQ,iBAAiBA,CAAQ,EAAE,EAAE,CAAC,EAEjG,IAAI0M,EACAC,EACAC,EACJ,GAAIxF,EACF,GAAIN,EAAO,KAAM,CACf,IAAIwC,EAAakD,EAAcxN,EAAO,QAAQ,aAC1CsK,EAAa,IAAGA,EAAatK,EAAO,QAAQ,OAAO,OAASsK,GAC5DA,GAActK,EAAO,QAAQ,OAAO,SAAQsK,GAActK,EAAO,QAAQ,OAAO,QACpF0N,EAAcD,EAAiB,6BAA6BnD,CAAU,IAAI,CAC5E,MACEoD,EAAcD,EAAiB,6BAA6BD,CAAW,IAAI,OAGzErE,GACFuE,EAAcpF,EAAO,KAAKY,GAAWA,EAAQ,SAAWsE,CAAW,EACnEI,EAAYtF,EAAO,KAAKY,GAAWA,EAAQ,SAAWsE,EAAc,CAAC,EACrEG,EAAYrF,EAAO,KAAKY,GAAWA,EAAQ,SAAWsE,EAAc,CAAC,GAErEE,EAAcpF,EAAOkF,CAAW,EAGhCE,IACGvE,IAEHyE,EAAY5L,GAAe0L,EAAa,IAAI5F,EAAO,UAAU,gBAAgB,EAAE,CAAC,EAC5EA,EAAO,MAAQ,CAAC8F,IAClBA,EAAYtF,EAAO,CAAC,GAItBqF,EAAY9L,GAAe6L,EAAa,IAAI5F,EAAO,UAAU,gBAAgB,EAAE,CAAC,EAC5EA,EAAO,MAAQ,CAAC6F,IAAc,IAChCA,EAAYrF,EAAOA,EAAO,OAAS,CAAC,KAI1CA,EAAO,QAAQY,GAAW,CACxBoE,GAAmBpE,EAASA,IAAYwE,EAAa5F,EAAO,gBAAgB,EAC5EwF,GAAmBpE,EAASA,IAAY0E,EAAW9F,EAAO,cAAc,EACxEwF,GAAmBpE,EAASA,IAAYyE,EAAW7F,EAAO,cAAc,CAC1E,CAAC,EACD9H,EAAO,kBAAiB,CAC1B,CAEA,MAAM6N,GAAuB,CAAC7N,EAAQ8N,IAAY,CAChD,GAAI,CAAC9N,GAAUA,EAAO,WAAa,CAACA,EAAO,OAAQ,OACnD,MAAM+N,EAAgB,IAAM/N,EAAO,UAAY,eAAiB,IAAIA,EAAO,OAAO,UAAU,GACtFkJ,EAAU4E,EAAQ,QAAQC,EAAa,CAAE,EAC/C,GAAI7E,EAAS,CACX,IAAI8E,EAAS9E,EAAQ,cAAc,IAAIlJ,EAAO,OAAO,kBAAkB,EAAE,EACrE,CAACgO,GAAUhO,EAAO,YAChBkJ,EAAQ,WACV8E,EAAS9E,EAAQ,WAAW,cAAc,IAAIlJ,EAAO,OAAO,kBAAkB,EAAE,EAGhF,sBAAsB,IAAM,CACtBkJ,EAAQ,aACV8E,EAAS9E,EAAQ,WAAW,cAAc,IAAIlJ,EAAO,OAAO,kBAAkB,EAAE,EAC5EgO,GAAQA,EAAO,OAAM,EAE7B,CAAC,GAGDA,GAAQA,EAAO,OAAM,CAC3B,CACF,EACMC,GAAS,CAACjO,EAAQoH,IAAU,CAChC,GAAI,CAACpH,EAAO,OAAOoH,CAAK,EAAG,OAC3B,MAAM0G,EAAU9N,EAAO,OAAOoH,CAAK,EAAE,cAAc,kBAAkB,EACjE0G,GAASA,EAAQ,gBAAgB,SAAS,CAChD,EACMI,GAAUlO,GAAU,CACxB,GAAI,CAACA,GAAUA,EAAO,WAAa,CAACA,EAAO,OAAQ,OACnD,IAAImO,EAASnO,EAAO,OAAO,oBAC3B,MAAMR,EAAMQ,EAAO,OAAO,OAC1B,GAAI,CAACR,GAAO,CAAC2O,GAAUA,EAAS,EAAG,OACnCA,EAAS,KAAK,IAAIA,EAAQ3O,CAAG,EAC7B,MAAM4O,EAAgBpO,EAAO,OAAO,gBAAkB,OAASA,EAAO,qBAAoB,EAAK,KAAK,KAAKA,EAAO,OAAO,aAAa,EAC9HwN,EAAcxN,EAAO,YAC3B,GAAIA,EAAO,OAAO,MAAQA,EAAO,OAAO,KAAK,KAAO,EAAG,CACrD,MAAMqO,EAAeb,EACfc,EAAiB,CAACD,EAAeF,CAAM,EAC7CG,EAAe,KAAK,GAAG,MAAM,KAAK,CAChC,OAAQH,CACd,CAAK,EAAE,IAAI,CAAC9D,EAAGjL,IACFiP,EAAeD,EAAgBhP,CACvC,CAAC,EACFY,EAAO,OAAO,QAAQ,CAACkJ,EAAS9J,IAAM,CAChCkP,EAAe,SAASpF,EAAQ,MAAM,GAAG+E,GAAOjO,EAAQZ,CAAC,CAC/D,CAAC,EACD,MACF,CACA,MAAMmP,EAAuBf,EAAcY,EAAgB,EAC3D,GAAIpO,EAAO,OAAO,QAAUA,EAAO,OAAO,KACxC,QAASZ,EAAIoO,EAAcW,EAAQ/O,GAAKmP,EAAuBJ,EAAQ/O,GAAK,EAAG,CAC7E,MAAMoP,GAAapP,EAAII,EAAMA,GAAOA,GAChCgP,EAAYhB,GAAegB,EAAYD,IAAsBN,GAAOjO,EAAQwO,CAAS,CAC3F,KAEA,SAASpP,EAAI,KAAK,IAAIoO,EAAcW,EAAQ,CAAC,EAAG/O,GAAK,KAAK,IAAImP,EAAuBJ,EAAQ3O,EAAM,CAAC,EAAGJ,GAAK,EACtGA,IAAMoO,IAAgBpO,EAAImP,GAAwBnP,EAAIoO,IACxDS,GAAOjO,EAAQZ,CAAC,CAIxB,EAEA,SAASqP,GAA0BzO,EAAQ,CACzC,KAAM,CACJ,WAAAyI,EACA,OAAAX,CACJ,EAAM9H,EACE6L,EAAY7L,EAAO,aAAeA,EAAO,UAAY,CAACA,EAAO,UACnE,IAAIwN,EACJ,QAASpO,EAAI,EAAGA,EAAIqJ,EAAW,OAAQrJ,GAAK,EACtC,OAAOqJ,EAAWrJ,EAAI,CAAC,EAAM,IAC3ByM,GAAapD,EAAWrJ,CAAC,GAAKyM,EAAYpD,EAAWrJ,EAAI,CAAC,GAAKqJ,EAAWrJ,EAAI,CAAC,EAAIqJ,EAAWrJ,CAAC,GAAK,EACtGoO,EAAcpO,EACLyM,GAAapD,EAAWrJ,CAAC,GAAKyM,EAAYpD,EAAWrJ,EAAI,CAAC,IACnEoO,EAAcpO,EAAI,GAEXyM,GAAapD,EAAWrJ,CAAC,IAClCoO,EAAcpO,GAIlB,OAAI0I,EAAO,sBACL0F,EAAc,GAAK,OAAOA,EAAgB,OAAaA,EAAc,GAEpEA,CACT,CACA,SAASkB,GAAkBC,EAAgB,CACzC,MAAM3O,EAAS,KACT6L,EAAY7L,EAAO,aAAeA,EAAO,UAAY,CAACA,EAAO,UAC7D,CACJ,SAAAwI,EACA,OAAAV,EACA,YAAa8G,EACb,UAAWC,EACX,UAAWC,CACf,EAAM9O,EACJ,IAAIwN,EAAcmB,EACd9D,EACJ,MAAMkE,EAAsBC,GAAU,CACpC,IAAIR,EAAYQ,EAAShP,EAAO,QAAQ,aACxC,OAAIwO,EAAY,IACdA,EAAYxO,EAAO,QAAQ,OAAO,OAASwO,GAEzCA,GAAaxO,EAAO,QAAQ,OAAO,SACrCwO,GAAaxO,EAAO,QAAQ,OAAO,QAE9BwO,CACT,EAIA,GAHI,OAAOhB,EAAgB,MACzBA,EAAciB,GAA0BzO,CAAM,GAE5CwI,EAAS,QAAQqD,CAAS,GAAK,EACjChB,EAAYrC,EAAS,QAAQqD,CAAS,MACjC,CACL,MAAMoD,EAAO,KAAK,IAAInH,EAAO,mBAAoB0F,CAAW,EAC5D3C,EAAYoE,EAAO,KAAK,OAAOzB,EAAcyB,GAAQnH,EAAO,cAAc,CAC5E,CAEA,GADI+C,GAAarC,EAAS,SAAQqC,EAAYrC,EAAS,OAAS,GAC5DgF,IAAgBoB,GAAiB,CAAC5O,EAAO,OAAO,KAAM,CACpD6K,IAAciE,IAChB9O,EAAO,UAAY6K,EACnB7K,EAAO,KAAK,iBAAiB,GAE/B,MACF,CACA,GAAIwN,IAAgBoB,GAAiB5O,EAAO,OAAO,MAAQA,EAAO,SAAWA,EAAO,OAAO,QAAQ,QAAS,CAC1GA,EAAO,UAAY+O,EAAoBvB,CAAW,EAClD,MACF,CACA,MAAMrE,EAAcnJ,EAAO,MAAQ8H,EAAO,MAAQA,EAAO,KAAK,KAAO,EAGrE,IAAI0G,EACJ,GAAIxO,EAAO,SAAW8H,EAAO,QAAQ,SAAWA,EAAO,KACrD0G,EAAYO,EAAoBvB,CAAW,UAClCrE,EAAa,CACtB,MAAM+F,EAAqBlP,EAAO,OAAO,KAAKkJ,GAAWA,EAAQ,SAAWsE,CAAW,EACvF,IAAI2B,EAAmB,SAASD,EAAmB,aAAa,yBAAyB,EAAG,EAAE,EAC1F,OAAO,MAAMC,CAAgB,IAC/BA,EAAmB,KAAK,IAAInP,EAAO,OAAO,QAAQkP,CAAkB,EAAG,CAAC,GAE1EV,EAAY,KAAK,MAAMW,EAAmBrH,EAAO,KAAK,IAAI,CAC5D,SAAW9H,EAAO,OAAOwN,CAAW,EAAG,CACrC,MAAMlD,EAAatK,EAAO,OAAOwN,CAAW,EAAE,aAAa,yBAAyB,EAChFlD,EACFkE,EAAY,SAASlE,EAAY,EAAE,EAEnCkE,EAAYhB,CAEhB,MACEgB,EAAYhB,EAEd,OAAO,OAAOxN,EAAQ,CACpB,kBAAA8O,EACA,UAAAjE,EACA,kBAAAgE,EACA,UAAAL,EACA,cAAAI,EACA,YAAApB,CACJ,CAAG,EACGxN,EAAO,aACTkO,GAAQlO,CAAM,EAEhBA,EAAO,KAAK,mBAAmB,EAC/BA,EAAO,KAAK,iBAAiB,GACzBA,EAAO,aAAeA,EAAO,OAAO,sBAClC6O,IAAsBL,GACxBxO,EAAO,KAAK,iBAAiB,EAE/BA,EAAO,KAAK,aAAa,EAE7B,CAEA,SAASoP,GAAmB7Q,EAAI8Q,EAAM,CACpC,MAAMrP,EAAS,KACT8H,EAAS9H,EAAO,OACtB,IAAIsJ,EAAQ/K,EAAG,QAAQ,IAAIuJ,EAAO,UAAU,gBAAgB,EACxD,CAACwB,GAAStJ,EAAO,WAAaqP,GAAQA,EAAK,OAAS,GAAKA,EAAK,SAAS9Q,CAAE,GAC3E,CAAC,GAAG8Q,EAAK,MAAMA,EAAK,QAAQ9Q,CAAE,EAAI,EAAG8Q,EAAK,MAAM,CAAC,EAAE,QAAQC,GAAU,CAC/D,CAAChG,GAASgG,EAAO,SAAWA,EAAO,QAAQ,IAAIxH,EAAO,UAAU,gBAAgB,IAClFwB,EAAQgG,EAEZ,CAAC,EAEH,IAAIC,EAAa,GACbjF,EACJ,GAAIhB,GACF,QAASlK,EAAI,EAAGA,EAAIY,EAAO,OAAO,OAAQZ,GAAK,EAC7C,GAAIY,EAAO,OAAOZ,CAAC,IAAMkK,EAAO,CAC9BiG,EAAa,GACbjF,EAAalL,EACb,KACF,EAGJ,GAAIkK,GAASiG,EACXvP,EAAO,aAAesJ,EAClBtJ,EAAO,SAAWA,EAAO,OAAO,QAAQ,QAC1CA,EAAO,aAAe,SAASsJ,EAAM,aAAa,yBAAyB,EAAG,EAAE,EAEhFtJ,EAAO,aAAesK,MAEnB,CACLtK,EAAO,aAAe,OACtBA,EAAO,aAAe,OACtB,MACF,CACI8H,EAAO,qBAAuB9H,EAAO,eAAiB,QAAaA,EAAO,eAAiBA,EAAO,aACpGA,EAAO,oBAAmB,CAE9B,CAEA,IAAIwP,GAAS,CACX,WAAA9H,GACA,aAAAC,GACA,iBAAAwD,GACA,mBAAAI,GACA,qBAAAK,GACA,eAAAU,GACA,oBAAAiB,GACA,kBAAAmB,GACA,mBAAAU,EACF,EAEA,SAASK,GAAmB9Q,EAAM,CAC5BA,IAAS,SACXA,EAAO,KAAK,aAAY,EAAK,IAAM,KAErC,MAAMqB,EAAS,KACT,CACJ,OAAA8H,EACA,aAAcI,EACd,UAAA2D,EACA,UAAA9D,CACJ,EAAM/H,EACJ,GAAI8H,EAAO,iBACT,OAAOI,EAAM,CAAC2D,EAAYA,EAE5B,GAAI/D,EAAO,QACT,OAAO+D,EAET,IAAI6D,EAAmBhR,GAAaqJ,EAAWpJ,CAAI,EACnD,OAAA+Q,GAAoB1P,EAAO,sBAAqB,EAC5CkI,IAAKwH,EAAmB,CAACA,GACtBA,GAAoB,CAC7B,CAEA,SAASC,GAAa9D,EAAW+D,EAAc,CAC7C,MAAM5P,EAAS,KACT,CACJ,aAAckI,EACd,OAAAJ,EACA,UAAAC,EACA,SAAApH,CACJ,EAAMX,EACJ,IAAI6P,EAAI,EACJC,EAAI,EACR,MAAMC,EAAI,EACN/P,EAAO,eACT6P,EAAI3H,EAAM,CAAC2D,EAAYA,EAEvBiE,EAAIjE,EAEF/D,EAAO,eACT+H,EAAI,KAAK,MAAMA,CAAC,EAChBC,EAAI,KAAK,MAAMA,CAAC,GAElB9P,EAAO,kBAAoBA,EAAO,UAClCA,EAAO,UAAYA,EAAO,aAAY,EAAK6P,EAAIC,EAC3ChI,EAAO,QACTC,EAAU/H,EAAO,aAAY,EAAK,aAAe,WAAW,EAAIA,EAAO,aAAY,EAAK,CAAC6P,EAAI,CAACC,EACpFhI,EAAO,mBACb9H,EAAO,eACT6P,GAAK7P,EAAO,sBAAqB,EAEjC8P,GAAK9P,EAAO,sBAAqB,EAEnC+H,EAAU,MAAM,UAAY,eAAe8H,CAAC,OAAOC,CAAC,OAAOC,CAAC,OAI9D,IAAIC,EACJ,MAAMxD,EAAiBxM,EAAO,aAAY,EAAKA,EAAO,aAAY,EAC9DwM,IAAmB,EACrBwD,EAAc,EAEdA,GAAenE,EAAY7L,EAAO,aAAY,GAAMwM,EAElDwD,IAAgBrP,GAClBX,EAAO,eAAe6L,CAAS,EAEjC7L,EAAO,KAAK,eAAgBA,EAAO,UAAW4P,CAAY,CAC5D,CAEA,SAASK,IAAe,CACtB,MAAO,CAAC,KAAK,SAAS,CAAC,CACzB,CAEA,SAASC,IAAe,CACtB,MAAO,CAAC,KAAK,SAAS,KAAK,SAAS,OAAS,CAAC,CAChD,CAEA,SAASC,GAAYtE,EAAWT,EAAOgF,EAAcC,EAAiBC,EAAU,CAC1EzE,IAAc,SAChBA,EAAY,GAEVT,IAAU,SACZA,EAAQ,KAAK,OAAO,OAElBgF,IAAiB,SACnBA,EAAe,IAEbC,IAAoB,SACtBA,EAAkB,IAEpB,MAAMrQ,EAAS,KACT,CACJ,OAAA8H,EACA,UAAAC,CACJ,EAAM/H,EACJ,GAAIA,EAAO,WAAa8H,EAAO,+BAC7B,MAAO,GAET,MAAMmI,EAAejQ,EAAO,aAAY,EAClCkQ,EAAelQ,EAAO,aAAY,EACxC,IAAIuQ,EAKJ,GAJIF,GAAmBxE,EAAYoE,EAAcM,EAAeN,EAAsBI,GAAmBxE,EAAYqE,EAAcK,EAAeL,EAAkBK,EAAe1E,EAGnL7L,EAAO,eAAeuQ,CAAY,EAC9BzI,EAAO,QAAS,CAClB,MAAM0I,EAAMxQ,EAAO,aAAY,EAC/B,GAAIoL,IAAU,EACZrD,EAAUyI,EAAM,aAAe,WAAW,EAAI,CAACD,MAC1C,CACL,GAAI,CAACvQ,EAAO,QAAQ,aAClB,OAAAF,GAAqB,CACnB,OAAAE,EACA,eAAgB,CAACuQ,EACjB,KAAMC,EAAM,OAAS,KAC/B,CAAS,EACM,GAETzI,EAAU,SAAS,CACjB,CAACyI,EAAM,OAAS,KAAK,EAAG,CAACD,EACzB,SAAU,QAClB,CAAO,CACH,CACA,MAAO,EACT,CACA,OAAInF,IAAU,GACZpL,EAAO,cAAc,CAAC,EACtBA,EAAO,aAAauQ,CAAY,EAC5BH,IACFpQ,EAAO,KAAK,wBAAyBoL,EAAOkF,CAAQ,EACpDtQ,EAAO,KAAK,eAAe,KAG7BA,EAAO,cAAcoL,CAAK,EAC1BpL,EAAO,aAAauQ,CAAY,EAC5BH,IACFpQ,EAAO,KAAK,wBAAyBoL,EAAOkF,CAAQ,EACpDtQ,EAAO,KAAK,iBAAiB,GAE1BA,EAAO,YACVA,EAAO,UAAY,GACdA,EAAO,oCACVA,EAAO,kCAAoC,SAAuByQ,EAAG,CAC/D,CAACzQ,GAAUA,EAAO,WAClByQ,EAAE,SAAW,OACjBzQ,EAAO,UAAU,oBAAoB,gBAAiBA,EAAO,iCAAiC,EAC9FA,EAAO,kCAAoC,KAC3C,OAAOA,EAAO,kCACdA,EAAO,UAAY,GACfoQ,GACFpQ,EAAO,KAAK,eAAe,EAE/B,GAEFA,EAAO,UAAU,iBAAiB,gBAAiBA,EAAO,iCAAiC,IAGxF,EACT,CAEA,IAAI6L,GAAY,CACd,aAAc4D,GACd,aAAAE,GACA,aAAAM,GACA,aAAAC,GACA,YAAAC,EACF,EAEA,SAASO,GAAcpQ,EAAUsP,EAAc,CAC7C,MAAM5P,EAAS,KACVA,EAAO,OAAO,UACjBA,EAAO,UAAU,MAAM,mBAAqB,GAAGM,CAAQ,KACvDN,EAAO,UAAU,MAAM,gBAAkBM,IAAa,EAAI,MAAQ,IAEpEN,EAAO,KAAK,gBAAiBM,EAAUsP,CAAY,CACrD,CAEA,SAASe,GAAe5Q,EAAM,CAC5B,GAAI,CACF,OAAAC,EACA,aAAAoQ,EACA,UAAAQ,EACA,KAAAC,CACJ,EAAM9Q,EACJ,KAAM,CACJ,YAAAyN,EACA,cAAAoB,CACJ,EAAM5O,EACJ,IAAIO,EAAMqQ,EACLrQ,IACCiN,EAAcoB,EAAerO,EAAM,OAAgBiN,EAAcoB,EAAerO,EAAM,OAAYA,EAAM,SAE9GP,EAAO,KAAK,aAAa6Q,CAAI,EAAE,EAC3BT,GAAgB7P,IAAQ,QAC1BP,EAAO,KAAK,uBAAuB6Q,CAAI,EAAE,EAChCT,GAAgB5C,IAAgBoB,IACzC5O,EAAO,KAAK,wBAAwB6Q,CAAI,EAAE,EACtCtQ,IAAQ,OACVP,EAAO,KAAK,sBAAsB6Q,CAAI,EAAE,EAExC7Q,EAAO,KAAK,sBAAsB6Q,CAAI,EAAE,EAG9C,CAEA,SAASC,GAAgBV,EAAcQ,EAAW,CAC5CR,IAAiB,SACnBA,EAAe,IAEjB,MAAMpQ,EAAS,KACT,CACJ,OAAA8H,CACJ,EAAM9H,EACA8H,EAAO,UACPA,EAAO,YACT9H,EAAO,iBAAgB,EAEzB2Q,GAAe,CACb,OAAA3Q,EACA,aAAAoQ,EACA,UAAAQ,EACA,KAAM,OACV,CAAG,EACH,CAEA,SAASG,GAAcX,EAAcQ,EAAW,CAC1CR,IAAiB,SACnBA,EAAe,IAEjB,MAAMpQ,EAAS,KACT,CACJ,OAAA8H,CACJ,EAAM9H,EACJA,EAAO,UAAY,GACf,CAAA8H,EAAO,UACX9H,EAAO,cAAc,CAAC,EACtB2Q,GAAe,CACb,OAAA3Q,EACA,aAAAoQ,EACA,UAAAQ,EACA,KAAM,KACV,CAAG,EACH,CAEA,IAAII,GAAa,CACf,cAAAN,GACA,gBAAAI,GACA,cAAAC,EACF,EAEA,SAASE,GAAQ7J,EAAOgE,EAAOgF,EAAcE,EAAUY,EAAS,CAC1D9J,IAAU,SACZA,EAAQ,GAENgJ,IAAiB,SACnBA,EAAe,IAEb,OAAOhJ,GAAU,WACnBA,EAAQ,SAASA,EAAO,EAAE,GAE5B,MAAMpH,EAAS,KACf,IAAIsK,EAAalD,EACbkD,EAAa,IAAGA,EAAa,GACjC,KAAM,CACJ,OAAAxC,EACA,SAAAU,EACA,WAAAC,EACA,cAAAmG,EACA,YAAApB,EACA,aAActF,EACd,UAAAH,EACA,QAAAoJ,CACJ,EAAMnR,EACJ,GAAI,CAACmR,GAAW,CAACb,GAAY,CAACY,GAAWlR,EAAO,WAAaA,EAAO,WAAa8H,EAAO,+BACtF,MAAO,GAEL,OAAOsD,EAAU,MACnBA,EAAQpL,EAAO,OAAO,OAExB,MAAMiP,EAAO,KAAK,IAAIjP,EAAO,OAAO,mBAAoBsK,CAAU,EAClE,IAAIO,EAAYoE,EAAO,KAAK,OAAO3E,EAAa2E,GAAQjP,EAAO,OAAO,cAAc,EAChF6K,GAAarC,EAAS,SAAQqC,EAAYrC,EAAS,OAAS,GAChE,MAAMqD,EAAY,CAACrD,EAASqC,CAAS,EAErC,GAAI/C,EAAO,oBACT,QAAS1I,EAAI,EAAGA,EAAIqJ,EAAW,OAAQrJ,GAAK,EAAG,CAC7C,MAAMgS,EAAsB,CAAC,KAAK,MAAMvF,EAAY,GAAG,EACjDwF,EAAiB,KAAK,MAAM5I,EAAWrJ,CAAC,EAAI,GAAG,EAC/CkS,EAAqB,KAAK,MAAM7I,EAAWrJ,EAAI,CAAC,EAAI,GAAG,EACzD,OAAOqJ,EAAWrJ,EAAI,CAAC,EAAM,IAC3BgS,GAAuBC,GAAkBD,EAAsBE,GAAsBA,EAAqBD,GAAkB,EAC9H/G,EAAalL,EACJgS,GAAuBC,GAAkBD,EAAsBE,IACxEhH,EAAalL,EAAI,GAEVgS,GAAuBC,IAChC/G,EAAalL,EAEjB,CAGF,GAAIY,EAAO,aAAesK,IAAekD,IACnC,CAACxN,EAAO,iBAAmBkI,EAAM2D,EAAY7L,EAAO,WAAa6L,EAAY7L,EAAO,aAAY,EAAK6L,EAAY7L,EAAO,WAAa6L,EAAY7L,EAAO,aAAY,IAGpK,CAACA,EAAO,gBAAkB6L,EAAY7L,EAAO,WAAa6L,EAAY7L,EAAO,iBAC1EwN,GAAe,KAAOlD,GACzB,MAAO,GAITA,KAAgBsE,GAAiB,IAAMwB,GACzCpQ,EAAO,KAAK,wBAAwB,EAItCA,EAAO,eAAe6L,CAAS,EAC/B,IAAI+E,EACAtG,EAAakD,EAAaoD,EAAY,OAAgBtG,EAAakD,EAAaoD,EAAY,OAAYA,EAAY,QAGxH,MAAMxI,EAAYpI,EAAO,SAAWA,EAAO,OAAO,QAAQ,QAG1D,GAAI,EAFqBoI,GAAa8I,KAEZhJ,GAAO,CAAC2D,IAAc7L,EAAO,WAAa,CAACkI,GAAO2D,IAAc7L,EAAO,WAC/F,OAAAA,EAAO,kBAAkBsK,CAAU,EAE/BxC,EAAO,YACT9H,EAAO,iBAAgB,EAEzBA,EAAO,oBAAmB,EACtB8H,EAAO,SAAW,SACpB9H,EAAO,aAAa6L,CAAS,EAE3B+E,IAAc,UAChB5Q,EAAO,gBAAgBoQ,EAAcQ,CAAS,EAC9C5Q,EAAO,cAAcoQ,EAAcQ,CAAS,GAEvC,GAET,GAAI9I,EAAO,QAAS,CAClB,MAAM0I,EAAMxQ,EAAO,aAAY,EACzBuR,EAAIrJ,EAAM2D,EAAY,CAACA,EAC7B,GAAIT,IAAU,EACRhD,IACFpI,EAAO,UAAU,MAAM,eAAiB,OACxCA,EAAO,kBAAoB,IAEzBoI,GAAa,CAACpI,EAAO,2BAA6BA,EAAO,OAAO,aAAe,GACjFA,EAAO,0BAA4B,GACnC,sBAAsB,IAAM,CAC1B+H,EAAUyI,EAAM,aAAe,WAAW,EAAIe,CAChD,CAAC,GAEDxJ,EAAUyI,EAAM,aAAe,WAAW,EAAIe,EAE5CnJ,GACF,sBAAsB,IAAM,CAC1BpI,EAAO,UAAU,MAAM,eAAiB,GACxCA,EAAO,kBAAoB,EAC7B,CAAC,MAEE,CACL,GAAI,CAACA,EAAO,QAAQ,aAClB,OAAAF,GAAqB,CACnB,OAAAE,EACA,eAAgBuR,EAChB,KAAMf,EAAM,OAAS,KAC/B,CAAS,EACM,GAETzI,EAAU,SAAS,CACjB,CAACyI,EAAM,OAAS,KAAK,EAAGe,EACxB,SAAU,QAClB,CAAO,CACH,CACA,MAAO,EACT,CAEA,MAAMlN,EADUO,GAAU,EACD,SACzB,OAAIwD,GAAa,CAAC8I,GAAW7M,GAAYrE,EAAO,WAC9CA,EAAO,QAAQ,OAAO,GAAO,GAAOsK,CAAU,EAEhDtK,EAAO,cAAcoL,CAAK,EAC1BpL,EAAO,aAAa6L,CAAS,EAC7B7L,EAAO,kBAAkBsK,CAAU,EACnCtK,EAAO,oBAAmB,EAC1BA,EAAO,KAAK,wBAAyBoL,EAAOkF,CAAQ,EACpDtQ,EAAO,gBAAgBoQ,EAAcQ,CAAS,EAC1CxF,IAAU,EACZpL,EAAO,cAAcoQ,EAAcQ,CAAS,EAClC5Q,EAAO,YACjBA,EAAO,UAAY,GACdA,EAAO,gCACVA,EAAO,8BAAgC,SAAuByQ,EAAG,CAC3D,CAACzQ,GAAUA,EAAO,WAClByQ,EAAE,SAAW,OACjBzQ,EAAO,UAAU,oBAAoB,gBAAiBA,EAAO,6BAA6B,EAC1FA,EAAO,8BAAgC,KACvC,OAAOA,EAAO,8BACdA,EAAO,cAAcoQ,EAAcQ,CAAS,EAC9C,GAEF5Q,EAAO,UAAU,iBAAiB,gBAAiBA,EAAO,6BAA6B,GAElF,EACT,CAEA,SAASwR,GAAYpK,EAAOgE,EAAOgF,EAAcE,EAAU,CACrDlJ,IAAU,SACZA,EAAQ,GAENgJ,IAAiB,SACnBA,EAAe,IAEb,OAAOhJ,GAAU,WAEnBA,EADsB,SAASA,EAAO,EAAE,GAG1C,MAAMpH,EAAS,KACf,GAAIA,EAAO,UAAW,OAClB,OAAOoL,EAAU,MACnBA,EAAQpL,EAAO,OAAO,OAExB,MAAMmJ,EAAcnJ,EAAO,MAAQA,EAAO,OAAO,MAAQA,EAAO,OAAO,KAAK,KAAO,EACnF,IAAIyR,EAAWrK,EACf,GAAIpH,EAAO,OAAO,KAChB,GAAIA,EAAO,SAAWA,EAAO,OAAO,QAAQ,QAE1CyR,EAAWA,EAAWzR,EAAO,QAAQ,iBAChC,CACL,IAAI0R,EACJ,GAAIvI,EAAa,CACf,MAAMmB,EAAamH,EAAWzR,EAAO,OAAO,KAAK,KACjD0R,EAAmB1R,EAAO,OAAO,KAAKkJ,GAAWA,EAAQ,aAAa,yBAAyB,EAAI,IAAMoB,CAAU,EAAE,MACvH,MACEoH,EAAmB1R,EAAO,oBAAoByR,CAAQ,EAExD,MAAME,EAAOxI,EAAc,KAAK,KAAKnJ,EAAO,OAAO,OAASA,EAAO,OAAO,KAAK,IAAI,EAAIA,EAAO,OAAO,OAC/F,CACJ,eAAA4R,CACR,EAAU5R,EAAO,OACX,IAAIoO,EAAgBpO,EAAO,OAAO,cAC9BoO,IAAkB,OACpBA,EAAgBpO,EAAO,qBAAoB,GAE3CoO,EAAgB,KAAK,KAAK,WAAWpO,EAAO,OAAO,cAAe,EAAE,CAAC,EACjE4R,GAAkBxD,EAAgB,IAAM,IAC1CA,EAAgBA,EAAgB,IAGpC,IAAIyD,EAAcF,EAAOD,EAAmBtD,EAO5C,GANIwD,IACFC,EAAcA,GAAeH,EAAmB,KAAK,KAAKtD,EAAgB,CAAC,GAEzEkC,GAAYsB,GAAkB5R,EAAO,OAAO,gBAAkB,QAAU,CAACmJ,IAC3E0I,EAAc,IAEZA,EAAa,CACf,MAAMjB,EAAYgB,EAAiBF,EAAmB1R,EAAO,YAAc,OAAS,OAAS0R,EAAmB1R,EAAO,YAAc,EAAIA,EAAO,OAAO,cAAgB,OAAS,OAChLA,EAAO,QAAQ,CACb,UAAA4Q,EACA,QAAS,GACT,iBAAkBA,IAAc,OAASc,EAAmB,EAAIA,EAAmBC,EAAO,EAC1F,eAAgBf,IAAc,OAAS5Q,EAAO,UAAY,MACpE,CAAS,CACH,CACA,GAAImJ,EAAa,CACf,MAAMmB,EAAamH,EAAWzR,EAAO,OAAO,KAAK,KACjDyR,EAAWzR,EAAO,OAAO,KAAKkJ,GAAWA,EAAQ,aAAa,yBAAyB,EAAI,IAAMoB,CAAU,EAAE,MAC/G,MACEmH,EAAWzR,EAAO,oBAAoByR,CAAQ,CAElD,CAEF,6BAAsB,IAAM,CAC1BzR,EAAO,QAAQyR,EAAUrG,EAAOgF,EAAcE,CAAQ,CACxD,CAAC,EACMtQ,CACT,CAGA,SAAS8R,GAAU1G,EAAOgF,EAAcE,EAAU,CAC5CF,IAAiB,SACnBA,EAAe,IAEjB,MAAMpQ,EAAS,KACT,CACJ,QAAAmR,EACA,OAAArJ,EACA,UAAAiK,CACJ,EAAM/R,EACJ,GAAI,CAACmR,GAAWnR,EAAO,UAAW,OAAOA,EACrC,OAAOoL,EAAU,MACnBA,EAAQpL,EAAO,OAAO,OAExB,IAAIgS,EAAWlK,EAAO,eAClBA,EAAO,gBAAkB,QAAUA,EAAO,iBAAmB,GAAKA,EAAO,qBAC3EkK,EAAW,KAAK,IAAIhS,EAAO,qBAAqB,UAAW,EAAI,EAAG,CAAC,GAErE,MAAMiS,EAAYjS,EAAO,YAAc8H,EAAO,mBAAqB,EAAIkK,EACjE5J,EAAYpI,EAAO,SAAW8H,EAAO,QAAQ,QACnD,GAAIA,EAAO,KAAM,CACf,GAAIiK,GAAa,CAAC3J,GAAaN,EAAO,oBAAqB,MAAO,GAMlE,GALA9H,EAAO,QAAQ,CACb,UAAW,MACjB,CAAK,EAEDA,EAAO,YAAcA,EAAO,UAAU,WAClCA,EAAO,cAAgBA,EAAO,OAAO,OAAS,GAAK8H,EAAO,QAC5D,6BAAsB,IAAM,CAC1B9H,EAAO,QAAQA,EAAO,YAAciS,EAAW7G,EAAOgF,EAAcE,CAAQ,CAC9E,CAAC,EACM,EAEX,CACA,OAAIxI,EAAO,QAAU9H,EAAO,MACnBA,EAAO,QAAQ,EAAGoL,EAAOgF,EAAcE,CAAQ,EAEjDtQ,EAAO,QAAQA,EAAO,YAAciS,EAAW7G,EAAOgF,EAAcE,CAAQ,CACrF,CAGA,SAAS4B,GAAU9G,EAAOgF,EAAcE,EAAU,CAC5CF,IAAiB,SACnBA,EAAe,IAEjB,MAAMpQ,EAAS,KACT,CACJ,OAAA8H,EACA,SAAAU,EACA,WAAAC,EACA,aAAA0J,EACA,QAAAhB,EACA,UAAAY,CACJ,EAAM/R,EACJ,GAAI,CAACmR,GAAWnR,EAAO,UAAW,OAAOA,EACrC,OAAOoL,EAAU,MACnBA,EAAQpL,EAAO,OAAO,OAExB,MAAMoI,EAAYpI,EAAO,SAAW8H,EAAO,QAAQ,QACnD,GAAIA,EAAO,KAAM,CACf,GAAIiK,GAAa,CAAC3J,GAAaN,EAAO,oBAAqB,MAAO,GAClE9H,EAAO,QAAQ,CACb,UAAW,MACjB,CAAK,EAEDA,EAAO,YAAcA,EAAO,UAAU,UACxC,CACA,MAAM6L,EAAYsG,EAAenS,EAAO,UAAY,CAACA,EAAO,UAC5D,SAASoS,EAAUC,EAAK,CACtB,OAAIA,EAAM,EAAU,CAAC,KAAK,MAAM,KAAK,IAAIA,CAAG,CAAC,EACtC,KAAK,MAAMA,CAAG,CACvB,CACA,MAAMjB,EAAsBgB,EAAUvG,CAAS,EACzCyG,EAAqB9J,EAAS,IAAI6J,GAAOD,EAAUC,CAAG,CAAC,EACvDE,EAAazK,EAAO,UAAYA,EAAO,SAAS,QACtD,IAAI0K,EAAWhK,EAAS8J,EAAmB,QAAQlB,CAAmB,EAAI,CAAC,EAC3E,GAAI,OAAOoB,EAAa,MAAgB1K,EAAO,SAAWyK,GAAa,CACrE,IAAIE,EACJjK,EAAS,QAAQ,CAACkC,EAAMG,IAAc,CAChCuG,GAAuB1G,IAEzB+H,EAAgB5H,EAEpB,CAAC,EACG,OAAO4H,EAAkB,MAC3BD,EAAWD,EAAa/J,EAASiK,CAAa,EAAIjK,EAASiK,EAAgB,EAAIA,EAAgB,EAAIA,CAAa,EAEpH,CACA,IAAIC,EAAY,EAShB,GARI,OAAOF,EAAa,MACtBE,EAAYjK,EAAW,QAAQ+J,CAAQ,EACnCE,EAAY,IAAGA,EAAY1S,EAAO,YAAc,GAChD8H,EAAO,gBAAkB,QAAUA,EAAO,iBAAmB,GAAKA,EAAO,qBAC3E4K,EAAYA,EAAY1S,EAAO,qBAAqB,WAAY,EAAI,EAAI,EACxE0S,EAAY,KAAK,IAAIA,EAAW,CAAC,IAGjC5K,EAAO,QAAU9H,EAAO,YAAa,CACvC,MAAM2S,EAAY3S,EAAO,OAAO,SAAWA,EAAO,OAAO,QAAQ,SAAWA,EAAO,QAAUA,EAAO,QAAQ,OAAO,OAAS,EAAIA,EAAO,OAAO,OAAS,EACvJ,OAAOA,EAAO,QAAQ2S,EAAWvH,EAAOgF,EAAcE,CAAQ,CAChE,SAAWxI,EAAO,MAAQ9H,EAAO,cAAgB,GAAK8H,EAAO,QAC3D,6BAAsB,IAAM,CAC1B9H,EAAO,QAAQ0S,EAAWtH,EAAOgF,EAAcE,CAAQ,CACzD,CAAC,EACM,GAET,OAAOtQ,EAAO,QAAQ0S,EAAWtH,EAAOgF,EAAcE,CAAQ,CAChE,CAGA,SAASsC,GAAWxH,EAAOgF,EAAcE,EAAU,CAC7CF,IAAiB,SACnBA,EAAe,IAEjB,MAAMpQ,EAAS,KACf,GAAI,CAAAA,EAAO,UACX,OAAI,OAAOoL,EAAU,MACnBA,EAAQpL,EAAO,OAAO,OAEjBA,EAAO,QAAQA,EAAO,YAAaoL,EAAOgF,EAAcE,CAAQ,CACzE,CAGA,SAASuC,GAAezH,EAAOgF,EAAcE,EAAUwC,EAAW,CAC5D1C,IAAiB,SACnBA,EAAe,IAEb0C,IAAc,SAChBA,EAAY,IAEd,MAAM9S,EAAS,KACf,GAAIA,EAAO,UAAW,OAClB,OAAOoL,EAAU,MACnBA,EAAQpL,EAAO,OAAO,OAExB,IAAIoH,EAAQpH,EAAO,YACnB,MAAMiP,EAAO,KAAK,IAAIjP,EAAO,OAAO,mBAAoBoH,CAAK,EACvDyD,EAAYoE,EAAO,KAAK,OAAO7H,EAAQ6H,GAAQjP,EAAO,OAAO,cAAc,EAC3E6L,EAAY7L,EAAO,aAAeA,EAAO,UAAY,CAACA,EAAO,UACnE,GAAI6L,GAAa7L,EAAO,SAAS6K,CAAS,EAAG,CAG3C,MAAMkI,EAAc/S,EAAO,SAAS6K,CAAS,EACvCmI,EAAWhT,EAAO,SAAS6K,EAAY,CAAC,EAC1CgB,EAAYkH,GAAeC,EAAWD,GAAeD,IACvD1L,GAASpH,EAAO,OAAO,eAE3B,KAAO,CAGL,MAAMwS,EAAWxS,EAAO,SAAS6K,EAAY,CAAC,EACxCkI,EAAc/S,EAAO,SAAS6K,CAAS,EACzCgB,EAAY2G,IAAaO,EAAcP,GAAYM,IACrD1L,GAASpH,EAAO,OAAO,eAE3B,CACA,OAAAoH,EAAQ,KAAK,IAAIA,EAAO,CAAC,EACzBA,EAAQ,KAAK,IAAIA,EAAOpH,EAAO,WAAW,OAAS,CAAC,EAC7CA,EAAO,QAAQoH,EAAOgE,EAAOgF,EAAcE,CAAQ,CAC5D,CAEA,SAAS2C,IAAsB,CAC7B,MAAMjT,EAAS,KACf,GAAIA,EAAO,UAAW,OACtB,KAAM,CACJ,OAAA8H,EACA,SAAAE,CACJ,EAAMhI,EACEoO,EAAgBtG,EAAO,gBAAkB,OAAS9H,EAAO,qBAAoB,EAAK8H,EAAO,cAC/F,IAAIoL,EAAelT,EAAO,sBAAsBA,EAAO,YAAY,EAC/DwO,EACJ,MAAMT,EAAgB/N,EAAO,UAAY,eAAiB,IAAI8H,EAAO,UAAU,GACzEqL,EAASnT,EAAO,MAAQA,EAAO,OAAO,MAAQA,EAAO,OAAO,KAAK,KAAO,EAC9E,GAAI8H,EAAO,KAAM,CACf,GAAI9H,EAAO,UAAW,OACtBwO,EAAY,SAASxO,EAAO,aAAa,aAAa,yBAAyB,EAAG,EAAE,EAChF8H,EAAO,eACT9H,EAAO,YAAYwO,CAAS,EACnB0E,GAAgBC,GAAUnT,EAAO,OAAO,OAASoO,GAAiB,GAAKpO,EAAO,OAAO,KAAK,KAAO,GAAKA,EAAO,OAAO,OAASoO,IACtIpO,EAAO,QAAO,EACdkT,EAAelT,EAAO,cAAcc,EAAgBkH,EAAU,GAAG+F,CAAa,6BAA6BS,CAAS,IAAI,EAAE,CAAC,CAAC,EAC5HrQ,GAAS,IAAM,CACb6B,EAAO,QAAQkT,CAAY,CAC7B,CAAC,GAEDlT,EAAO,QAAQkT,CAAY,CAE/B,MACElT,EAAO,QAAQkT,CAAY,CAE/B,CAEA,IAAI5J,GAAQ,CACV,QAAA2H,GACA,YAAAO,GACA,UAAAM,GACA,UAAAI,GACA,WAAAU,GACA,eAAAC,GACA,oBAAAI,EACF,EAEA,SAASG,GAAWC,EAAgBnC,EAAS,CAC3C,MAAMlR,EAAS,KACT,CACJ,OAAA8H,EACA,SAAAE,CACJ,EAAMhI,EACJ,GAAI,CAAC8H,EAAO,MAAQ9H,EAAO,SAAWA,EAAO,OAAO,QAAQ,QAAS,OACrE,MAAMsT,EAAa,IAAM,CACRxS,EAAgBkH,EAAU,IAAIF,EAAO,UAAU,gBAAgB,EACvE,QAAQ,CAACvJ,EAAI6I,IAAU,CAC5B7I,EAAG,aAAa,0BAA2B6I,CAAK,CAClD,CAAC,CACH,EACMmM,EAAmB,IAAM,CAC7B,MAAMjL,EAASxH,EAAgBkH,EAAU,IAAIF,EAAO,eAAe,EAAE,EACrEQ,EAAO,QAAQ/J,GAAM,CACnBA,EAAG,OAAM,CACX,CAAC,EACG+J,EAAO,OAAS,IAClBtI,EAAO,aAAY,EACnBA,EAAO,aAAY,EAEvB,EACMmJ,EAAcnJ,EAAO,MAAQ8H,EAAO,MAAQA,EAAO,KAAK,KAAO,EACjEA,EAAO,qBAAuBA,EAAO,eAAiB,GAAKqB,IAC7DoK,EAAgB,EAElB,MAAMC,EAAiB1L,EAAO,gBAAkBqB,EAAcrB,EAAO,KAAK,KAAO,GAC3E2L,EAAkBzT,EAAO,OAAO,OAASwT,IAAmB,EAC5DE,EAAiBvK,GAAenJ,EAAO,OAAO,OAAS8H,EAAO,KAAK,OAAS,EAC5E6L,EAAiBC,GAAkB,CACvC,QAASxU,EAAI,EAAGA,EAAIwU,EAAgBxU,GAAK,EAAG,CAC1C,MAAM8J,EAAUlJ,EAAO,UAAY2B,GAAc,eAAgB,CAACmG,EAAO,eAAe,CAAC,EAAInG,GAAc,MAAO,CAACmG,EAAO,WAAYA,EAAO,eAAe,CAAC,EAC7J9H,EAAO,SAAS,OAAOkJ,CAAO,CAChC,CACF,EACA,GAAIuK,EAAiB,CACnB,GAAI3L,EAAO,mBAAoB,CAC7B,MAAM+L,EAAcL,EAAiBxT,EAAO,OAAO,OAASwT,EAC5DG,EAAeE,CAAW,EAC1B7T,EAAO,aAAY,EACnBA,EAAO,aAAY,CACrB,MACEyB,GAAY,iLAAiL,EAE/L6R,EAAU,CACZ,SAAWI,EAAgB,CACzB,GAAI5L,EAAO,mBAAoB,CAC7B,MAAM+L,EAAc/L,EAAO,KAAK,KAAO9H,EAAO,OAAO,OAAS8H,EAAO,KAAK,KAC1E6L,EAAeE,CAAW,EAC1B7T,EAAO,aAAY,EACnBA,EAAO,aAAY,CACrB,MACEyB,GAAY,4KAA4K,EAE1L6R,EAAU,CACZ,MACEA,EAAU,EAEZtT,EAAO,QAAQ,CACb,eAAAqT,EACA,UAAWvL,EAAO,eAAiB,OAAY,OAC/C,QAAAoJ,CACJ,CAAG,CACH,CAEA,SAAS4C,GAAQ5Q,EAAO,CACtB,GAAI,CACF,eAAAmQ,EACA,QAAApC,EAAU,GACV,UAAAL,EACA,aAAAjB,EACA,iBAAAR,EACA,QAAA+B,EACA,aAAAtB,EACA,aAAAmE,CACJ,EAAM7Q,IAAU,OAAS,CAAA,EAAKA,EAC5B,MAAMlD,EAAS,KACf,GAAI,CAACA,EAAO,OAAO,KAAM,OACzBA,EAAO,KAAK,eAAe,EAC3B,KAAM,CACJ,OAAAsI,EACA,eAAA0L,EACA,eAAAC,EACA,SAAAjM,EACA,OAAAF,CACJ,EAAM9H,EACE,CACJ,eAAA4R,EACA,aAAAsC,CACJ,EAAMpM,EAGJ,GAFA9H,EAAO,eAAiB,GACxBA,EAAO,eAAiB,GACpBA,EAAO,SAAW8H,EAAO,QAAQ,QAAS,CACxCmJ,IACE,CAACnJ,EAAO,gBAAkB9H,EAAO,YAAc,EACjDA,EAAO,QAAQA,EAAO,QAAQ,OAAO,OAAQ,EAAG,GAAO,EAAI,EAClD8H,EAAO,gBAAkB9H,EAAO,UAAY8H,EAAO,cAC5D9H,EAAO,QAAQA,EAAO,QAAQ,OAAO,OAASA,EAAO,UAAW,EAAG,GAAO,EAAI,EACrEA,EAAO,YAAcA,EAAO,SAAS,OAAS,GACvDA,EAAO,QAAQA,EAAO,QAAQ,aAAc,EAAG,GAAO,EAAI,GAG9DA,EAAO,eAAiBgU,EACxBhU,EAAO,eAAiBiU,EACxBjU,EAAO,KAAK,SAAS,EACrB,MACF,CACA,IAAIoO,EAAgBtG,EAAO,cACvBsG,IAAkB,OACpBA,EAAgBpO,EAAO,qBAAoB,GAE3CoO,EAAgB,KAAK,KAAK,WAAWtG,EAAO,cAAe,EAAE,CAAC,EAC1D8J,GAAkBxD,EAAgB,IAAM,IAC1CA,EAAgBA,EAAgB,IAGpC,MAAMoF,EAAiB1L,EAAO,mBAAqBsG,EAAgBtG,EAAO,eAC1E,IAAIqM,EAAevC,EAAiB,KAAK,IAAI4B,EAAgB,KAAK,KAAKpF,EAAgB,CAAC,CAAC,EAAIoF,EACzFW,EAAeX,IAAmB,IACpCW,GAAgBX,EAAiBW,EAAeX,GAElDW,GAAgBrM,EAAO,qBACvB9H,EAAO,aAAemU,EACtB,MAAMhL,EAAcnJ,EAAO,MAAQ8H,EAAO,MAAQA,EAAO,KAAK,KAAO,EACjEQ,EAAO,OAAS8F,EAAgB+F,GAAgBnU,EAAO,OAAO,SAAW,SAAWsI,EAAO,OAAS8F,EAAgB+F,EAAe,EACrI1S,GAAY,0OAA0O,EAC7O0H,GAAerB,EAAO,KAAK,OAAS,OAC7CrG,GAAY,yEAAyE,EAEvF,MAAM2S,EAAuB,CAAA,EACvBC,EAAsB,CAAA,EACtB1C,EAAOxI,EAAc,KAAK,KAAKb,EAAO,OAASR,EAAO,KAAK,IAAI,EAAIQ,EAAO,OAC1EgM,EAAoBpD,GAAWS,EAAOuC,EAAe9F,GAAiB,CAACwD,EAC7E,IAAIpE,EAAc8G,EAAoBJ,EAAelU,EAAO,YACxD,OAAOmP,EAAqB,IAC9BA,EAAmBnP,EAAO,cAAcsI,EAAO,KAAK/J,GAAMA,EAAG,UAAU,SAASuJ,EAAO,gBAAgB,CAAC,CAAC,EAEzG0F,EAAc2B,EAEhB,MAAMoF,EAAS3D,IAAc,QAAU,CAACA,EAClC4D,EAAS5D,IAAc,QAAU,CAACA,EACxC,IAAI6D,EAAkB,EAClBC,EAAiB,EAErB,MAAMC,GADiBxL,EAAcb,EAAO6G,CAAgB,EAAE,OAASA,IACrByC,GAAkB,OAAOjC,EAAiB,IAAc,CAACvB,EAAgB,EAAI,GAAM,GAErI,GAAIuG,EAA0BR,EAAc,CAC1CM,EAAkB,KAAK,IAAIN,EAAeQ,EAAyBnB,CAAc,EACjF,QAASpU,EAAI,EAAGA,EAAI+U,EAAeQ,EAAyBvV,GAAK,EAAG,CAClE,MAAMgI,EAAQhI,EAAI,KAAK,MAAMA,EAAIuS,CAAI,EAAIA,EACzC,GAAIxI,EAAa,CACf,MAAMyL,EAAoBjD,EAAOvK,EAAQ,EACzC,QAAShI,EAAIkJ,EAAO,OAAS,EAAGlJ,GAAK,EAAGA,GAAK,EACvCkJ,EAAOlJ,CAAC,EAAE,SAAWwV,GAAmBR,EAAqB,KAAKhV,CAAC,CAK3E,MACEgV,EAAqB,KAAKzC,EAAOvK,EAAQ,CAAC,CAE9C,CACF,SAAWuN,EAA0BvG,EAAgBuD,EAAOwC,EAAc,CACxEO,EAAiB,KAAK,IAAIC,GAA2BhD,EAAOwC,EAAe,GAAIX,CAAc,EACzFc,IACFI,EAAiB,KAAK,IAAIA,EAAgBtG,EAAgBuD,EAAOuC,EAAe,CAAC,GAEnF,QAAS9U,EAAI,EAAGA,EAAIsV,EAAgBtV,GAAK,EAAG,CAC1C,MAAMgI,EAAQhI,EAAI,KAAK,MAAMA,EAAIuS,CAAI,EAAIA,EACrCxI,EACFb,EAAO,QAAQ,CAACgB,EAAOgB,IAAe,CAChChB,EAAM,SAAWlC,GAAOiN,EAAoB,KAAK/J,CAAU,CACjE,CAAC,EAED+J,EAAoB,KAAKjN,CAAK,CAElC,CACF,CAsCA,GArCApH,EAAO,oBAAsB,GAC7B,sBAAsB,IAAM,CAC1BA,EAAO,oBAAsB,EAC/B,CAAC,EACGA,EAAO,OAAO,SAAW,SAAWsI,EAAO,OAAS8F,EAAgB+F,EAAe,IACjFE,EAAoB,SAASlF,CAAgB,GAC/CkF,EAAoB,OAAOA,EAAoB,QAAQlF,CAAgB,EAAG,CAAC,EAEzEiF,EAAqB,SAASjF,CAAgB,GAChDiF,EAAqB,OAAOA,EAAqB,QAAQjF,CAAgB,EAAG,CAAC,GAG7EqF,GACFJ,EAAqB,QAAQhN,GAAS,CACpCkB,EAAOlB,CAAK,EAAE,kBAAoB,GAClCY,EAAS,QAAQM,EAAOlB,CAAK,CAAC,EAC9BkB,EAAOlB,CAAK,EAAE,kBAAoB,EACpC,CAAC,EAECmN,GACFF,EAAoB,QAAQjN,GAAS,CACnCkB,EAAOlB,CAAK,EAAE,kBAAoB,GAClCY,EAAS,OAAOM,EAAOlB,CAAK,CAAC,EAC7BkB,EAAOlB,CAAK,EAAE,kBAAoB,EACpC,CAAC,EAEHpH,EAAO,aAAY,EACf8H,EAAO,gBAAkB,OAC3B9H,EAAO,aAAY,EACVmJ,IAAgBiL,EAAqB,OAAS,GAAKI,GAAUH,EAAoB,OAAS,GAAKE,IACxGvU,EAAO,OAAO,QAAQ,CAACsJ,EAAOgB,IAAe,CAC3CtK,EAAO,KAAK,YAAYsK,EAAYhB,EAAOtJ,EAAO,MAAM,CAC1D,CAAC,EAEC8H,EAAO,qBACT9H,EAAO,mBAAkB,EAEvBiR,GACF,GAAImD,EAAqB,OAAS,GAAKI,GACrC,GAAI,OAAOnB,EAAmB,IAAa,CACzC,MAAMwB,EAAwB7U,EAAO,WAAWwN,CAAW,EAErDsH,EADoB9U,EAAO,WAAWwN,EAAciH,CAAe,EACxCI,EAC7Bd,EACF/T,EAAO,aAAaA,EAAO,UAAY8U,CAAI,GAE3C9U,EAAO,QAAQwN,EAAc,KAAK,KAAKiH,CAAe,EAAG,EAAG,GAAO,EAAI,EACnE9E,IACF3P,EAAO,gBAAgB,eAAiBA,EAAO,gBAAgB,eAAiB8U,EAChF9U,EAAO,gBAAgB,iBAAmBA,EAAO,gBAAgB,iBAAmB8U,GAG1F,SACMnF,EAAc,CAChB,MAAMoF,EAAQ5L,EAAciL,EAAqB,OAAStM,EAAO,KAAK,KAAOsM,EAAqB,OAClGpU,EAAO,QAAQA,EAAO,YAAc+U,EAAO,EAAG,GAAO,EAAI,EACzD/U,EAAO,gBAAgB,iBAAmBA,EAAO,SACnD,UAEOqU,EAAoB,OAAS,GAAKE,EAC3C,GAAI,OAAOlB,EAAmB,IAAa,CACzC,MAAMwB,EAAwB7U,EAAO,WAAWwN,CAAW,EAErDsH,EADoB9U,EAAO,WAAWwN,EAAckH,CAAc,EACvCG,EAC7Bd,EACF/T,EAAO,aAAaA,EAAO,UAAY8U,CAAI,GAE3C9U,EAAO,QAAQwN,EAAckH,EAAgB,EAAG,GAAO,EAAI,EACvD/E,IACF3P,EAAO,gBAAgB,eAAiBA,EAAO,gBAAgB,eAAiB8U,EAChF9U,EAAO,gBAAgB,iBAAmBA,EAAO,gBAAgB,iBAAmB8U,GAG1F,KAAO,CACL,MAAMC,EAAQ5L,EAAckL,EAAoB,OAASvM,EAAO,KAAK,KAAOuM,EAAoB,OAChGrU,EAAO,QAAQA,EAAO,YAAc+U,EAAO,EAAG,GAAO,EAAI,CAC3D,EAKJ,GAFA/U,EAAO,eAAiBgU,EACxBhU,EAAO,eAAiBiU,EACpBjU,EAAO,YAAcA,EAAO,WAAW,SAAW,CAAC4P,EAAc,CACnE,MAAMoF,EAAa,CACjB,eAAA3B,EACA,UAAAzC,EACA,aAAAjB,EACA,iBAAAR,EACA,aAAc,EACpB,EACQ,MAAM,QAAQnP,EAAO,WAAW,OAAO,EACzCA,EAAO,WAAW,QAAQ,QAAQhC,GAAK,CACjC,CAACA,EAAE,WAAaA,EAAE,OAAO,MAAMA,EAAE,QAAQ,CAC3C,GAAGgX,EACH,QAAShX,EAAE,OAAO,gBAAkB8J,EAAO,cAAgBmJ,EAAU,EAC/E,CAAS,CACH,CAAC,EACQjR,EAAO,WAAW,mBAAmBA,EAAO,aAAeA,EAAO,WAAW,QAAQ,OAAO,MACrGA,EAAO,WAAW,QAAQ,QAAQ,CAChC,GAAGgV,EACH,QAAShV,EAAO,WAAW,QAAQ,OAAO,gBAAkB8H,EAAO,cAAgBmJ,EAAU,EACrG,CAAO,CAEL,CACAjR,EAAO,KAAK,SAAS,CACvB,CAEA,SAASiV,IAAc,CACrB,MAAMjV,EAAS,KACT,CACJ,OAAA8H,EACA,SAAAE,CACJ,EAAMhI,EACJ,GAAI,CAAC8H,EAAO,MAAQ,CAACE,GAAYhI,EAAO,SAAWA,EAAO,OAAO,QAAQ,QAAS,OAClFA,EAAO,aAAY,EACnB,MAAMkV,EAAiB,CAAA,EACvBlV,EAAO,OAAO,QAAQkJ,GAAW,CAC/B,MAAM9B,EAAQ,OAAO8B,EAAQ,iBAAqB,IAAcA,EAAQ,aAAa,yBAAyB,EAAI,EAAIA,EAAQ,iBAC9HgM,EAAe9N,CAAK,EAAI8B,CAC1B,CAAC,EACDlJ,EAAO,OAAO,QAAQkJ,GAAW,CAC/BA,EAAQ,gBAAgB,yBAAyB,CACnD,CAAC,EACDgM,EAAe,QAAQhM,GAAW,CAChClB,EAAS,OAAOkB,CAAO,CACzB,CAAC,EACDlJ,EAAO,aAAY,EACnBA,EAAO,QAAQA,EAAO,UAAW,CAAC,CACpC,CAEA,IAAImV,GAAO,CACT,WAAA/B,GACA,QAAAU,GACA,YAAAmB,EACF,EAEA,SAASG,GAAcC,EAAQ,CAC7B,MAAMrV,EAAS,KACf,GAAI,CAACA,EAAO,OAAO,eAAiBA,EAAO,OAAO,eAAiBA,EAAO,UAAYA,EAAO,OAAO,QAAS,OAC7G,MAAMzB,EAAKyB,EAAO,OAAO,oBAAsB,YAAcA,EAAO,GAAKA,EAAO,UAC5EA,EAAO,YACTA,EAAO,oBAAsB,IAE/BzB,EAAG,MAAM,OAAS,OAClBA,EAAG,MAAM,OAAS8W,EAAS,WAAa,OACpCrV,EAAO,WACT,sBAAsB,IAAM,CAC1BA,EAAO,oBAAsB,EAC/B,CAAC,CAEL,CAEA,SAASsV,IAAkB,CACzB,MAAMtV,EAAS,KACXA,EAAO,OAAO,eAAiBA,EAAO,UAAYA,EAAO,OAAO,UAGhEA,EAAO,YACTA,EAAO,oBAAsB,IAE/BA,EAAOA,EAAO,OAAO,oBAAsB,YAAc,KAAO,WAAW,EAAE,MAAM,OAAS,GACxFA,EAAO,WACT,sBAAsB,IAAM,CAC1BA,EAAO,oBAAsB,EAC/B,CAAC,EAEL,CAEA,IAAIuV,GAAa,CACf,cAAAH,GACA,gBAAAE,EACF,EAGA,SAASE,GAAexU,EAAUyU,EAAM,CAClCA,IAAS,SACXA,EAAO,MAET,SAASC,EAAcnX,EAAI,CACzB,GAAI,CAACA,GAAMA,IAAOhB,EAAW,GAAMgB,IAAOX,EAAS,EAAI,OAAO,KAC1DW,EAAG,eAAcA,EAAKA,EAAG,cAC7B,MAAMoX,EAAQpX,EAAG,QAAQyC,CAAQ,EACjC,MAAI,CAAC2U,GAAS,CAACpX,EAAG,YACT,KAEFoX,GAASD,EAAcnX,EAAG,YAAW,EAAG,IAAI,CACrD,CACA,OAAOmX,EAAcD,CAAI,CAC3B,CACA,SAASG,GAAiB5V,EAAQ+G,EAAO8O,EAAQ,CAC/C,MAAMrX,EAASZ,EAAS,EAClB,CACJ,OAAAkK,CACJ,EAAM9H,EACE8V,EAAqBhO,EAAO,mBAC5BiO,EAAqBjO,EAAO,mBAClC,OAAIgO,IAAuBD,GAAUE,GAAsBF,GAAUrX,EAAO,WAAauX,GACnFD,IAAuB,WACzB/O,EAAM,eAAc,EACb,IAEF,GAEF,EACT,CACA,SAASiP,GAAajP,EAAO,CAC3B,MAAM/G,EAAS,KACT8C,EAAWvF,EAAW,EAC5B,IAAIkT,EAAI1J,EACJ0J,EAAE,gBAAeA,EAAIA,EAAE,eAC3B,MAAMnJ,EAAOtH,EAAO,gBACpB,GAAIyQ,EAAE,OAAS,cAAe,CAC5B,GAAInJ,EAAK,YAAc,MAAQA,EAAK,YAAcmJ,EAAE,UAClD,OAEFnJ,EAAK,UAAYmJ,EAAE,SACrB,MAAWA,EAAE,OAAS,cAAgBA,EAAE,cAAc,SAAW,IAC/DnJ,EAAK,QAAUmJ,EAAE,cAAc,CAAC,EAAE,YAEpC,GAAIA,EAAE,OAAS,aAAc,CAE3BmF,GAAiB5V,EAAQyQ,EAAGA,EAAE,cAAc,CAAC,EAAE,KAAK,EACpD,MACF,CACA,KAAM,CACJ,OAAA3I,EACA,QAAAmO,EACA,QAAA9E,CACJ,EAAMnR,EAGJ,GAFI,CAACmR,GACD,CAACrJ,EAAO,eAAiB2I,EAAE,cAAgB,SAC3CzQ,EAAO,WAAa8H,EAAO,+BAC7B,OAEE,CAAC9H,EAAO,WAAa8H,EAAO,SAAWA,EAAO,MAChD9H,EAAO,QAAO,EAEhB,IAAIkW,EAAWzF,EAAE,OAMjB,GALI3I,EAAO,oBAAsB,WAC3B,CAACxG,GAAiB4U,EAAUlW,EAAO,SAAS,GAE9C,UAAWyQ,GAAKA,EAAE,QAAU,GAC5B,WAAYA,GAAKA,EAAE,OAAS,GAC5BnJ,EAAK,WAAaA,EAAK,QAAS,OAGpC,MAAM6O,EAAuB,CAAC,CAACrO,EAAO,gBAAkBA,EAAO,iBAAmB,GAE5EsO,EAAY3F,EAAE,aAAeA,EAAE,aAAY,EAAKA,EAAE,KACpD0F,GAAwB1F,EAAE,QAAUA,EAAE,OAAO,YAAc2F,IAC7DF,EAAWE,EAAU,CAAC,GAExB,MAAMC,EAAoBvO,EAAO,kBAAoBA,EAAO,kBAAoB,IAAIA,EAAO,cAAc,GACnGwO,EAAiB,CAAC,EAAE7F,EAAE,QAAUA,EAAE,OAAO,YAG/C,GAAI3I,EAAO,YAAcwO,EAAiBd,GAAea,EAAmBH,CAAQ,EAAIA,EAAS,QAAQG,CAAiB,GAAI,CAC5HrW,EAAO,WAAa,GACpB,MACF,CACA,GAAI8H,EAAO,cACL,CAACoO,EAAS,QAAQpO,EAAO,YAAY,EAAG,OAE9CmO,EAAQ,SAAWxF,EAAE,MACrBwF,EAAQ,SAAWxF,EAAE,MACrB,MAAMoF,EAASI,EAAQ,SACjBM,EAASN,EAAQ,SAIvB,GAAI,CAACL,GAAiB5V,EAAQyQ,EAAGoF,CAAM,EACrC,OAEF,OAAO,OAAOvO,EAAM,CAClB,UAAW,GACX,QAAS,GACT,oBAAqB,GACrB,YAAa,OACb,YAAa,MACjB,CAAG,EACD2O,EAAQ,OAASJ,EACjBI,EAAQ,OAASM,EACjBjP,EAAK,eAAiBjJ,EAAG,EACzB2B,EAAO,WAAa,GACpBA,EAAO,WAAU,EACjBA,EAAO,eAAiB,OACpB8H,EAAO,UAAY,IAAGR,EAAK,mBAAqB,IACpD,IAAIkP,EAAiB,GACjBN,EAAS,QAAQ5O,EAAK,iBAAiB,IACzCkP,EAAiB,GACbN,EAAS,WAAa,WACxB5O,EAAK,UAAY,KAGjBxE,EAAS,eAAiBA,EAAS,cAAc,QAAQwE,EAAK,iBAAiB,GAAKxE,EAAS,gBAAkBoT,IAAazF,EAAE,cAAgB,SAAWA,EAAE,cAAgB,SAAW,CAACyF,EAAS,QAAQ5O,EAAK,iBAAiB,IAChOxE,EAAS,cAAc,KAAI,EAE7B,MAAM2T,EAAuBD,GAAkBxW,EAAO,gBAAkB8H,EAAO,0BAC1EA,EAAO,+BAAiC2O,IAAyB,CAACP,EAAS,mBAC9EzF,EAAE,eAAc,EAEd3I,EAAO,UAAYA,EAAO,SAAS,SAAW9H,EAAO,UAAYA,EAAO,WAAa,CAAC8H,EAAO,SAC/F9H,EAAO,SAAS,aAAY,EAE9BA,EAAO,KAAK,aAAcyQ,CAAC,CAC7B,CAEA,SAASiG,GAAY3P,EAAO,CAC1B,MAAMjE,EAAWvF,EAAW,EACtByC,EAAS,KACTsH,EAAOtH,EAAO,gBACd,CACJ,OAAA8H,EACA,QAAAmO,EACA,aAAc/N,EACd,QAAAiJ,CACJ,EAAMnR,EAEJ,GADI,CAACmR,GACD,CAACrJ,EAAO,eAAiBf,EAAM,cAAgB,QAAS,OAC5D,IAAI0J,EAAI1J,EAER,GADI0J,EAAE,gBAAeA,EAAIA,EAAE,eACvBA,EAAE,OAAS,gBACTnJ,EAAK,UAAY,MACVmJ,EAAE,YACFnJ,EAAK,WAAW,OAE7B,IAAIqP,EACJ,GAAIlG,EAAE,OAAS,aAEb,GADAkG,EAAc,CAAC,GAAGlG,EAAE,cAAc,EAAE,KAAKc,GAAKA,EAAE,aAAejK,EAAK,OAAO,EACvE,CAACqP,GAAeA,EAAY,aAAerP,EAAK,QAAS,YAE7DqP,EAAclG,EAEhB,GAAI,CAACnJ,EAAK,UAAW,CACfA,EAAK,aAAeA,EAAK,aAC3BtH,EAAO,KAAK,oBAAqByQ,CAAC,EAEpC,MACF,CACA,MAAMmG,EAAQD,EAAY,MACpBE,EAAQF,EAAY,MAC1B,GAAIlG,EAAE,wBAAyB,CAC7BwF,EAAQ,OAASW,EACjBX,EAAQ,OAASY,EACjB,MACF,CACA,GAAI,CAAC7W,EAAO,eAAgB,CACrByQ,EAAE,OAAO,QAAQnJ,EAAK,iBAAiB,IAC1CtH,EAAO,WAAa,IAElBsH,EAAK,YACP,OAAO,OAAO2O,EAAS,CACrB,OAAQW,EACR,OAAQC,EACR,SAAUD,EACV,SAAUC,CAClB,CAAO,EACDvP,EAAK,eAAiBjJ,EAAG,GAE3B,MACF,CACA,GAAIyJ,EAAO,qBAAuB,CAACA,EAAO,KACxC,GAAI9H,EAAO,cAET,GAAI6W,EAAQZ,EAAQ,QAAUjW,EAAO,WAAaA,EAAO,aAAY,GAAM6W,EAAQZ,EAAQ,QAAUjW,EAAO,WAAaA,EAAO,eAAgB,CAC9IsH,EAAK,UAAY,GACjBA,EAAK,QAAU,GACf,MACF,MACK,IAAIY,IAAQ0O,EAAQX,EAAQ,QAAU,CAACjW,EAAO,WAAaA,EAAO,aAAY,GAAM4W,EAAQX,EAAQ,QAAU,CAACjW,EAAO,WAAaA,EAAO,aAAY,GAC3J,OACK,GAAI,CAACkI,IAAQ0O,EAAQX,EAAQ,QAAUjW,EAAO,WAAaA,EAAO,aAAY,GAAM4W,EAAQX,EAAQ,QAAUjW,EAAO,WAAaA,EAAO,aAAY,GAC1J,OAMJ,GAHI8C,EAAS,eAAiBA,EAAS,cAAc,QAAQwE,EAAK,iBAAiB,GAAKxE,EAAS,gBAAkB2N,EAAE,QAAUA,EAAE,cAAgB,SAC/I3N,EAAS,cAAc,KAAI,EAEzBA,EAAS,eACP2N,EAAE,SAAW3N,EAAS,eAAiB2N,EAAE,OAAO,QAAQnJ,EAAK,iBAAiB,EAAG,CACnFA,EAAK,QAAU,GACftH,EAAO,WAAa,GACpB,MACF,CAEEsH,EAAK,qBACPtH,EAAO,KAAK,YAAayQ,CAAC,EAE5BwF,EAAQ,UAAYA,EAAQ,SAC5BA,EAAQ,UAAYA,EAAQ,SAC5BA,EAAQ,SAAWW,EACnBX,EAAQ,SAAWY,EACnB,MAAMC,EAAQb,EAAQ,SAAWA,EAAQ,OACnCc,EAAQd,EAAQ,SAAWA,EAAQ,OACzC,GAAIjW,EAAO,OAAO,WAAa,KAAK,KAAK8W,GAAS,EAAIC,GAAS,CAAC,EAAI/W,EAAO,OAAO,UAAW,OAC7F,GAAI,OAAOsH,EAAK,YAAgB,IAAa,CAC3C,IAAI0P,EACAhX,EAAO,aAAY,GAAMiW,EAAQ,WAAaA,EAAQ,QAAUjW,EAAO,WAAU,GAAMiW,EAAQ,WAAaA,EAAQ,OACtH3O,EAAK,YAAc,GAGfwP,EAAQA,EAAQC,EAAQA,GAAS,KACnCC,EAAa,KAAK,MAAM,KAAK,IAAID,CAAK,EAAG,KAAK,IAAID,CAAK,CAAC,EAAI,IAAM,KAAK,GACvExP,EAAK,YAActH,EAAO,eAAiBgX,EAAalP,EAAO,WAAa,GAAKkP,EAAalP,EAAO,WAG3G,CASA,GARIR,EAAK,aACPtH,EAAO,KAAK,oBAAqByQ,CAAC,EAEhC,OAAOnJ,EAAK,YAAgB,MAC1B2O,EAAQ,WAAaA,EAAQ,QAAUA,EAAQ,WAAaA,EAAQ,UACtE3O,EAAK,YAAc,IAGnBA,EAAK,aAAemJ,EAAE,OAAS,aAAenJ,EAAK,gCAAiC,CACtFA,EAAK,UAAY,GACjB,MACF,CACA,GAAI,CAACA,EAAK,YACR,OAEFtH,EAAO,WAAa,GAChB,CAAC8H,EAAO,SAAW2I,EAAE,YACvBA,EAAE,eAAc,EAEd3I,EAAO,0BAA4B,CAACA,EAAO,QAC7C2I,EAAE,gBAAe,EAEnB,IAAIqE,EAAO9U,EAAO,aAAY,EAAK8W,EAAQC,EACvCE,EAAcjX,EAAO,aAAY,EAAKiW,EAAQ,SAAWA,EAAQ,UAAYA,EAAQ,SAAWA,EAAQ,UACxGnO,EAAO,iBACTgN,EAAO,KAAK,IAAIA,CAAI,GAAK5M,EAAM,EAAI,IACnC+O,EAAc,KAAK,IAAIA,CAAW,GAAK/O,EAAM,EAAI,KAEnD+N,EAAQ,KAAOnB,EACfA,GAAQhN,EAAO,WACXI,IACF4M,EAAO,CAACA,EACRmC,EAAc,CAACA,GAEjB,MAAMC,EAAuBlX,EAAO,iBACpCA,EAAO,eAAiB8U,EAAO,EAAI,OAAS,OAC5C9U,EAAO,iBAAmBiX,EAAc,EAAI,OAAS,OACrD,MAAME,EAASnX,EAAO,OAAO,MAAQ,CAAC8H,EAAO,QACvCsP,EAAepX,EAAO,mBAAqB,QAAUA,EAAO,gBAAkBA,EAAO,mBAAqB,QAAUA,EAAO,eACjI,GAAI,CAACsH,EAAK,QAAS,CAQjB,GAPI6P,GAAUC,GACZpX,EAAO,QAAQ,CACb,UAAWA,EAAO,cAC1B,CAAO,EAEHsH,EAAK,eAAiBtH,EAAO,aAAY,EACzCA,EAAO,cAAc,CAAC,EAClBA,EAAO,UAAW,CACpB,MAAMqX,EAAM,IAAI,OAAO,YAAY,gBAAiB,CAClD,QAAS,GACT,WAAY,GACZ,OAAQ,CACN,kBAAmB,EAC7B,CACA,CAAO,EACDrX,EAAO,UAAU,cAAcqX,CAAG,CACpC,CACA/P,EAAK,oBAAsB,GAEvBQ,EAAO,aAAe9H,EAAO,iBAAmB,IAAQA,EAAO,iBAAmB,KACpFA,EAAO,cAAc,EAAI,EAE3BA,EAAO,KAAK,kBAAmByQ,CAAC,CAClC,CAGA,GADA,IAAI,KAAI,EAAG,QAAO,EACd3I,EAAO,iBAAmB,IAASR,EAAK,SAAWA,EAAK,oBAAsB4P,IAAyBlX,EAAO,kBAAoBmX,GAAUC,GAAgB,KAAK,IAAItC,CAAI,GAAK,EAAG,CACnL,OAAO,OAAOmB,EAAS,CACrB,OAAQW,EACR,OAAQC,EACR,SAAUD,EACV,SAAUC,EACV,eAAgBvP,EAAK,gBAC3B,CAAK,EACDA,EAAK,cAAgB,GACrBA,EAAK,eAAiBA,EAAK,iBAC3B,MACF,CACAtH,EAAO,KAAK,aAAcyQ,CAAC,EAC3BnJ,EAAK,QAAU,GACfA,EAAK,iBAAmBwN,EAAOxN,EAAK,eACpC,IAAIgQ,EAAsB,GACtBC,EAAkBzP,EAAO,gBAiD7B,GAhDIA,EAAO,sBACTyP,EAAkB,GAEhBzC,EAAO,GACLqC,GAAUC,GAA8B9P,EAAK,oBAAsBA,EAAK,kBAAoBQ,EAAO,eAAiB9H,EAAO,eAAiBA,EAAO,gBAAgBA,EAAO,YAAc,CAAC,GAAK8H,EAAO,gBAAkB,QAAU9H,EAAO,OAAO,OAAS8H,EAAO,eAAiB,EAAI9H,EAAO,gBAAgBA,EAAO,YAAc,CAAC,EAAIA,EAAO,OAAO,aAAe,GAAKA,EAAO,OAAO,aAAeA,EAAO,aAAY,IACzZA,EAAO,QAAQ,CACb,UAAW,OACX,aAAc,GACd,iBAAkB,CAC1B,CAAO,EAECsH,EAAK,iBAAmBtH,EAAO,aAAY,IAC7CsX,EAAsB,GAClBxP,EAAO,aACTR,EAAK,iBAAmBtH,EAAO,aAAY,EAAK,GAAK,CAACA,EAAO,aAAY,EAAKsH,EAAK,eAAiBwN,IAASyC,KAGxGzC,EAAO,IACZqC,GAAUC,GAA8B9P,EAAK,oBAAsBA,EAAK,kBAAoBQ,EAAO,eAAiB9H,EAAO,aAAY,EAAKA,EAAO,gBAAgBA,EAAO,gBAAgB,OAAS,CAAC,EAAIA,EAAO,OAAO,cAAgB8H,EAAO,gBAAkB,QAAU9H,EAAO,OAAO,OAAS8H,EAAO,eAAiB,EAAI9H,EAAO,gBAAgBA,EAAO,gBAAgB,OAAS,CAAC,EAAIA,EAAO,OAAO,aAAe,GAAKA,EAAO,aAAY,IAC/aA,EAAO,QAAQ,CACb,UAAW,OACX,aAAc,GACd,iBAAkBA,EAAO,OAAO,QAAU8H,EAAO,gBAAkB,OAAS9H,EAAO,qBAAoB,EAAK,KAAK,KAAK,WAAW8H,EAAO,cAAe,EAAE,CAAC,EAClK,CAAO,EAECR,EAAK,iBAAmBtH,EAAO,aAAY,IAC7CsX,EAAsB,GAClBxP,EAAO,aACTR,EAAK,iBAAmBtH,EAAO,aAAY,EAAK,GAAKA,EAAO,aAAY,EAAKsH,EAAK,eAAiBwN,IAASyC,KAI9GD,IACF7G,EAAE,wBAA0B,IAI1B,CAACzQ,EAAO,gBAAkBA,EAAO,iBAAmB,QAAUsH,EAAK,iBAAmBA,EAAK,iBAC7FA,EAAK,iBAAmBA,EAAK,gBAE3B,CAACtH,EAAO,gBAAkBA,EAAO,iBAAmB,QAAUsH,EAAK,iBAAmBA,EAAK,iBAC7FA,EAAK,iBAAmBA,EAAK,gBAE3B,CAACtH,EAAO,gBAAkB,CAACA,EAAO,iBACpCsH,EAAK,iBAAmBA,EAAK,gBAI3BQ,EAAO,UAAY,EACrB,GAAI,KAAK,IAAIgN,CAAI,EAAIhN,EAAO,WAAaR,EAAK,oBAC5C,GAAI,CAACA,EAAK,mBAAoB,CAC5BA,EAAK,mBAAqB,GAC1B2O,EAAQ,OAASA,EAAQ,SACzBA,EAAQ,OAASA,EAAQ,SACzB3O,EAAK,iBAAmBA,EAAK,eAC7B2O,EAAQ,KAAOjW,EAAO,aAAY,EAAKiW,EAAQ,SAAWA,EAAQ,OAASA,EAAQ,SAAWA,EAAQ,OACtG,MACF,MACK,CACL3O,EAAK,iBAAmBA,EAAK,eAC7B,MACF,CAEE,CAACQ,EAAO,cAAgBA,EAAO,WAG/BA,EAAO,UAAYA,EAAO,SAAS,SAAW9H,EAAO,UAAY8H,EAAO,uBAC1E9H,EAAO,kBAAiB,EACxBA,EAAO,oBAAmB,GAExB8H,EAAO,UAAYA,EAAO,SAAS,SAAW9H,EAAO,UACvDA,EAAO,SAAS,YAAW,EAG7BA,EAAO,eAAesH,EAAK,gBAAgB,EAE3CtH,EAAO,aAAasH,EAAK,gBAAgB,EAC3C,CAEA,SAASkQ,GAAWzQ,EAAO,CACzB,MAAM/G,EAAS,KACTsH,EAAOtH,EAAO,gBACpB,IAAIyQ,EAAI1J,EACJ0J,EAAE,gBAAeA,EAAIA,EAAE,eAC3B,IAAIkG,EAEJ,GADqBlG,EAAE,OAAS,YAAcA,EAAE,OAAS,eAOvD,GADAkG,EAAc,CAAC,GAAGlG,EAAE,cAAc,EAAE,KAAKc,GAAKA,EAAE,aAAejK,EAAK,OAAO,EACvE,CAACqP,GAAeA,EAAY,aAAerP,EAAK,QAAS,WAN5C,CAEjB,GADIA,EAAK,UAAY,MACjBmJ,EAAE,YAAcnJ,EAAK,UAAW,OACpCqP,EAAclG,CAChB,CAIA,GAAI,CAAC,gBAAiB,aAAc,eAAgB,aAAa,EAAE,SAASA,EAAE,IAAI,GAE5E,EADY,CAAC,gBAAiB,aAAa,EAAE,SAASA,EAAE,IAAI,IAAMzQ,EAAO,QAAQ,UAAYA,EAAO,QAAQ,YAE9G,OAGJsH,EAAK,UAAY,KACjBA,EAAK,QAAU,KACf,KAAM,CACJ,OAAAQ,EACA,QAAAmO,EACA,aAAc/N,EACd,WAAAO,EACA,QAAA0I,CACJ,EAAMnR,EAEJ,GADI,CAACmR,GACD,CAACrJ,EAAO,eAAiB2I,EAAE,cAAgB,QAAS,OAKxD,GAJInJ,EAAK,qBACPtH,EAAO,KAAK,WAAYyQ,CAAC,EAE3BnJ,EAAK,oBAAsB,GACvB,CAACA,EAAK,UAAW,CACfA,EAAK,SAAWQ,EAAO,YACzB9H,EAAO,cAAc,EAAK,EAE5BsH,EAAK,QAAU,GACfA,EAAK,YAAc,GACnB,MACF,CAGIQ,EAAO,YAAcR,EAAK,SAAWA,EAAK,YAActH,EAAO,iBAAmB,IAAQA,EAAO,iBAAmB,KACtHA,EAAO,cAAc,EAAK,EAI5B,MAAMyX,EAAepZ,EAAG,EAClBqZ,EAAWD,EAAenQ,EAAK,eAGrC,GAAItH,EAAO,WAAY,CACrB,MAAM2X,EAAWlH,EAAE,MAAQA,EAAE,cAAgBA,EAAE,aAAY,EAC3DzQ,EAAO,mBAAmB2X,GAAYA,EAAS,CAAC,GAAKlH,EAAE,OAAQkH,CAAQ,EACvE3X,EAAO,KAAK,YAAayQ,CAAC,EACtBiH,EAAW,KAAOD,EAAenQ,EAAK,cAAgB,KACxDtH,EAAO,KAAK,wBAAyByQ,CAAC,CAE1C,CAKA,GAJAnJ,EAAK,cAAgBjJ,EAAG,EACxBF,GAAS,IAAM,CACR6B,EAAO,YAAWA,EAAO,WAAa,GAC7C,CAAC,EACG,CAACsH,EAAK,WAAa,CAACA,EAAK,SAAW,CAACtH,EAAO,gBAAkBiW,EAAQ,OAAS,GAAK,CAAC3O,EAAK,eAAiBA,EAAK,mBAAqBA,EAAK,gBAAkB,CAACA,EAAK,cAAe,CACnLA,EAAK,UAAY,GACjBA,EAAK,QAAU,GACfA,EAAK,YAAc,GACnB,MACF,CACAA,EAAK,UAAY,GACjBA,EAAK,QAAU,GACfA,EAAK,YAAc,GACnB,IAAIsQ,EAMJ,GALI9P,EAAO,aACT8P,EAAa1P,EAAMlI,EAAO,UAAY,CAACA,EAAO,UAE9C4X,EAAa,CAACtQ,EAAK,iBAEjBQ,EAAO,QACT,OAEF,GAAIA,EAAO,UAAYA,EAAO,SAAS,QAAS,CAC9C9H,EAAO,SAAS,WAAW,CACzB,WAAA4X,CACN,CAAK,EACD,MACF,CAGA,MAAMC,EAAcD,GAAc,CAAC5X,EAAO,aAAY,GAAM,CAACA,EAAO,OAAO,KAC3E,IAAI8X,EAAY,EACZ1N,EAAYpK,EAAO,gBAAgB,CAAC,EACxC,QAASZ,EAAI,EAAGA,EAAIqJ,EAAW,OAAQrJ,GAAKA,EAAI0I,EAAO,mBAAqB,EAAIA,EAAO,eAAgB,CACrG,MAAMmK,EAAY7S,EAAI0I,EAAO,mBAAqB,EAAI,EAAIA,EAAO,eAC7D,OAAOW,EAAWrJ,EAAI6S,CAAS,EAAM,KACnC4F,GAAeD,GAAcnP,EAAWrJ,CAAC,GAAKwY,EAAanP,EAAWrJ,EAAI6S,CAAS,KACrF6F,EAAY1Y,EACZgL,EAAY3B,EAAWrJ,EAAI6S,CAAS,EAAIxJ,EAAWrJ,CAAC,IAE7CyY,GAAeD,GAAcnP,EAAWrJ,CAAC,KAClD0Y,EAAY1Y,EACZgL,EAAY3B,EAAWA,EAAW,OAAS,CAAC,EAAIA,EAAWA,EAAW,OAAS,CAAC,EAEpF,CACA,IAAIsP,EAAmB,KACnBC,EAAkB,KAClBlQ,EAAO,SACL9H,EAAO,YACTgY,EAAkBlQ,EAAO,SAAWA,EAAO,QAAQ,SAAW9H,EAAO,QAAUA,EAAO,QAAQ,OAAO,OAAS,EAAIA,EAAO,OAAO,OAAS,EAChIA,EAAO,QAChB+X,EAAmB,IAIvB,MAAME,GAASL,EAAanP,EAAWqP,CAAS,GAAK1N,EAC/C6H,EAAY6F,EAAYhQ,EAAO,mBAAqB,EAAI,EAAIA,EAAO,eACzE,GAAI4P,EAAW5P,EAAO,aAAc,CAElC,GAAI,CAACA,EAAO,WAAY,CACtB9H,EAAO,QAAQA,EAAO,WAAW,EACjC,MACF,CACIA,EAAO,iBAAmB,SACxBiY,GAASnQ,EAAO,gBAAiB9H,EAAO,QAAQ8H,EAAO,QAAU9H,EAAO,MAAQ+X,EAAmBD,EAAY7F,CAAS,EAAOjS,EAAO,QAAQ8X,CAAS,GAEzJ9X,EAAO,iBAAmB,SACxBiY,EAAQ,EAAInQ,EAAO,gBACrB9H,EAAO,QAAQ8X,EAAY7F,CAAS,EAC3B+F,IAAoB,MAAQC,EAAQ,GAAK,KAAK,IAAIA,CAAK,EAAInQ,EAAO,gBAC3E9H,EAAO,QAAQgY,CAAe,EAE9BhY,EAAO,QAAQ8X,CAAS,EAG9B,KAAO,CAEL,GAAI,CAAChQ,EAAO,YAAa,CACvB9H,EAAO,QAAQA,EAAO,WAAW,EACjC,MACF,CAC0BA,EAAO,aAAeyQ,EAAE,SAAWzQ,EAAO,WAAW,QAAUyQ,EAAE,SAAWzQ,EAAO,WAAW,QAQ7GyQ,EAAE,SAAWzQ,EAAO,WAAW,OACxCA,EAAO,QAAQ8X,EAAY7F,CAAS,EAEpCjS,EAAO,QAAQ8X,CAAS,GATpB9X,EAAO,iBAAmB,QAC5BA,EAAO,QAAQ+X,IAAqB,KAAOA,EAAmBD,EAAY7F,CAAS,EAEjFjS,EAAO,iBAAmB,QAC5BA,EAAO,QAAQgY,IAAoB,KAAOA,EAAkBF,CAAS,EAO3E,CACF,CAEA,SAASI,IAAW,CAClB,MAAMlY,EAAS,KACT,CACJ,OAAA8H,EACA,GAAAvJ,CACJ,EAAMyB,EACJ,GAAIzB,GAAMA,EAAG,cAAgB,EAAG,OAG5BuJ,EAAO,aACT9H,EAAO,cAAa,EAItB,KAAM,CACJ,eAAAiU,EACA,eAAAD,EACA,SAAAxL,CACJ,EAAMxI,EACEoI,EAAYpI,EAAO,SAAWA,EAAO,OAAO,QAAQ,QAG1DA,EAAO,eAAiB,GACxBA,EAAO,eAAiB,GACxBA,EAAO,WAAU,EACjBA,EAAO,aAAY,EACnBA,EAAO,oBAAmB,EAC1B,MAAMmY,EAAgB/P,GAAaN,EAAO,MACrCA,EAAO,gBAAkB,QAAUA,EAAO,cAAgB,IAAM9H,EAAO,OAAS,CAACA,EAAO,aAAe,CAACA,EAAO,OAAO,gBAAkB,CAACmY,EAC5InY,EAAO,QAAQA,EAAO,OAAO,OAAS,EAAG,EAAG,GAAO,EAAI,EAEnDA,EAAO,OAAO,MAAQ,CAACoI,EACzBpI,EAAO,YAAYA,EAAO,UAAW,EAAG,GAAO,EAAI,EAEnDA,EAAO,QAAQA,EAAO,YAAa,EAAG,GAAO,EAAI,EAGjDA,EAAO,UAAYA,EAAO,SAAS,SAAWA,EAAO,SAAS,SAChE,aAAaA,EAAO,SAAS,aAAa,EAC1CA,EAAO,SAAS,cAAgB,WAAW,IAAM,CAC3CA,EAAO,UAAYA,EAAO,SAAS,SAAWA,EAAO,SAAS,QAChEA,EAAO,SAAS,OAAM,CAE1B,EAAG,GAAG,GAGRA,EAAO,eAAiBgU,EACxBhU,EAAO,eAAiBiU,EACpBjU,EAAO,OAAO,eAAiBwI,IAAaxI,EAAO,UACrDA,EAAO,cAAa,CAExB,CAEA,SAASoY,GAAQ3H,EAAG,CAClB,MAAMzQ,EAAS,KACVA,EAAO,UACPA,EAAO,aACNA,EAAO,OAAO,eAAeyQ,EAAE,eAAc,EAC7CzQ,EAAO,OAAO,0BAA4BA,EAAO,YACnDyQ,EAAE,gBAAe,EACjBA,EAAE,yBAAwB,IAGhC,CAEA,SAAS4H,IAAW,CAClB,MAAMrY,EAAS,KACT,CACJ,UAAA+H,EACA,aAAAoK,EACA,QAAAhB,CACJ,EAAMnR,EACJ,GAAI,CAACmR,EAAS,OACdnR,EAAO,kBAAoBA,EAAO,UAC9BA,EAAO,eACTA,EAAO,UAAY,CAAC+H,EAAU,WAE9B/H,EAAO,UAAY,CAAC+H,EAAU,UAG5B/H,EAAO,YAAc,IAAGA,EAAO,UAAY,GAC/CA,EAAO,kBAAiB,EACxBA,EAAO,oBAAmB,EAC1B,IAAIgQ,EACJ,MAAMxD,EAAiBxM,EAAO,aAAY,EAAKA,EAAO,aAAY,EAC9DwM,IAAmB,EACrBwD,EAAc,EAEdA,GAAehQ,EAAO,UAAYA,EAAO,aAAY,GAAMwM,EAEzDwD,IAAgBhQ,EAAO,UACzBA,EAAO,eAAemS,EAAe,CAACnS,EAAO,UAAYA,EAAO,SAAS,EAE3EA,EAAO,KAAK,eAAgBA,EAAO,UAAW,EAAK,CACrD,CAEA,SAASsY,GAAO7H,EAAG,CACjB,MAAMzQ,EAAS,KACf6N,GAAqB7N,EAAQyQ,EAAE,MAAM,EACjC,EAAAzQ,EAAO,OAAO,SAAWA,EAAO,OAAO,gBAAkB,QAAU,CAACA,EAAO,OAAO,aAGtFA,EAAO,OAAM,CACf,CAEA,SAASuY,IAAuB,CAC9B,MAAMvY,EAAS,KACXA,EAAO,gCACXA,EAAO,8BAAgC,GACnCA,EAAO,OAAO,sBAChBA,EAAO,GAAG,MAAM,YAAc,QAElC,CAEA,MAAM0G,GAAS,CAAC1G,EAAQ8G,IAAW,CACjC,MAAMhE,EAAWvF,EAAW,EACtB,CACJ,OAAAuK,EACA,GAAAvJ,EACA,UAAAwJ,EACA,OAAAzE,CACJ,EAAMtD,EACEwY,EAAU,CAAC,CAAC1Q,EAAO,OACnB2Q,EAAY3R,IAAW,KAAO,mBAAqB,sBACnD4R,EAAe5R,EACjB,CAACvI,GAAM,OAAOA,GAAO,WAGzBuE,EAAS2V,CAAS,EAAE,aAAczY,EAAO,qBAAsB,CAC7D,QAAS,GACT,QAAAwY,CACJ,CAAG,EACDja,EAAGka,CAAS,EAAE,aAAczY,EAAO,aAAc,CAC/C,QAAS,EACb,CAAG,EACDzB,EAAGka,CAAS,EAAE,cAAezY,EAAO,aAAc,CAChD,QAAS,EACb,CAAG,EACD8C,EAAS2V,CAAS,EAAE,YAAazY,EAAO,YAAa,CACnD,QAAS,GACT,QAAAwY,CACJ,CAAG,EACD1V,EAAS2V,CAAS,EAAE,cAAezY,EAAO,YAAa,CACrD,QAAS,GACT,QAAAwY,CACJ,CAAG,EACD1V,EAAS2V,CAAS,EAAE,WAAYzY,EAAO,WAAY,CACjD,QAAS,EACb,CAAG,EACD8C,EAAS2V,CAAS,EAAE,YAAazY,EAAO,WAAY,CAClD,QAAS,EACb,CAAG,EACD8C,EAAS2V,CAAS,EAAE,gBAAiBzY,EAAO,WAAY,CACtD,QAAS,EACb,CAAG,EACD8C,EAAS2V,CAAS,EAAE,cAAezY,EAAO,WAAY,CACpD,QAAS,EACb,CAAG,EACD8C,EAAS2V,CAAS,EAAE,aAAczY,EAAO,WAAY,CACnD,QAAS,EACb,CAAG,EACD8C,EAAS2V,CAAS,EAAE,eAAgBzY,EAAO,WAAY,CACrD,QAAS,EACb,CAAG,EACD8C,EAAS2V,CAAS,EAAE,cAAezY,EAAO,WAAY,CACpD,QAAS,EACb,CAAG,GAGG8H,EAAO,eAAiBA,EAAO,2BACjCvJ,EAAGka,CAAS,EAAE,QAASzY,EAAO,QAAS,EAAI,EAEzC8H,EAAO,SACTC,EAAU0Q,CAAS,EAAE,SAAUzY,EAAO,QAAQ,EAI5C8H,EAAO,qBACT9H,EAAO0Y,CAAY,EAAEpV,EAAO,KAAOA,EAAO,QAAU,0CAA4C,wBAAyB4U,GAAU,EAAI,EAEvIlY,EAAO0Y,CAAY,EAAE,iBAAkBR,GAAU,EAAI,EAIvD3Z,EAAGka,CAAS,EAAE,OAAQzY,EAAO,OAAQ,CACnC,QAAS,EACb,CAAG,EACH,EACA,SAAS2Y,IAAe,CACtB,MAAM3Y,EAAS,KACT,CACJ,OAAA8H,CACJ,EAAM9H,EACJA,EAAO,aAAegW,GAAa,KAAKhW,CAAM,EAC9CA,EAAO,YAAc0W,GAAY,KAAK1W,CAAM,EAC5CA,EAAO,WAAawX,GAAW,KAAKxX,CAAM,EAC1CA,EAAO,qBAAuBuY,GAAqB,KAAKvY,CAAM,EAC1D8H,EAAO,UACT9H,EAAO,SAAWqY,GAAS,KAAKrY,CAAM,GAExCA,EAAO,QAAUoY,GAAQ,KAAKpY,CAAM,EACpCA,EAAO,OAASsY,GAAO,KAAKtY,CAAM,EAClC0G,GAAO1G,EAAQ,IAAI,CACrB,CACA,SAAS4Y,IAAe,CAEtBlS,GADe,KACA,KAAK,CACtB,CACA,IAAImS,GAAW,CACb,aAAAF,GACA,aAAAC,EACF,EAEA,MAAME,GAAgB,CAAC9Y,EAAQ8H,IACtB9H,EAAO,MAAQ8H,EAAO,MAAQA,EAAO,KAAK,KAAO,EAE1D,SAASiR,IAAgB,CACvB,MAAM/Y,EAAS,KACT,CACJ,UAAAwO,EACA,YAAAwK,EACA,OAAAlR,EACA,GAAAvJ,CACJ,EAAMyB,EACEiZ,EAAcnR,EAAO,YAC3B,GAAI,CAACmR,GAAeA,GAAe,OAAO,KAAKA,CAAW,EAAE,SAAW,EAAG,OAC1E,MAAMnW,EAAWvF,EAAW,EAGtB2b,EAAkBpR,EAAO,kBAAoB,UAAY,CAACA,EAAO,gBAAkBA,EAAO,gBAAkB,YAC5GqR,EAAsB,CAAC,SAAU,WAAW,EAAE,SAASrR,EAAO,eAAe,GAAK,CAACA,EAAO,gBAAkB9H,EAAO,GAAK8C,EAAS,cAAcgF,EAAO,eAAe,EACrKsR,EAAapZ,EAAO,cAAciZ,EAAaC,EAAiBC,CAAmB,EACzF,GAAI,CAACC,GAAcpZ,EAAO,oBAAsBoZ,EAAY,OAE5D,MAAMC,GADuBD,KAAcH,EAAcA,EAAYG,CAAU,EAAI,SAClCpZ,EAAO,eAClDsZ,EAAcR,GAAc9Y,EAAQ8H,CAAM,EAC1CyR,EAAaT,GAAc9Y,EAAQqZ,CAAgB,EACnDG,EAAgBxZ,EAAO,OAAO,WAC9ByZ,EAAeJ,EAAiB,WAChCK,EAAa5R,EAAO,QACtBwR,GAAe,CAACC,GAClBhb,EAAG,UAAU,OAAO,GAAGuJ,EAAO,sBAAsB,OAAQ,GAAGA,EAAO,sBAAsB,aAAa,EACzG9H,EAAO,qBAAoB,GAClB,CAACsZ,GAAeC,IACzBhb,EAAG,UAAU,IAAI,GAAGuJ,EAAO,sBAAsB,MAAM,GACnDuR,EAAiB,KAAK,MAAQA,EAAiB,KAAK,OAAS,UAAY,CAACA,EAAiB,KAAK,MAAQvR,EAAO,KAAK,OAAS,WAC/HvJ,EAAG,UAAU,IAAI,GAAGuJ,EAAO,sBAAsB,aAAa,EAEhE9H,EAAO,qBAAoB,GAEzBwZ,GAAiB,CAACC,EACpBzZ,EAAO,gBAAe,EACb,CAACwZ,GAAiBC,GAC3BzZ,EAAO,cAAa,EAItB,CAAC,aAAc,aAAc,WAAW,EAAE,QAAQoC,GAAQ,CACxD,GAAI,OAAOiX,EAAiBjX,CAAI,EAAM,IAAa,OACnD,MAAMuX,EAAmB7R,EAAO1F,CAAI,GAAK0F,EAAO1F,CAAI,EAAE,QAChDwX,EAAkBP,EAAiBjX,CAAI,GAAKiX,EAAiBjX,CAAI,EAAE,QACrEuX,GAAoB,CAACC,GACvB5Z,EAAOoC,CAAI,EAAE,QAAO,EAElB,CAACuX,GAAoBC,GACvB5Z,EAAOoC,CAAI,EAAE,OAAM,CAEvB,CAAC,EACD,MAAMyX,EAAmBR,EAAiB,WAAaA,EAAiB,YAAcvR,EAAO,UACvFgS,EAAchS,EAAO,OAASuR,EAAiB,gBAAkBvR,EAAO,eAAiB+R,GACzFE,EAAUjS,EAAO,KACnB+R,GAAoBb,GACtBhZ,EAAO,gBAAe,EAExB/C,EAAO+C,EAAO,OAAQqZ,CAAgB,EACtC,MAAMW,EAAYha,EAAO,OAAO,QAC1Bia,EAAUja,EAAO,OAAO,KAC9B,OAAO,OAAOA,EAAQ,CACpB,eAAgBA,EAAO,OAAO,eAC9B,eAAgBA,EAAO,OAAO,eAC9B,eAAgBA,EAAO,OAAO,cAClC,CAAG,EACG0Z,GAAc,CAACM,EACjBha,EAAO,QAAO,EACL,CAAC0Z,GAAcM,GACxBha,EAAO,OAAM,EAEfA,EAAO,kBAAoBoZ,EAC3BpZ,EAAO,KAAK,oBAAqBqZ,CAAgB,EAC7CL,IACEc,GACF9Z,EAAO,YAAW,EAClBA,EAAO,WAAWwO,CAAS,EAC3BxO,EAAO,aAAY,GACV,CAAC+Z,GAAWE,GACrBja,EAAO,WAAWwO,CAAS,EAC3BxO,EAAO,aAAY,GACV+Z,GAAW,CAACE,GACrBja,EAAO,YAAW,GAGtBA,EAAO,KAAK,aAAcqZ,CAAgB,CAC5C,CAEA,SAASa,GAAcjB,EAAaxD,EAAM0E,EAAa,CAIrD,GAHI1E,IAAS,SACXA,EAAO,UAEL,CAACwD,GAAexD,IAAS,aAAe,CAAC0E,EAAa,OAC1D,IAAIf,EAAa,GACjB,MAAM5a,EAASZ,EAAS,EAClBwc,EAAgB3E,IAAS,SAAWjX,EAAO,YAAc2b,EAAY,aACrEE,EAAS,OAAO,KAAKpB,CAAW,EAAE,IAAIqB,GAAS,CACnD,GAAI,OAAOA,GAAU,UAAYA,EAAM,QAAQ,GAAG,IAAM,EAAG,CACzD,MAAMC,EAAW,WAAWD,EAAM,OAAO,CAAC,CAAC,EAE3C,MAAO,CACL,MAFYF,EAAgBG,EAG5B,MAAAD,CACR,CACI,CACA,MAAO,CACL,MAAOA,EACP,MAAAA,CACN,CACE,CAAC,EACDD,EAAO,KAAK,CAAC,EAAGG,IAAM,SAAS,EAAE,MAAO,EAAE,EAAI,SAASA,EAAE,MAAO,EAAE,CAAC,EACnE,QAASpb,EAAI,EAAGA,EAAIib,EAAO,OAAQjb,GAAK,EAAG,CACzC,KAAM,CACJ,MAAAkb,EACA,MAAAG,CACN,EAAQJ,EAAOjb,CAAC,EACRqW,IAAS,SACPjX,EAAO,WAAW,eAAeic,CAAK,KAAK,EAAE,UAC/CrB,EAAakB,GAENG,GAASN,EAAY,cAC9Bf,EAAakB,EAEjB,CACA,OAAOlB,GAAc,KACvB,CAEA,IAAIH,GAAc,CAChB,cAAAF,GACA,cAAAmB,EACF,EAEA,SAASQ,GAAetV,EAASuV,EAAQ,CACvC,MAAMC,EAAgB,CAAA,EACtB,OAAAxV,EAAQ,QAAQyV,GAAQ,CAClB,OAAOA,GAAS,SAClB,OAAO,KAAKA,CAAI,EAAE,QAAQC,GAAc,CAClCD,EAAKC,CAAU,GACjBF,EAAc,KAAKD,EAASG,CAAU,CAE1C,CAAC,EACQ,OAAOD,GAAS,UACzBD,EAAc,KAAKD,EAASE,CAAI,CAEpC,CAAC,EACMD,CACT,CACA,SAASG,IAAa,CACpB,MAAM/a,EAAS,KACT,CACJ,WAAA8a,EACA,OAAAhT,EACA,IAAAI,EACA,GAAA3J,EACA,OAAA+E,CACJ,EAAMtD,EAEEgb,EAAWN,GAAe,CAAC,cAAe5S,EAAO,UAAW,CAChE,YAAa9H,EAAO,OAAO,UAAY8H,EAAO,SAAS,OAC3D,EAAK,CACD,WAAcA,EAAO,UACzB,EAAK,CACD,IAAOI,CACX,EAAK,CACD,KAAQJ,EAAO,MAAQA,EAAO,KAAK,KAAO,CAC9C,EAAK,CACD,cAAeA,EAAO,MAAQA,EAAO,KAAK,KAAO,GAAKA,EAAO,KAAK,OAAS,QAC/E,EAAK,CACD,QAAWxE,EAAO,OACtB,EAAK,CACD,IAAOA,EAAO,GAClB,EAAK,CACD,WAAYwE,EAAO,OACvB,EAAK,CACD,SAAYA,EAAO,SAAWA,EAAO,cACzC,EAAK,CACD,iBAAkBA,EAAO,mBAC7B,CAAG,EAAGA,EAAO,sBAAsB,EACjCgT,EAAW,KAAK,GAAGE,CAAQ,EAC3Bzc,EAAG,UAAU,IAAI,GAAGuc,CAAU,EAC9B9a,EAAO,qBAAoB,CAC7B,CAEA,SAASib,IAAgB,CACvB,MAAMjb,EAAS,KACT,CACJ,GAAAzB,EACA,WAAAuc,CACJ,EAAM9a,EACA,CAACzB,GAAM,OAAOA,GAAO,WACzBA,EAAG,UAAU,OAAO,GAAGuc,CAAU,EACjC9a,EAAO,qBAAoB,EAC7B,CAEA,IAAIjC,GAAU,CACZ,WAAAgd,GACA,cAAAE,EACF,EAEA,SAASC,IAAgB,CACvB,MAAMlb,EAAS,KACT,CACJ,SAAUmb,EACV,OAAArT,CACJ,EAAM9H,EACE,CACJ,mBAAAob,CACJ,EAAMtT,EACJ,GAAIsT,EAAoB,CACtB,MAAMnO,EAAiBjN,EAAO,OAAO,OAAS,EACxCqb,EAAqBrb,EAAO,WAAWiN,CAAc,EAAIjN,EAAO,gBAAgBiN,CAAc,EAAImO,EAAqB,EAC7Hpb,EAAO,SAAWA,EAAO,KAAOqb,CAClC,MACErb,EAAO,SAAWA,EAAO,SAAS,SAAW,EAE3C8H,EAAO,iBAAmB,KAC5B9H,EAAO,eAAiB,CAACA,EAAO,UAE9B8H,EAAO,iBAAmB,KAC5B9H,EAAO,eAAiB,CAACA,EAAO,UAE9Bmb,GAAaA,IAAcnb,EAAO,WACpCA,EAAO,MAAQ,IAEbmb,IAAcnb,EAAO,UACvBA,EAAO,KAAKA,EAAO,SAAW,OAAS,QAAQ,CAEnD,CACA,IAAIsb,GAAkB,CACpB,cAAAJ,EACF,EAEIK,GAAW,CACb,KAAM,GACN,UAAW,aACX,eAAgB,GAChB,sBAAuB,mBACvB,kBAAmB,UACnB,aAAc,EACd,MAAO,IACP,QAAS,GACT,qBAAsB,GACtB,eAAgB,GAChB,OAAQ,GACR,eAAgB,GAChB,aAAc,SACd,QAAS,GACT,kBAAmB,wDAEnB,MAAO,KACP,OAAQ,KAER,+BAAgC,GAEhC,UAAW,KACX,IAAK,KAEL,mBAAoB,GACpB,mBAAoB,GAEpB,WAAY,GAEZ,eAAgB,GAEhB,iBAAkB,GAElB,OAAQ,QAIR,YAAa,OACb,gBAAiB,SAEjB,aAAc,EACd,cAAe,EACf,eAAgB,EAChB,mBAAoB,EACpB,mBAAoB,GACpB,eAAgB,GAChB,qBAAsB,GACtB,mBAAoB,EAEpB,kBAAmB,EAEnB,oBAAqB,GACrB,yBAA0B,GAE1B,cAAe,GAEf,aAAc,GAEd,WAAY,EACZ,WAAY,GACZ,cAAe,GACf,YAAa,GACb,WAAY,GACZ,gBAAiB,GACjB,aAAc,IACd,aAAc,GACd,eAAgB,GAChB,UAAW,EACX,yBAA0B,GAC1B,yBAA0B,GAC1B,8BAA+B,GAC/B,oBAAqB,GAErB,kBAAmB,GAEnB,WAAY,GACZ,gBAAiB,IAEjB,oBAAqB,GAErB,WAAY,GAEZ,cAAe,GACf,yBAA0B,GAC1B,oBAAqB,GAErB,KAAM,GACN,mBAAoB,GACpB,qBAAsB,EACtB,oBAAqB,GAErB,OAAQ,GAER,eAAgB,GAChB,eAAgB,GAChB,aAAc,KAEd,UAAW,GACX,eAAgB,oBAChB,kBAAmB,KAEnB,iBAAkB,GAClB,wBAAyB,GAEzB,uBAAwB,UAExB,WAAY,eACZ,gBAAiB,qBACjB,iBAAkB,sBAClB,kBAAmB,uBACnB,uBAAwB,6BACxB,eAAgB,oBAChB,eAAgB,oBAChB,aAAc,iBACd,mBAAoB,wBACpB,oBAAqB,EAErB,mBAAoB,GAEpB,aAAc,EAChB,EAEA,SAASC,GAAmB1T,EAAQ2T,EAAkB,CACpD,OAAO,SAAsBze,EAAK,CAC5BA,IAAQ,SACVA,EAAM,CAAA,GAER,MAAM0e,EAAkB,OAAO,KAAK1e,CAAG,EAAE,CAAC,EACpC2e,EAAe3e,EAAI0e,CAAe,EACxC,GAAI,OAAOC,GAAiB,UAAYA,IAAiB,KAAM,CAC7D1e,EAAOwe,EAAkBze,CAAG,EAC5B,MACF,CAYA,GAXI8K,EAAO4T,CAAe,IAAM,KAC9B5T,EAAO4T,CAAe,EAAI,CACxB,QAAS,EACjB,GAEQA,IAAoB,cAAgB5T,EAAO4T,CAAe,GAAK5T,EAAO4T,CAAe,EAAE,SAAW,CAAC5T,EAAO4T,CAAe,EAAE,QAAU,CAAC5T,EAAO4T,CAAe,EAAE,SAChK5T,EAAO4T,CAAe,EAAE,KAAO,IAE7B,CAAC,aAAc,WAAW,EAAE,QAAQA,CAAe,GAAK,GAAK5T,EAAO4T,CAAe,GAAK5T,EAAO4T,CAAe,EAAE,SAAW,CAAC5T,EAAO4T,CAAe,EAAE,KACtJ5T,EAAO4T,CAAe,EAAE,KAAO,IAE7B,EAAEA,KAAmB5T,GAAU,YAAa6T,GAAe,CAC7D1e,EAAOwe,EAAkBze,CAAG,EAC5B,MACF,CACI,OAAO8K,EAAO4T,CAAe,GAAM,UAAY,EAAE,YAAa5T,EAAO4T,CAAe,KACtF5T,EAAO4T,CAAe,EAAE,QAAU,IAE/B5T,EAAO4T,CAAe,IAAG5T,EAAO4T,CAAe,EAAI,CACtD,QAAS,EACf,GACIze,EAAOwe,EAAkBze,CAAG,CAC9B,CACF,CAGA,MAAM4e,GAAa,CACjB,cAAAnV,GACA,OAAA+I,GACA,UAAA3D,GACA,WAAAmF,GACA,MAAA1H,GACA,KAAA6L,GACA,WAAAI,GACA,OAAQsD,GACR,YAAAI,GACA,cAAeqC,GACf,QAAAvd,EACF,EACM8d,GAAmB,CAAA,EACzB,MAAMC,CAAO,CACX,aAAc,CACZ,IAAIvd,EACAuJ,EACJ,QAASb,EAAO,UAAU,OAAQC,EAAO,IAAI,MAAMD,CAAI,EAAGE,EAAO,EAAGA,EAAOF,EAAME,IAC/ED,EAAKC,CAAI,EAAI,UAAUA,CAAI,EAEzBD,EAAK,SAAW,GAAKA,EAAK,CAAC,EAAE,aAAe,OAAO,UAAU,SAAS,KAAKA,EAAK,CAAC,CAAC,EAAE,MAAM,EAAG,EAAE,IAAM,SACvGY,EAASZ,EAAK,CAAC,EAEf,CAAC3I,EAAIuJ,CAAM,EAAIZ,EAEZY,IAAQA,EAAS,CAAA,GACtBA,EAAS7K,EAAO,CAAA,EAAI6K,CAAM,EACtBvJ,GAAM,CAACuJ,EAAO,KAAIA,EAAO,GAAKvJ,GAClC,MAAMuE,EAAWvF,EAAW,EAC5B,GAAIuK,EAAO,IAAM,OAAOA,EAAO,IAAO,UAAYhF,EAAS,iBAAiBgF,EAAO,EAAE,EAAE,OAAS,EAAG,CACjG,MAAMiU,EAAU,CAAA,EAChB,OAAAjZ,EAAS,iBAAiBgF,EAAO,EAAE,EAAE,QAAQqS,GAAe,CAC1D,MAAM6B,EAAY/e,EAAO,CAAA,EAAI6K,EAAQ,CACnC,GAAIqS,CACd,CAAS,EACD4B,EAAQ,KAAK,IAAID,EAAOE,CAAS,CAAC,CACpC,CAAC,EAEMD,CACT,CAGA,MAAM/b,EAAS,KACfA,EAAO,WAAa,GACpBA,EAAO,QAAU+C,GAAU,EAC3B/C,EAAO,OAASgE,GAAU,CACxB,UAAW8D,EAAO,SACxB,CAAK,EACD9H,EAAO,QAAU4E,GAAU,EAC3B5E,EAAO,gBAAkB,CAAA,EACzBA,EAAO,mBAAqB,CAAA,EAC5BA,EAAO,QAAU,CAAC,GAAGA,EAAO,WAAW,EACnC8H,EAAO,SAAW,MAAM,QAAQA,EAAO,OAAO,GAChD9H,EAAO,QAAQ,KAAK,GAAG8H,EAAO,OAAO,EAEvC,MAAM2T,EAAmB,CAAA,EACzBzb,EAAO,QAAQ,QAAQic,GAAO,CAC5BA,EAAI,CACF,OAAAnU,EACA,OAAA9H,EACA,aAAcwb,GAAmB1T,EAAQ2T,CAAgB,EACzD,GAAIzb,EAAO,GAAG,KAAKA,CAAM,EACzB,KAAMA,EAAO,KAAK,KAAKA,CAAM,EAC7B,IAAKA,EAAO,IAAI,KAAKA,CAAM,EAC3B,KAAMA,EAAO,KAAK,KAAKA,CAAM,CACrC,CAAO,CACH,CAAC,EAGD,MAAMkc,EAAejf,EAAO,GAAIse,GAAUE,CAAgB,EAG1D,OAAAzb,EAAO,OAAS/C,EAAO,CAAA,EAAIif,EAAcL,GAAkB/T,CAAM,EACjE9H,EAAO,eAAiB/C,EAAO,CAAA,EAAI+C,EAAO,MAAM,EAChDA,EAAO,aAAe/C,EAAO,CAAA,EAAI6K,CAAM,EAGnC9H,EAAO,QAAUA,EAAO,OAAO,IACjC,OAAO,KAAKA,EAAO,OAAO,EAAE,EAAE,QAAQmc,GAAa,CACjDnc,EAAO,GAAGmc,EAAWnc,EAAO,OAAO,GAAGmc,CAAS,CAAC,CAClD,CAAC,EAECnc,EAAO,QAAUA,EAAO,OAAO,OACjCA,EAAO,MAAMA,EAAO,OAAO,KAAK,EAIlC,OAAO,OAAOA,EAAQ,CACpB,QAASA,EAAO,OAAO,QACvB,GAAAzB,EAEA,WAAY,CAAA,EAEZ,OAAQ,CAAA,EACR,WAAY,CAAA,EACZ,SAAU,CAAA,EACV,gBAAiB,CAAA,EAEjB,cAAe,CACb,OAAOyB,EAAO,OAAO,YAAc,YACrC,EACA,YAAa,CACX,OAAOA,EAAO,OAAO,YAAc,UACrC,EAEA,YAAa,EACb,UAAW,EAEX,YAAa,GACb,MAAO,GAEP,UAAW,EACX,kBAAmB,EACnB,SAAU,EACV,SAAU,EACV,UAAW,GACX,uBAAwB,CAGtB,OAAO,KAAK,MAAM,KAAK,UAAY,GAAK,EAAE,EAAI,GAAK,EACrD,EAEA,eAAgBA,EAAO,OAAO,eAC9B,eAAgBA,EAAO,OAAO,eAE9B,gBAAiB,CACf,UAAW,OACX,QAAS,OACT,oBAAqB,OACrB,eAAgB,OAChB,YAAa,OACb,iBAAkB,OAClB,eAAgB,OAChB,mBAAoB,OAEpB,kBAAmBA,EAAO,OAAO,kBAEjC,cAAe,EACf,aAAc,OAEd,WAAY,CAAA,EACZ,oBAAqB,OACrB,YAAa,OACb,UAAW,KACX,QAAS,IACjB,EAEM,WAAY,GAEZ,eAAgBA,EAAO,OAAO,eAC9B,QAAS,CACP,OAAQ,EACR,OAAQ,EACR,SAAU,EACV,SAAU,EACV,KAAM,CACd,EAEM,aAAc,CAAA,EACd,aAAc,CACpB,CAAK,EACDA,EAAO,KAAK,SAAS,EAGjBA,EAAO,OAAO,MAChBA,EAAO,KAAI,EAKNA,CACT,CACA,kBAAkBoc,EAAU,CAC1B,OAAI,KAAK,eACAA,EAGF,CACL,MAAS,SACT,aAAc,cACd,iBAAkB,eAClB,cAAe,aACf,eAAgB,gBAChB,eAAgB,cAChB,gBAAiB,iBACjB,YAAe,cACrB,EAAMA,CAAQ,CACZ,CACA,cAAclT,EAAS,CACrB,KAAM,CACJ,SAAAlB,EACA,OAAAF,CACN,EAAQ,KACEQ,EAASxH,EAAgBkH,EAAU,IAAIF,EAAO,UAAU,gBAAgB,EACxEkF,EAAkB3K,GAAaiG,EAAO,CAAC,CAAC,EAC9C,OAAOjG,GAAa6G,CAAO,EAAI8D,CACjC,CACA,oBAAoB5F,EAAO,CACzB,OAAO,KAAK,cAAc,KAAK,OAAO,KAAK8B,GAAWA,EAAQ,aAAa,yBAAyB,EAAI,IAAM9B,CAAK,CAAC,CACtH,CACA,sBAAsBA,EAAO,CAC3B,OAAI,KAAK,MAAQ,KAAK,OAAO,MAAQ,KAAK,OAAO,KAAK,KAAO,IACvD,KAAK,OAAO,KAAK,OAAS,SAC5BA,EAAQ,KAAK,MAAMA,EAAQ,KAAK,OAAO,KAAK,IAAI,EACvC,KAAK,OAAO,KAAK,OAAS,QACnCA,EAAQA,EAAQ,KAAK,KAAK,KAAK,OAAO,OAAS,KAAK,OAAO,KAAK,IAAI,IAGjEA,CACT,CACA,cAAe,CACb,MAAMpH,EAAS,KACT,CACJ,SAAAgI,EACA,OAAAF,CACN,EAAQ9H,EACJA,EAAO,OAASc,EAAgBkH,EAAU,IAAIF,EAAO,UAAU,gBAAgB,CACjF,CACA,QAAS,CACP,MAAM9H,EAAS,KACXA,EAAO,UACXA,EAAO,QAAU,GACbA,EAAO,OAAO,YAChBA,EAAO,cAAa,EAEtBA,EAAO,KAAK,QAAQ,EACtB,CACA,SAAU,CACR,MAAMA,EAAS,KACVA,EAAO,UACZA,EAAO,QAAU,GACbA,EAAO,OAAO,YAChBA,EAAO,gBAAe,EAExBA,EAAO,KAAK,SAAS,EACvB,CACA,YAAYW,EAAUyK,EAAO,CAC3B,MAAMpL,EAAS,KACfW,EAAW,KAAK,IAAI,KAAK,IAAIA,EAAU,CAAC,EAAG,CAAC,EAC5C,MAAM0b,EAAMrc,EAAO,aAAY,EAEzBS,GADMT,EAAO,aAAY,EACRqc,GAAO1b,EAAW0b,EACzCrc,EAAO,YAAYS,EAAS,OAAO2K,EAAU,IAAc,EAAIA,CAAK,EACpEpL,EAAO,kBAAiB,EACxBA,EAAO,oBAAmB,CAC5B,CACA,sBAAuB,CACrB,MAAMA,EAAS,KACf,GAAI,CAACA,EAAO,OAAO,cAAgB,CAACA,EAAO,GAAI,OAC/C,MAAMsc,EAAMtc,EAAO,GAAG,UAAU,MAAM,GAAG,EAAE,OAAO2L,GACzCA,EAAU,QAAQ,QAAQ,IAAM,GAAKA,EAAU,QAAQ3L,EAAO,OAAO,sBAAsB,IAAM,CACzG,EACDA,EAAO,KAAK,oBAAqBsc,EAAI,KAAK,GAAG,CAAC,CAChD,CACA,gBAAgBpT,EAAS,CACvB,MAAMlJ,EAAS,KACf,OAAIA,EAAO,UAAkB,GACtBkJ,EAAQ,UAAU,MAAM,GAAG,EAAE,OAAOyC,GAClCA,EAAU,QAAQ,cAAc,IAAM,GAAKA,EAAU,QAAQ3L,EAAO,OAAO,UAAU,IAAM,CACnG,EAAE,KAAK,GAAG,CACb,CACA,mBAAoB,CAClB,MAAMA,EAAS,KACf,GAAI,CAACA,EAAO,OAAO,cAAgB,CAACA,EAAO,GAAI,OAC/C,MAAMuc,EAAU,CAAA,EAChBvc,EAAO,OAAO,QAAQkJ,GAAW,CAC/B,MAAM4R,EAAa9a,EAAO,gBAAgBkJ,CAAO,EACjDqT,EAAQ,KAAK,CACX,QAAArT,EACA,WAAA4R,CACR,CAAO,EACD9a,EAAO,KAAK,cAAekJ,EAAS4R,CAAU,CAChD,CAAC,EACD9a,EAAO,KAAK,gBAAiBuc,CAAO,CACtC,CACA,qBAAqBC,EAAMC,EAAO,CAC5BD,IAAS,SACXA,EAAO,WAELC,IAAU,SACZA,EAAQ,IAEV,MAAMzc,EAAS,KACT,CACJ,OAAA8H,EACA,OAAAQ,EACA,WAAAG,EACA,gBAAAC,EACA,KAAMT,EACN,YAAAuF,CACN,EAAQxN,EACJ,IAAI0c,EAAM,EACV,GAAI,OAAO5U,EAAO,eAAkB,SAAU,OAAOA,EAAO,cAC5D,GAAIA,EAAO,eAAgB,CACzB,IAAIsB,EAAYd,EAAOkF,CAAW,EAAI,KAAK,KAAKlF,EAAOkF,CAAW,EAAE,eAAe,EAAI,EACnFmP,EACJ,QAASvd,EAAIoO,EAAc,EAAGpO,EAAIkJ,EAAO,OAAQlJ,GAAK,EAChDkJ,EAAOlJ,CAAC,GAAK,CAACud,IAChBvT,GAAa,KAAK,KAAKd,EAAOlJ,CAAC,EAAE,eAAe,EAChDsd,GAAO,EACHtT,EAAYnB,IAAY0U,EAAY,KAG5C,QAASvd,EAAIoO,EAAc,EAAGpO,GAAK,EAAGA,GAAK,EACrCkJ,EAAOlJ,CAAC,GAAK,CAACud,IAChBvT,GAAad,EAAOlJ,CAAC,EAAE,gBACvBsd,GAAO,EACHtT,EAAYnB,IAAY0U,EAAY,IAG9C,SAEMH,IAAS,UACX,QAASpd,EAAIoO,EAAc,EAAGpO,EAAIkJ,EAAO,OAAQlJ,GAAK,GAChCqd,EAAQhU,EAAWrJ,CAAC,EAAIsJ,EAAgBtJ,CAAC,EAAIqJ,EAAW+E,CAAW,EAAIvF,EAAaQ,EAAWrJ,CAAC,EAAIqJ,EAAW+E,CAAW,EAAIvF,KAEhJyU,GAAO,OAKX,SAAStd,EAAIoO,EAAc,EAAGpO,GAAK,EAAGA,GAAK,EACrBqJ,EAAW+E,CAAW,EAAI/E,EAAWrJ,CAAC,EAAI6I,IAE5DyU,GAAO,GAKf,OAAOA,CACT,CACA,QAAS,CACP,MAAM1c,EAAS,KACf,GAAI,CAACA,GAAUA,EAAO,UAAW,OACjC,KAAM,CACJ,SAAAwI,EACA,OAAAV,CACN,EAAQ9H,EAEA8H,EAAO,aACT9H,EAAO,cAAa,EAEtB,CAAC,GAAGA,EAAO,GAAG,iBAAiB,kBAAkB,CAAC,EAAE,QAAQ8N,GAAW,CACjEA,EAAQ,UACVD,GAAqB7N,EAAQ8N,CAAO,CAExC,CAAC,EACD9N,EAAO,WAAU,EACjBA,EAAO,aAAY,EACnBA,EAAO,eAAc,EACrBA,EAAO,oBAAmB,EAC1B,SAAS2P,GAAe,CACtB,MAAMiN,EAAiB5c,EAAO,aAAeA,EAAO,UAAY,GAAKA,EAAO,UACtEuQ,EAAe,KAAK,IAAI,KAAK,IAAIqM,EAAgB5c,EAAO,aAAY,CAAE,EAAGA,EAAO,aAAY,CAAE,EACpGA,EAAO,aAAauQ,CAAY,EAChCvQ,EAAO,kBAAiB,EACxBA,EAAO,oBAAmB,CAC5B,CACA,IAAI6c,EACJ,GAAI/U,EAAO,UAAYA,EAAO,SAAS,SAAW,CAACA,EAAO,QACxD6H,EAAY,EACR7H,EAAO,YACT9H,EAAO,iBAAgB,MAEpB,CACL,IAAK8H,EAAO,gBAAkB,QAAUA,EAAO,cAAgB,IAAM9H,EAAO,OAAS,CAAC8H,EAAO,eAAgB,CAC3G,MAAMQ,EAAStI,EAAO,SAAW8H,EAAO,QAAQ,QAAU9H,EAAO,QAAQ,OAASA,EAAO,OACzF6c,EAAa7c,EAAO,QAAQsI,EAAO,OAAS,EAAG,EAAG,GAAO,EAAI,CAC/D,MACEuU,EAAa7c,EAAO,QAAQA,EAAO,YAAa,EAAG,GAAO,EAAI,EAE3D6c,GACHlN,EAAY,CAEhB,CACI7H,EAAO,eAAiBU,IAAaxI,EAAO,UAC9CA,EAAO,cAAa,EAEtBA,EAAO,KAAK,QAAQ,CACtB,CACA,gBAAgB8c,EAAcC,EAAY,CACpCA,IAAe,SACjBA,EAAa,IAEf,MAAM/c,EAAS,KACTgd,EAAmBhd,EAAO,OAAO,UAKvC,OAJK8c,IAEHA,EAAeE,IAAqB,aAAe,WAAa,cAE9DF,IAAiBE,GAAoBF,IAAiB,cAAgBA,IAAiB,aAG3F9c,EAAO,GAAG,UAAU,OAAO,GAAGA,EAAO,OAAO,sBAAsB,GAAGgd,CAAgB,EAAE,EACvFhd,EAAO,GAAG,UAAU,IAAI,GAAGA,EAAO,OAAO,sBAAsB,GAAG8c,CAAY,EAAE,EAChF9c,EAAO,qBAAoB,EAC3BA,EAAO,OAAO,UAAY8c,EAC1B9c,EAAO,OAAO,QAAQkJ,GAAW,CAC3B4T,IAAiB,WACnB5T,EAAQ,MAAM,MAAQ,GAEtBA,EAAQ,MAAM,OAAS,EAE3B,CAAC,EACDlJ,EAAO,KAAK,iBAAiB,EACzB+c,GAAY/c,EAAO,OAAM,GACtBA,CACT,CACA,wBAAwB4Q,EAAW,CACjC,MAAM5Q,EAAS,KACXA,EAAO,KAAO4Q,IAAc,OAAS,CAAC5Q,EAAO,KAAO4Q,IAAc,QACtE5Q,EAAO,IAAM4Q,IAAc,MAC3B5Q,EAAO,aAAeA,EAAO,OAAO,YAAc,cAAgBA,EAAO,IACrEA,EAAO,KACTA,EAAO,GAAG,UAAU,IAAI,GAAGA,EAAO,OAAO,sBAAsB,KAAK,EACpEA,EAAO,GAAG,IAAM,QAEhBA,EAAO,GAAG,UAAU,OAAO,GAAGA,EAAO,OAAO,sBAAsB,KAAK,EACvEA,EAAO,GAAG,IAAM,OAElBA,EAAO,OAAM,EACf,CACA,MAAMe,EAAS,CACb,MAAMf,EAAS,KACf,GAAIA,EAAO,QAAS,MAAO,GAG3B,IAAIzB,EAAKwC,GAAWf,EAAO,OAAO,GAIlC,GAHI,OAAOzB,GAAO,WAChBA,EAAK,SAAS,cAAcA,CAAE,GAE5B,CAACA,EACH,MAAO,GAETA,EAAG,OAASyB,EACRzB,EAAG,YAAcA,EAAG,WAAW,MAAQA,EAAG,WAAW,KAAK,WAAayB,EAAO,OAAO,sBAAsB,YAAW,IACxHA,EAAO,UAAY,IAErB,MAAMid,EAAqB,IAClB,KAAKjd,EAAO,OAAO,cAAgB,IAAI,KAAI,EAAG,MAAM,GAAG,EAAE,KAAK,GAAG,CAAC,GAW3E,IAAI+H,EARExJ,GAAMA,EAAG,YAAcA,EAAG,WAAW,cAC3BA,EAAG,WAAW,cAAc0e,EAAkB,CAAE,EAIvDnc,EAAgBvC,EAAI0e,EAAkB,CAAE,EAAE,CAAC,EAIpD,MAAI,CAAClV,GAAa/H,EAAO,OAAO,iBAC9B+H,EAAYpG,GAAc,MAAO3B,EAAO,OAAO,YAAY,EAC3DzB,EAAG,OAAOwJ,CAAS,EACnBjH,EAAgBvC,EAAI,IAAIyB,EAAO,OAAO,UAAU,EAAE,EAAE,QAAQkJ,GAAW,CACrEnB,EAAU,OAAOmB,CAAO,CAC1B,CAAC,GAEH,OAAO,OAAOlJ,EAAQ,CACpB,GAAAzB,EACA,UAAAwJ,EACA,SAAU/H,EAAO,WAAa,CAACzB,EAAG,WAAW,KAAK,WAAaA,EAAG,WAAW,KAAOwJ,EACpF,OAAQ/H,EAAO,UAAYzB,EAAG,WAAW,KAAOA,EAChD,QAAS,GAET,IAAKA,EAAG,IAAI,YAAW,IAAO,OAAS4D,EAAa5D,EAAI,WAAW,IAAM,MACzE,aAAcyB,EAAO,OAAO,YAAc,eAAiBzB,EAAG,IAAI,YAAW,IAAO,OAAS4D,EAAa5D,EAAI,WAAW,IAAM,OAC/H,SAAU4D,EAAa4F,EAAW,SAAS,IAAM,aACvD,CAAK,EACM,EACT,CACA,KAAKxJ,EAAI,CACP,MAAMyB,EAAS,KAGf,GAFIA,EAAO,aACKA,EAAO,MAAMzB,CAAE,IACf,GAAO,OAAOyB,EAC9BA,EAAO,KAAK,YAAY,EAGpBA,EAAO,OAAO,aAChBA,EAAO,cAAa,EAItBA,EAAO,WAAU,EAGjBA,EAAO,WAAU,EAGjBA,EAAO,aAAY,EACfA,EAAO,OAAO,eAChBA,EAAO,cAAa,EAIlBA,EAAO,OAAO,YAAcA,EAAO,SACrCA,EAAO,cAAa,EAIlBA,EAAO,OAAO,MAAQA,EAAO,SAAWA,EAAO,OAAO,QAAQ,QAChEA,EAAO,QAAQA,EAAO,OAAO,aAAeA,EAAO,QAAQ,aAAc,EAAGA,EAAO,OAAO,mBAAoB,GAAO,EAAI,EAEzHA,EAAO,QAAQA,EAAO,OAAO,aAAc,EAAGA,EAAO,OAAO,mBAAoB,GAAO,EAAI,EAIzFA,EAAO,OAAO,MAChBA,EAAO,WAAW,OAAW,EAAI,EAInCA,EAAO,aAAY,EACnB,MAAMkd,EAAe,CAAC,GAAGld,EAAO,GAAG,iBAAiB,kBAAkB,CAAC,EACvE,OAAIA,EAAO,WACTkd,EAAa,KAAK,GAAGld,EAAO,OAAO,iBAAiB,kBAAkB,CAAC,EAEzEkd,EAAa,QAAQpP,GAAW,CAC1BA,EAAQ,SACVD,GAAqB7N,EAAQ8N,CAAO,EAEpCA,EAAQ,iBAAiB,OAAQ2C,GAAK,CACpC5C,GAAqB7N,EAAQyQ,EAAE,MAAM,CACvC,CAAC,CAEL,CAAC,EACDvC,GAAQlO,CAAM,EAGdA,EAAO,YAAc,GACrBkO,GAAQlO,CAAM,EAGdA,EAAO,KAAK,MAAM,EAClBA,EAAO,KAAK,WAAW,EAChBA,CACT,CACA,QAAQmd,EAAgBC,EAAa,CAC/BD,IAAmB,SACrBA,EAAiB,IAEfC,IAAgB,SAClBA,EAAc,IAEhB,MAAMpd,EAAS,KACT,CACJ,OAAA8H,EACA,GAAAvJ,EACA,UAAAwJ,EACA,OAAAO,CACN,EAAQtI,EACJ,OAAI,OAAOA,EAAO,OAAW,KAAeA,EAAO,YAGnDA,EAAO,KAAK,eAAe,EAG3BA,EAAO,YAAc,GAGrBA,EAAO,aAAY,EAGf8H,EAAO,MACT9H,EAAO,YAAW,EAIhBod,IACFpd,EAAO,cAAa,EAChBzB,GAAM,OAAOA,GAAO,UACtBA,EAAG,gBAAgB,OAAO,EAExBwJ,GACFA,EAAU,gBAAgB,OAAO,EAE/BO,GAAUA,EAAO,QACnBA,EAAO,QAAQY,GAAW,CACxBA,EAAQ,UAAU,OAAOpB,EAAO,kBAAmBA,EAAO,uBAAwBA,EAAO,iBAAkBA,EAAO,eAAgBA,EAAO,cAAc,EACvJoB,EAAQ,gBAAgB,OAAO,EAC/BA,EAAQ,gBAAgB,yBAAyB,CACnD,CAAC,GAGLlJ,EAAO,KAAK,SAAS,EAGrB,OAAO,KAAKA,EAAO,eAAe,EAAE,QAAQmc,GAAa,CACvDnc,EAAO,IAAImc,CAAS,CACtB,CAAC,EACGgB,IAAmB,KACjBnd,EAAO,IAAM,OAAOA,EAAO,IAAO,WACpCA,EAAO,GAAG,OAAS,MAErB/B,GAAY+B,CAAM,GAEpBA,EAAO,UAAY,IACZ,IACT,CACA,OAAO,eAAeqd,EAAa,CACjCpgB,EAAO4e,GAAkBwB,CAAW,CACtC,CACA,WAAW,kBAAmB,CAC5B,OAAOxB,EACT,CACA,WAAW,UAAW,CACpB,OAAON,EACT,CACA,OAAO,cAAcU,EAAK,CACnBH,EAAO,UAAU,cAAaA,EAAO,UAAU,YAAc,CAAA,GAClE,MAAMwB,EAAUxB,EAAO,UAAU,YAC7B,OAAOG,GAAQ,YAAcqB,EAAQ,QAAQrB,CAAG,EAAI,GACtDqB,EAAQ,KAAKrB,CAAG,CAEpB,CACA,OAAO,IAAIsB,EAAQ,CACjB,OAAI,MAAM,QAAQA,CAAM,GACtBA,EAAO,QAAQC,GAAK1B,EAAO,cAAc0B,CAAC,CAAC,EACpC1B,IAETA,EAAO,cAAcyB,CAAM,EACpBzB,EACT,CACF,CACA,OAAO,KAAKF,EAAU,EAAE,QAAQ6B,GAAkB,CAChD,OAAO,KAAK7B,GAAW6B,CAAc,CAAC,EAAE,QAAQC,GAAe,CAC7D5B,EAAO,UAAU4B,CAAW,EAAI9B,GAAW6B,CAAc,EAAEC,CAAW,CACxE,CAAC,CACH,CAAC,EACD5B,EAAO,IAAI,CAACjX,GAAQiB,EAAQ,CAAC,ECz2H7B,SAAS6X,GAAS5d,EAAM,CACtB,GAAI,CACF,OAAAC,EACA,aAAA+F,EACA,GAAAjB,EACA,KAAAC,EACA,OAAA+C,CACJ,EAAM/H,EACJC,EAAO,SAAW,CAChB,QAAS,GACT,OAAQ,GACR,SAAU,CACd,EACE+F,EAAa,CACX,SAAU,CACR,QAAS,GACT,MAAO,IACP,kBAAmB,GACnB,qBAAsB,GACtB,gBAAiB,GACjB,iBAAkB,GAClB,kBAAmB,EACzB,CACA,CAAG,EACD,IAAI6X,EACAC,EACAC,EAAqBhW,GAAUA,EAAO,SAAWA,EAAO,SAAS,MAAQ,IACzEiW,EAAuBjW,GAAUA,EAAO,SAAWA,EAAO,SAAS,MAAQ,IAC3EkW,EACAC,EAAoB,IAAI,KAAI,EAAG,QAAO,EACtCC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACJ,SAASC,EAAgBhO,EAAG,CACtB,CAACzQ,GAAUA,EAAO,WAAa,CAACA,EAAO,WACvCyQ,EAAE,SAAWzQ,EAAO,YACxBA,EAAO,UAAU,oBAAoB,gBAAiBye,CAAe,EACjE,EAAAD,GAAwB/N,EAAE,QAAUA,EAAE,OAAO,oBAGjDiO,EAAM,EACR,CACA,MAAMC,EAAe,IAAM,CACzB,GAAI3e,EAAO,WAAa,CAACA,EAAO,SAAS,QAAS,OAC9CA,EAAO,SAAS,OAClBke,EAAY,GACHA,IACTH,EAAuBC,EACvBE,EAAY,IAEd,MAAMU,EAAW5e,EAAO,SAAS,OAASge,EAAmBC,EAAoBF,EAAuB,IAAI,KAAI,EAAG,QAAO,EAC1H/d,EAAO,SAAS,SAAW4e,EAC3B7Z,EAAK,mBAAoB6Z,EAAUA,EAAWd,CAAkB,EAChED,EAAM,sBAAsB,IAAM,CAChCc,EAAY,CACd,CAAC,CACH,EACME,EAAgB,IAAM,CAC1B,IAAIC,EAMJ,OALI9e,EAAO,SAAWA,EAAO,OAAO,QAAQ,QAC1C8e,EAAgB9e,EAAO,OAAO,KAAKkJ,GAAWA,EAAQ,UAAU,SAAS,qBAAqB,CAAC,EAE/F4V,EAAgB9e,EAAO,OAAOA,EAAO,WAAW,EAE7C8e,EACqB,SAASA,EAAc,aAAa,sBAAsB,EAAG,EAAE,EADrE,MAGtB,EACMC,EAAMC,GAAc,CACxB,GAAIhf,EAAO,WAAa,CAACA,EAAO,SAAS,QAAS,OAClD,qBAAqB6d,CAAG,EACxBc,EAAY,EACZ,IAAIvgB,EAAQ,OAAO4gB,EAAe,IAAchf,EAAO,OAAO,SAAS,MAAQgf,EAC/ElB,EAAqB9d,EAAO,OAAO,SAAS,MAC5C+d,EAAuB/d,EAAO,OAAO,SAAS,MAC9C,MAAMif,EAAoBJ,EAAa,EACnC,CAAC,OAAO,MAAMI,CAAiB,GAAKA,EAAoB,GAAK,OAAOD,EAAe,MACrF5gB,EAAQ6gB,EACRnB,EAAqBmB,EACrBlB,EAAuBkB,GAEzBjB,EAAmB5f,EACnB,MAAMgN,EAAQpL,EAAO,OAAO,MACtBkf,GAAU,IAAM,CAChB,CAAClf,GAAUA,EAAO,YAClBA,EAAO,OAAO,SAAS,iBACrB,CAACA,EAAO,aAAeA,EAAO,OAAO,MAAQA,EAAO,OAAO,QAC7DA,EAAO,UAAUoL,EAAO,GAAM,EAAI,EAClCrG,EAAK,UAAU,GACL/E,EAAO,OAAO,SAAS,kBACjCA,EAAO,QAAQA,EAAO,OAAO,OAAS,EAAGoL,EAAO,GAAM,EAAI,EAC1DrG,EAAK,UAAU,GAGb,CAAC/E,EAAO,OAASA,EAAO,OAAO,MAAQA,EAAO,OAAO,QACvDA,EAAO,UAAUoL,EAAO,GAAM,EAAI,EAClCrG,EAAK,UAAU,GACL/E,EAAO,OAAO,SAAS,kBACjCA,EAAO,QAAQ,EAAGoL,EAAO,GAAM,EAAI,EACnCrG,EAAK,UAAU,GAGf/E,EAAO,OAAO,UAChBie,EAAoB,IAAI,KAAI,EAAG,QAAO,EACtC,sBAAsB,IAAM,CAC1Bc,EAAG,CACL,CAAC,GAEL,EACA,OAAI3gB,EAAQ,GACV,aAAawf,CAAO,EACpBA,EAAU,WAAW,IAAM,CACzBsB,GAAO,CACT,EAAG9gB,CAAK,GAER,sBAAsB,IAAM,CAC1B8gB,GAAO,CACT,CAAC,EAII9gB,CACT,EACM+gB,EAAQ,IAAM,CAClBlB,EAAoB,IAAI,KAAI,EAAG,QAAO,EACtCje,EAAO,SAAS,QAAU,GAC1B+e,EAAG,EACHha,EAAK,eAAe,CACtB,EACMqa,EAAO,IAAM,CACjBpf,EAAO,SAAS,QAAU,GAC1B,aAAa4d,CAAO,EACpB,qBAAqBC,CAAG,EACxB9Y,EAAK,cAAc,CACrB,EACMsa,EAAQ,CAAC/O,EAAUgP,IAAU,CACjC,GAAItf,EAAO,WAAa,CAACA,EAAO,SAAS,QAAS,OAClD,aAAa4d,CAAO,EACftN,IACHiO,EAAsB,IAExB,MAAMW,EAAU,IAAM,CACpBna,EAAK,eAAe,EAChB/E,EAAO,OAAO,SAAS,kBACzBA,EAAO,UAAU,iBAAiB,gBAAiBye,CAAe,EAElEC,EAAM,CAEV,EAEA,GADA1e,EAAO,SAAS,OAAS,GACrBsf,EAAO,CACLhB,IACFN,EAAmBhe,EAAO,OAAO,SAAS,OAE5Cse,EAAe,GACfY,EAAO,EACP,MACF,CAEAlB,GADcA,GAAoBhe,EAAO,OAAO,SAAS,QAC7B,IAAI,KAAI,EAAG,QAAO,EAAKie,GAC/C,EAAAje,EAAO,OAASge,EAAmB,GAAK,CAAChe,EAAO,OAAO,QACvDge,EAAmB,IAAGA,EAAmB,GAC7CkB,EAAO,EACT,EACMR,EAAS,IAAM,CACf1e,EAAO,OAASge,EAAmB,GAAK,CAAChe,EAAO,OAAO,MAAQA,EAAO,WAAa,CAACA,EAAO,SAAS,UACxGie,EAAoB,IAAI,KAAI,EAAG,QAAO,EAClCM,GACFA,EAAsB,GACtBQ,EAAIf,CAAgB,GAEpBe,EAAG,EAEL/e,EAAO,SAAS,OAAS,GACzB+E,EAAK,gBAAgB,EACvB,EACMwa,EAAqB,IAAM,CAC/B,GAAIvf,EAAO,WAAa,CAACA,EAAO,SAAS,QAAS,OAClD,MAAM8C,EAAWvF,EAAW,EACxBuF,EAAS,kBAAoB,WAC/Byb,EAAsB,GACtBc,EAAM,EAAI,GAERvc,EAAS,kBAAoB,WAC/B4b,EAAM,CAEV,EACMc,EAAiB/O,GAAK,CACtBA,EAAE,cAAgB,UACtB8N,EAAsB,GACtBC,EAAuB,GACnB,EAAAxe,EAAO,WAAaA,EAAO,SAAS,SACxCqf,EAAM,EAAI,EACZ,EACMI,EAAiBhP,GAAK,CACtBA,EAAE,cAAgB,UACtB+N,EAAuB,GACnBxe,EAAO,SAAS,QAClB0e,EAAM,EAEV,EACMgB,EAAoB,IAAM,CAC1B1f,EAAO,OAAO,SAAS,oBACzBA,EAAO,GAAG,iBAAiB,eAAgBwf,CAAc,EACzDxf,EAAO,GAAG,iBAAiB,eAAgByf,CAAc,EAE7D,EACME,EAAoB,IAAM,CAC1B3f,EAAO,IAAM,OAAOA,EAAO,IAAO,WACpCA,EAAO,GAAG,oBAAoB,eAAgBwf,CAAc,EAC5Dxf,EAAO,GAAG,oBAAoB,eAAgByf,CAAc,EAEhE,EACMG,EAAuB,IAAM,CAChBriB,EAAW,EACnB,iBAAiB,mBAAoBgiB,CAAkB,CAClE,EACMM,EAAuB,IAAM,CAChBtiB,EAAW,EACnB,oBAAoB,mBAAoBgiB,CAAkB,CACrE,EACAza,EAAG,OAAQ,IAAM,CACX9E,EAAO,OAAO,SAAS,UACzB0f,EAAiB,EACjBE,EAAoB,EACpBT,EAAK,EAET,CAAC,EACDra,EAAG,UAAW,IAAM,CAClB6a,EAAiB,EACjBE,EAAoB,EAChB7f,EAAO,SAAS,SAClBof,EAAI,CAER,CAAC,EACDta,EAAG,yBAA0B,IAAM,EAC7BsZ,GAAiBG,IACnBG,EAAM,CAEV,CAAC,EACD5Z,EAAG,6BAA8B,IAAM,CAChC9E,EAAO,OAAO,SAAS,qBAG1Bof,EAAI,EAFJC,EAAM,GAAM,EAAI,CAIpB,CAAC,EACDva,EAAG,wBAAyB,CAACgb,EAAI1U,EAAOkF,IAAa,CAC/CtQ,EAAO,WAAa,CAACA,EAAO,SAAS,UACrCsQ,GAAY,CAACtQ,EAAO,OAAO,SAAS,qBACtCqf,EAAM,GAAM,EAAI,EAEhBD,EAAI,EAER,CAAC,EACDta,EAAG,kBAAmB,IAAM,CAC1B,GAAI,EAAA9E,EAAO,WAAa,CAACA,EAAO,SAAS,SACzC,IAAIA,EAAO,OAAO,SAAS,qBAAsB,CAC/Cof,EAAI,EACJ,MACF,CACAjB,EAAY,GACZC,EAAgB,GAChBG,EAAsB,GACtBF,EAAoB,WAAW,IAAM,CACnCE,EAAsB,GACtBH,EAAgB,GAChBiB,EAAM,EAAI,CACZ,EAAG,GAAG,EACR,CAAC,EACDva,EAAG,WAAY,IAAM,CACnB,GAAI,EAAA9E,EAAO,WAAa,CAACA,EAAO,SAAS,SAAW,CAACme,GAGrD,IAFA,aAAaE,CAAiB,EAC9B,aAAaT,CAAO,EAChB5d,EAAO,OAAO,SAAS,qBAAsB,CAC/Coe,EAAgB,GAChBD,EAAY,GACZ,MACF,CACIC,GAAiBpe,EAAO,OAAO,SAAS0e,EAAM,EAClDN,EAAgB,GAChBD,EAAY,GACd,CAAC,EACDrZ,EAAG,cAAe,IAAM,CAClB9E,EAAO,WAAa,CAACA,EAAO,SAAS,UACzCse,EAAe,GACjB,CAAC,EACD,OAAO,OAAOte,EAAO,SAAU,CAC7B,MAAAmf,EACA,KAAAC,EACA,MAAAC,EACA,OAAAX,CACJ,CAAG,CACH,CCpSO,MAAMqB,GAAiB/e,GAAqC,CAC/D,GAAI,CACA,OAAO,SAAS,cAAcA,CAAQ,CAC1C,MAAQ,CACJ,OAAO,IACX,CACJ,EAKagf,GAAoBhf,GAA0C,CACvE,GAAI,CACA,OAAO,SAAS,iBAAiBA,CAAQ,CAC7C,MAAQ,CACJ,OAAO,SAAS,iBAAiB,EAAE,CACvC,CACJ,EAKaif,GAAkBtiB,GAAmC,CAC9D,GAAI,CACA,OAAO,SAAS,eAAeA,CAAE,CACrC,MAAQ,CACJ,OAAO,IACX,CACJ,EAKagE,EAAgB,CACzBue,EACAha,EAA6B,CAAA,EAC7Bia,EAAY,KACE,CACd,GAAI,CACA,MAAMpf,EAAU,SAAS,cAAcmf,CAAO,EAG9C,SAAW,CAAC7iB,EAAKod,CAAK,IAAK,OAAO,QAAQvU,CAAO,EACzC7I,IAAQ,YACR0D,EAAQ,UAAY,OAAO0Z,CAAK,EACzBpd,IAAQ,KACf0D,EAAQ,GAAK,OAAO0Z,CAAK,EAEzB1Z,EAAQ,aAAa1D,EAAK,OAAOod,CAAK,CAAC,EAI/C,OAAI0F,IACApf,EAAQ,UAAYof,GAGjBpf,CACX,MAAQ,CACJ,OAAO,SAAS,cAAc,KAAK,CACvC,CACJ,EAKaqf,GAAc,CAAC7e,EAAiBe,IAAyB,CAClE,GAAI,CACAf,EAAO,YAAYe,CAAK,CAC5B,MAAQ,CAER,CACJ,EAKa+d,GAAe,CAAC9e,EAAiBe,IAAyB,CACnE,GAAI,CACAf,EAAO,QAAQe,CAAK,CACxB,MAAQ,CAER,CACJ,EAKage,GAAiBvf,GAA2B,CACrD,GAAI,CACAA,EAAQ,OAAA,CACZ,MAAQ,CAER,CACJ,EAKawf,GAAY,CAACxf,EAAsByf,IAA+B,CAC3E,GAAI,CACA,SAAW,CAACpE,EAAU3B,CAAK,IAAK,OAAO,QAAQ+F,CAAM,EACjDzf,EAAQ,MAAM,YAAYqb,EAAU,OAAO3B,CAAK,CAAC,CAEzD,MAAQ,CAER,CACJ,EC9GagG,GAAmB,CAC9B,wBAAyB,EACzB,yBAA0B,CAC5B,EAGaC,EAAgB,CAC3B,OAAQ,CACN,cAAe,GACf,gBAAiB,CAAA,EAEnB,QAAS,CACP,cAAe,GACf,gBAAiB,CAAA,EAEnB,eAAgB,GAClB,EAGaC,GAAiB,CAC5B,sBAAuB,GACvB,kBAAmB,GACrB,EAYaC,EAAkB,CAC7B,aAAc,EACd,YAAa,EACb,gBAAiB,GACjB,iBAAkB,EAClB,iBAAkB,EACpB,EAGaC,GAAS,CACpB,eAAgB,GAChB,oBAAqB,GACvB,EAGaC,GAAe,CAC1B,wBAAyB,qBACzB,sBAAuB,kBACzB,EA2BaC,GAAmB,CAC9B,GAAI,uBACJ,mBAAoB,sBACtB,EC7EaC,GAAiB,IAAe,CACzC,GAAI,CAMA,OALkB,UAAU,UACM,UAC9BP,GAAiB,wBACjBA,GAAiB,wBAAA,IAEM,MAC/B,MAAQ,CACJ,MAAO,EACX,CACJ,EAKaQ,GAAkB,IACvBD,KACO,CACH,aAAcN,EAAc,OAAO,cACnC,cAAeA,EAAc,OAAO,eAAA,EAIrC,CACH,aAAcA,EAAc,QAAQ,cACpC,cAAeA,EAAc,QAAQ,eAAA,EC5BhCQ,EAA4B,CACvC,IAAM,aACN,IAAK,CACH,YAAaH,GAAiB,GAC9B,kBAAmBA,GAAiB,kBAAA,EAEtC,SAAU,CACR,cAAeL,EAAc,eAC7B,cAAeG,GAAO,eACtB,kBAAmBA,GAAO,oBAC1B,OAAQ,CACN,aAAcH,EAAc,OAAO,cACnC,cAAeA,EAAc,OAAO,eAAA,EAEtC,QAAS,CACP,aAAcA,EAAc,QAAQ,cACpC,cAAeA,EAAc,QAAQ,eAAA,CACvC,EAEF,GAAI,CACF,eAAgBI,GAAa,wBAC7B,aAAcA,GAAa,qBAAA,CAE/B,ECnBO,MAAMK,EAAgB,CAKzB,sBAA6B,CACzB,GAAI,CACA,GAAIC,GAAwBF,EAAc,GAAG,cAAc,EACvD,OAIJ,MAAMG,EAAWC,GAA0B,UAAU,EACjDD,EAAS,OAAST,EAAgB,aAClC,KAAK,gBAAgBS,CAAQ,EAG7B,KAAK,0BAAA,CAEb,MAAQ,CAER,CACJ,CAKQ,2BAAkC,CAGtC,IAAIE,EAAW,EAEf,MAAMC,EAAkB,IAAY,CAChCD,GAAYX,EAAgB,iBAC5B,MAAMS,EAAWC,GAA0B,UAAU,EAEjDD,EAAS,OAAST,EAAgB,aAElC,KAAK,gBAAgBS,CAAQ,EACtBE,EAAW,IAElB,WAAWC,EAAiB,GAAe,CAGnD,EAEAA,EAAA,CACJ,CAKQ,gBAAgBH,EAAqC,CACzD,GAAI,CACA,MAAMI,EAAY,KAAK,yBAAA,EACvB,GAAI,CAACA,EACD,OAGJ,MAAMzhB,EAAS,KAAK,gBAAgByhB,CAAS,EAC7C,GAAI,CAACzhB,EACD,OAGJ,MAAM0hB,EAAU,KAAK,uBAAuB1hB,CAAM,EAClD,GAAI,CAAC0hB,EACD,OAGJ,KAAK,kBAAkBA,EAASL,CAAQ,EACxC,KAAK,mBAAmBI,CAAS,EACjC,KAAK,oBAAoBA,CAAS,EAClC,KAAK,uBAAA,EACL,KAAK,kBAAA,EACL,KAAK,oBAAA,EAGL,KAAK,wBAAA,CACT,MAAQ,CAER,CACJ,CAKQ,0BAAwC,CAC5C,MAAMA,EAAYE,EAAuB,MAAO,CAC5C,UAAW,qBACX,GAAIT,EAAc,GAAG,cAAA,CACxB,EAEKU,EAAgBD,EAAuB,MAAO,CAChD,UAAW,uBAAA,CACd,EAEDE,OAAAA,GAAqBJ,EAAWG,CAAa,EACtCH,CACX,CAKQ,gBAAgBA,EAAqC,CACzD,MAAMG,EAAgBH,EAAU,cAAc,wBAAwB,EAChEzhB,EAAS2hB,EAAuB,MAAO,CACzC,UAAW,kBAAA,CACd,EAED,OAAIC,GACAC,GAAqBD,EAAe5hB,CAAM,EAGvCA,CACX,CAKQ,uBAAuBA,EAAkC,CAC7D,MAAM0hB,EAAUC,EAAuB,MAAO,CAC1C,UAAW,iBACX,GAAIT,EAAc,GAAG,YAAA,CACxB,EACDW,OAAAA,GAAqB7hB,EAAQ0hB,CAAO,EAC7BA,CACX,CAKQ,kBAAkBA,EAAsBL,EAAqC,CACjF,MAAMS,EAAWd,GAAA,EAEjB,UAAWpf,KAAOyf,EAAU,CACxB,MAAMU,EAAangB,EACbogB,EAAU,KAAK,eAAeD,CAAU,EAE9C,GAAIC,EAAS,CACT,MAAM1Y,EAAQ,KAAK,eAAe0Y,EAASF,CAAQ,EACnDD,GAAqBH,EAASpY,CAAK,CACvC,CACJ,CACJ,CAKQ,eAAe1H,EAAkC,CACrD,MAAMqgB,EAAcrgB,EAAI,cAAc,GAAG,EACnCsgB,EAActgB,EAAI,cAAc,eAAe,EAC/CugB,EAAcvgB,EAAI,cAAc,sBAAsB,EAE5D,GAAI,CAACqgB,GAAe,CAACC,EACjB,OAIJ,MAAME,EAAkB,KAAK,sBAAsBH,EAAY,KAAMrgB,CAAG,EAClEygB,EAAgB,WAAW,iBAAiBzgB,CAAG,EAC/C0gB,EAAaF,GAAmBC,EAAc,WAEpD,IAAIE,EAAc,GACdC,EAAY,GAChB,OAAIL,IACAI,EAAcJ,EAAY,aAAe,GACzCK,EAAY,WAAW,iBAAiBL,CAAW,EAAE,OAGlD,CACH,IAAKF,EAAY,KACjB,WAAAK,EACA,KAAMJ,EAAY,aAAe,GACjC,UAAW,WAAW,iBAAiBA,CAAW,EAAE,MACpD,YAAAK,EACA,UAAAC,CAAA,CAER,CAKQ,sBAAsBC,EAAgBV,EAAwC,CAClF,GAAI,CAGA,MAAMW,EADM,IAAI,IAAID,EAAQ,WAAW,SAAS,MAAM,EACpC,SAAS,MAAM,GAAG,EAAE,OAAO,OAAO,EAC9CE,EAASD,EAAM,QAAQ,GAAG,EAC1BE,EAAYF,EAAM,QAAQ,MAAM,EAEtC,IAAIG,EAAO,GAUX,GARIF,IAAW/B,EAAgB,iBAAmB8B,EAAMC,EAAS/B,EAAgB,gBAAgB,EAC7FiC,EAAOH,EAAMC,EAAS/B,EAAgB,gBAAgB,EAC/CgC,IAAchC,EAAgB,iBAAmB8B,EAAME,EAAYhC,EAAgB,gBAAgB,EAC1GiC,EAAOH,EAAME,EAAYhC,EAAgB,gBAAgB,EAClD8B,EAAM,OAAS9B,EAAgB,eACtCiC,EAAOH,EAAMA,EAAM,OAAS9B,EAAgB,gBAAgB,GAG5D,CAACiC,EACD,OAIJ,MAAMC,EAAQ,KAAK,0BAA0BD,CAAI,EAEjD,OAAIC,EACO,OAAOA,CAAK,IAGvB,MACJ,MAAQ,CAEJ,MAAMC,EAAmBhB,EAAW,MAAM,WAC1C,OAAIgB,GAAoBA,EAAiB,SAAS,MAAM,EAC7CA,EAEX,MACJ,CACJ,CAKQ,0BAA0BF,EAA6B,CAC3D,GAAI,CAGA,MAAMG,EADOC,EAAI,MAAM,IAAI,MAAM,EACX,KAAMC,GAAqB,CAC7C,MAAMC,EAAYD,EAClB,IAAIE,EAAU,GAEd,OAAI,OAAOD,EAAU,MAAS,WAC1BC,EAAUD,EAAU,KAAA,EACbA,EAAU,WAAa,OAAOA,EAAU,WAAc,aAC7DC,EAAUD,EAAU,UAAU,MAAM,GAGjCC,IAAYP,CACvB,CAAC,EAED,GAAI,CAACG,EACD,OAIJ,MAAMG,EAAYH,EAElB,GAAIG,EAAU,WAAa,OAAOA,EAAU,WAAc,WAAY,CAClE,MAAML,EAAQK,EAAU,UAAU,yBAAyB,EAC3D,GAAIL,EACA,OAAOA,CAEf,CAEA,MACJ,MAAQ,CACJ,MACJ,CACJ,CAKQ,eAAed,EAAkBF,EAAgC,CACrE,MAAMxY,EAAQqY,EAAuB,MAAO,CACxC,UAAW,+BAAA,CACd,EAED,IAAI0B,EAAa,yBACbvB,IACAuB,EAAa,iCAGjB,MAAMC,EAAkB,cAActB,EAAQ,UAAU,oFAGlDuB,EAAqB,KAAK,mBAAmBvB,EAAQ,UAAU,EAGrE,IAAIwB,EAAc,GAClB,OAAKD,IACDC,EAAc;AAAA,gEACsCxB,EAAQ,SAAS;AAAA,kBAC/DA,EAAQ,IAAI;AAAA;AAAA,WAKtB1Y,EAAM,UAAY;AAAA,uBACH0Y,EAAQ,GAAG;AAAA,8BACJqB,CAAU,YAAYC,CAAe;AAAA,sBAC7CE,CAAW;AAAA;AAAA;AAAA,UAKlBla,CACX,CAKQ,mBAAmBgZ,EAA6B,CACpD,OAAKA,EAKEA,EAAW,SAAS,MAAM,GAAK,CAACA,EAAW,SAAS,OAAO,EAJvD,EAKf,CAKQ,mBAAmBb,EAA8B,CACrD,MAAMgC,EAAiBC,GAAuB,uCAAuC,EACjFD,GACAE,GAAsBF,EAAgBhC,CAAS,CAEvD,CAKQ,oBAAoBA,EAA8B,CACtD,MAAMG,EAAgBH,EAAU,cAAc,wBAAwB,EACtE,GAAIG,EAAe,CACf,MAAMgC,EAAejC,EAAuB,MAAO,CAC/C,UAAW,kBAAA,EACZ,yCAAyC,EAE5CgC,GAAsB/B,EAAegC,CAAY,EAEjD,MAAMC,EAAgB,KAAK,wBAAA,EAC3BjC,EAAc,mBAAmB,YAAaiC,CAAa,CAC/D,CACJ,CAKQ,yBAAkC,CACtC,MAAMC,EAAc5C,EAAc,IAAI,YAgChC2C,EA7BkB,CACpB,CACI,OAAQ,GAAGC,CAAW,iBACtB,QAAS,GAAGA,CAAW,kBACvB,YAAa,EAAA,EAEjB,CACI,OAAQ,GAAGA,CAAW,qBACtB,QAAS,GAAGA,CAAW,sBACvB,YAAa,EAAA,EAEjB,CACI,OAAQ,GAAGA,CAAW,oBACtB,QAAS,GAAGA,CAAW,qBACvB,YAAa,EAAA,EAEjB,CACI,OAAQ,GAAGA,CAAW,oBACtB,QAAS,GAAGA,CAAW,qBACvB,YAAa,EAAA,EAEjB,CACI,OAAQ,GAAGA,CAAW,sBACtB,QAAS,GAAGA,CAAW,uBACvB,YAAa,EAAA,CACjB,EAKC,IAAI,CAAC1gB,EAAUgE,IAAU,CACtB,MAAM2c,EAAMd,EAAI,MAAM,UAAU7f,EAAS,MAAM,GAAK,GAC9C4gB,EAAUf,EAAI,MAAM,UAAU7f,EAAS,OAAO,GAAKA,EAAS,YAGlE,GAAI,CAAC2gB,EAAI,KAAA,GAAU,CAACC,EAAQ,OACxB,MAAO,GAGX,IAAIC,EAAc,GAClB,OAAI7c,EAAQwZ,EAAgB,cACxBqD,EAAc,sBAEX,8BAA8BF,CAAG,oCAAoCE,CAAW,UAAUD,CAAO,IAC5G,CAAC,EACA,OAAOE,GAAUA,IAAW,EAAE,EAC9B,KAAK,EAAE,EAGZ,OAAKL,EAIE;AAAA;AAAA;AAAA;AAAA,0BAIWA,CAAa;AAAA;AAAA;AAAA;AAAA,UAPpB,EAYf,CAKQ,wBAA+B,CACnC,MAAMxC,EAAWqC,GAAuB,WAAW,EAC/CrC,GACA8C,GAAuB9C,CAAQ,CAEvC,CAKQ,mBAA0B,CAC9B,GAAIL,KAAkB,CAClB,MAAMiC,EAAM7B,GAAwB,KAAK,EACnCgD,EAAaV,GAAuB,cAAc,EAEpDT,GACAoB,GAAmBpB,EAAK,CAAE,aAAc,SAAU,EAGlDmB,GACAC,GAAmBD,EAAY,CAC3B,aAAc,OACd,WAAc,EAAA,CACjB,CAET,CACJ,CAKQ,qBAA4B,CAChC,GAAI,CACA,MAAME,EAASrD,GAAA,EACTsD,EAAiB,IAAIzI,EAAO,aAAc,CAC5C,KAAM,GACN,aAAcwI,EAAO,aACrB,cAAeA,EAAO,cACtB,SAAU,CACN,MAAO5D,EAAc,eACrB,qBAAsB,EAAA,EAE1B,QAAS,CAAC/C,EAAQ,CAAA,CACrB,CAKL,MAAQ,CAER,CACJ,CAKQ,yBAAgC,CACpC,GAAI,CAEA,MAAM5W,EAAQ,IAAI,YAAY,oBAAqB,CAC/C,OAAQ,CACJ,YAAama,EAAc,IAAI,YAC/B,WAAY,QAAA,CAChB,CACH,EACD,SAAS,cAAcna,CAAK,CAChC,MAAQ,CAER,CACJ,CACJ,CC1eO,MAAMyd,CAAa,CAKd,aAAc,CAHtB,KAAQ,SAA4B,CAAA,EACpC,KAAQ,cAAgB,EAED,CAKvB,OAAc,aAA4B,CACtC,OAAKA,EAAa,WACdA,EAAa,SAAW,IAAIA,GAEzBA,EAAa,QACxB,CAKO,YAAsB,CACzB,GAAI,CACA,OAAI,KAAK,gBAKT,KAAK,yBAAA,EACL,KAAK,cAAgB,IACd,EACX,MAAQ,CACJ,MAAO,EACX,CACJ,CAKO,WAAcC,EAAald,EAAgC,CAC9D,GAAI,CACA,OAAOkd,EAAA,CACX,OAASC,EAAO,CACZ,KAAK,SAASA,EAAgBnd,CAAO,EACrC,MACJ,CACJ,CAKA,MAAa,YAAekd,EAAsBld,EAAyC,CACvF,GAAI,CACA,OAAO,MAAMkd,EAAA,CACjB,OAASC,EAAO,CACZ,KAAK,SAASA,EAAgBnd,CAAO,EACrC,MACJ,CACJ,CAKQ,SAASmd,EAAcnd,EAAuB,CAClD,GAAI,CACA,MAAMod,EAAuB,CACzB,cAAe,KACf,MAAAD,EACA,QAAAnd,CAAA,EAGJ,KAAK,SAAS,KAAKod,CAAK,EAGpB,KAAK,SAAS,OAAShE,GAAe,uBACtC,KAAK,SAAS,MAAA,CAOtB,MAAQ,CAER,CACJ,CAKQ,0BAAiC,CACrC,GAAI,CAEA,OAAO,iBAAiB,qBAAuB5Z,GAAU,CACrD,KAAK,SACD,IAAI,MAAM,OAAOA,EAAM,MAAM,CAAC,EAC9B,6BAAA,CAER,CAAC,CACL,MAAQ,CAER,CACJ,CAKO,aAA+B,CAClC,MAAO,CAAC,GAAG,KAAK,QAAQ,CAC5B,CAKO,eAAsB,CACzB,KAAK,SAAW,CAAA,CACpB,CACJ,CCpHO,MAAM6d,CAAc,CAGf,aAAc,CAAC,CAKvB,OAAc,aAA6B,CACvC,OAAKA,EAAc,WACfA,EAAc,SAAW,IAAIA,GAE1BA,EAAc,QACzB,CAKO,YAAsB,CACzB,GAAI,CAEA,OADqB3B,EAAI,QAAQ,IAAI,WAAW,IACxB,MAC5B,MAAQ,CAEJ,GAAI,CACA,OAAO,OAAO,SAAS,SAAS,SAAS,OAAO,CACpD,MAAQ,CACJ,MAAO,EACX,CACJ,CACJ,CAKO,WAAY,CACf,OAAO/B,CACX,CAKO,cAAwB,CAC3B,GAAI,CAEA,MAAM2D,EAAkB,CAAC,OAAQ,WAAY,UAAW,UAAW,WAAW,EAE9E,UAAWzhB,KAAYyhB,EAAiB,CACpC,MAAMd,EAAMd,EAAI,MAAM,UAAU,GAAG/B,EAAc,IAAI,WAAW,UAAU9d,CAAQ,KAAK,EACjF0hB,EAAO7B,EAAI,MAAM,UAAU,GAAG/B,EAAc,IAAI,WAAW,UAAU9d,CAAQ,MAAM,EAEzF,GAAI2gB,GAAOe,EACP,MAAO,EAEf,CAEA,MAAO,EACX,MAAQ,CACJ,MAAO,EACX,CACJ,CACJ,CCvDA7B,EAAI,aAAa,IAAI/B,EAAc,IAAI,YAAa,IAAM,CACtD,MAAM6D,EAAeP,EAAa,YAAA,EAC5BQ,EAAgBJ,EAAc,YAAA,EAGpC,GAAI,CAACG,EAAa,aACd,OAGJ,MAAME,EAAkB,IAAI9D,GAG5BlkB,GAAAA,OAAOioB,GAAS,UAAW,WAAY,SAAmCC,EAAiB,CACvFJ,EAAa,WAAW,IAAM,CACtBC,EAAc,cAGd,WAAW,IAAM,CACbC,EAAgB,qBAAA,CACpB,EAAG,GAAe,CAE1B,EAAG,6BAA6B,CACpC,CAAC,EAEDhoB,GAAAA,OAAOioB,GAAS,UAAW,WAAY,SAAmCC,EAAiB,CACvFJ,EAAa,WAAW,IAAM,CAErB,SAAS,eAAe7D,EAAc,GAAG,cAAc,GAExD,WAAW,IAAM,CACb+D,EAAgB,qBAAA,CACpB,EAAG,GAAe,CAE1B,EAAG,6BAA6B,CACpC,CAAC,CACL,CAAC", "x_google_ignoreList": [0, 1, 2, 3]}