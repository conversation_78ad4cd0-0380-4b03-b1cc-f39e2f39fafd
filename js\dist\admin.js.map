{"version": 3, "file": "admin.js", "sources": ["../src/admin/settings-generator.ts", "../src/admin/index.ts"], "sourcesContent": ["import app from 'flarum/admin/app';\nimport type { ExtensionData } from '../common/config/types';\n\n/**\n * Settings generator utility for TagTiles admin interface\n */\nexport class SettingsGenerator {\n    private extensionId: string;\n    private extensionData: ExtensionData;\n\n    constructor(extensionId: string) {\n        this.extensionId = extensionId;\n        this.extensionData = app.extensionData.for(extensionId) as ExtensionData;\n    }\n\n    /**\n     * Register social media settings\n     */\n    registerSocialMediaSettings(): this {\n        const socialPlatforms = ['Kick', 'Facebook', 'Twitter', 'YouTube', 'Instagram'];\n\n        for (const platform of socialPlatforms) {\n            // URL setting\n            this.extensionData.registerSetting({\n                setting: `${this.extensionId}.Social${platform}Url`,\n                type: 'url',\n                label: String(app.translator.trans(`wusong8899-tag-tiles.admin.Social${platform}Url`)),\n                help: String(app.translator.trans(`wusong8899-tag-tiles.admin.Social${platform}UrlHelp`)),\n            });\n\n            // Icon setting\n            this.extensionData.registerSetting({\n                setting: `${this.extensionId}.Social${platform}Icon`,\n                type: 'text',\n                label: String(app.translator.trans(`wusong8899-tag-tiles.admin.Social${platform}Icon`)),\n                help: String(app.translator.trans(`wusong8899-tag-tiles.admin.Social${platform}IconHelp`)),\n            });\n        }\n\n        return this;\n    }\n\n    /**\n     * Register all settings for the extension\n     */\n    registerAllSettings(): this {\n        return this.registerSocialMediaSettings();\n    }\n}\n\n/**\n * Configuration constants\n */\nexport const EXTENSION_CONFIG = {\n    EXTENSION_ID: 'wusong8899-tag-tiles',\n};\n\n/**\n * Initialize admin settings\n * @param extensionId - The extension identifier\n */\nexport const initializeAdminSettings = (\n    extensionId = EXTENSION_CONFIG.EXTENSION_ID\n): void => {\n    const generator = new SettingsGenerator(extensionId);\n    generator.registerAllSettings();\n};\n", "import app from 'flarum/admin/app';\nimport { initializeAdminSettings } from './settings-generator';\n\napp.initializers.add('wusong8899-tag-tiles', (): void => {\n    initializeAdminSettings();\n});\n"], "names": ["SettingsGenerator", "extensionId", "app", "socialPlatforms", "platform", "EXTENSION_CONFIG", "initializeAdminSettings"], "mappings": "0BAMO,MAAMA,CAAkB,CAI3B,YAAYC,EAAqB,CAC7B,KAAK,YAAcA,EACnB,KAAK,cAAgBC,EAAI,cAAc,IAAID,CAAW,CAC1D,CAKA,6BAAoC,CAChC,MAAME,EAAkB,CAAC,OAAQ,WAAY,UAAW,UAAW,WAAW,EAE9E,UAAWC,KAAYD,EAEnB,KAAK,cAAc,gBAAgB,CAC/B,QAAS,GAAG,KAAK,WAAW,UAAUC,CAAQ,MAC9C,KAAM,MACN,MAAO,OAAOF,EAAI,WAAW,MAAM,oCAAoCE,CAAQ,KAAK,CAAC,EACrF,KAAM,OAAOF,EAAI,WAAW,MAAM,oCAAoCE,CAAQ,SAAS,CAAC,CAAA,CAC3F,EAGD,KAAK,cAAc,gBAAgB,CAC/B,QAAS,GAAG,KAAK,WAAW,UAAUA,CAAQ,OAC9C,KAAM,OACN,MAAO,OAAOF,EAAI,WAAW,MAAM,oCAAoCE,CAAQ,MAAM,CAAC,EACtF,KAAM,OAAOF,EAAI,WAAW,MAAM,oCAAoCE,CAAQ,UAAU,CAAC,CAAA,CAC5F,EAGL,OAAO,IACX,CAKA,qBAA4B,CACxB,OAAO,KAAK,4BAAA,CAChB,CACJ,CAKO,MAAMC,EAAmB,CAC5B,aAAc,sBAClB,EAMaC,EAA0B,CACnCL,EAAcI,EAAiB,eACxB,CACW,IAAIL,EAAkBC,CAAW,EACzC,oBAAA,CACd,EC/DAC,EAAI,aAAa,IAAI,uBAAwB,IAAY,CACrDI,EAAA,CACJ,CAAC"}